import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { AddReportAutomationComponent } from '../add-report-automation/add-report-automation.component';
import { DeleteReportAutomation } from 'src/app/reducers/reports/reports.actions';

@Component({
  selector: 'report-automation-actions',
  templateUrl: './report-automation-actions.component.html',
})
export class ReportAutomationActionsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editReportAutomation(agent: any): void {
    const initialState: any = {
      selectedReportAutomation: agent
    };
    this.modalRef = this.modalService.show(AddReportAutomationComponent, {
      class: 'right-modal modal-350 ip-modal-unset',
      initialState,
    });
  }

  deleteReportAutomation(report: any) {
    let initialState: any = {
      type: 'reportAutomation',
      data: {
        fieldType: 'Delete',
        heading: `Deleting the report?`,
        message: `<span> Are you sure you want to delete the selected report - 
          <b class='text-dark' title='${report?.displayName}'>
            ${report?.displayName?.length > 20 ? report?.displayName.slice(0, 20) + '...' : report?.displayName}
          </b> 🚫</span>`,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteReportAutomation(report?.id));
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}