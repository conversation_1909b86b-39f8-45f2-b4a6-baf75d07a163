import { Action } from '@ngrx/store';
import { GlobalSettings } from 'src/app/core/interfaces/global-settings';

export enum GlobalSettingsActionTypes {
  FETCH_GLOBAL_SETTINGS = '[GLOBAL_SETTINGS] Fetch Global Settings',
  FETCH_GLOBAL_SETTINGS_SUCCESS = '[GLOBAL_SETTINGS] Fetch Global Settings Success',
  FETCH_GLOBAL_SETTINGS_ANONYMOUS = '[GLOBAL_SETTINGS] Fetch Global Settings Anonymous',
  FETCH_GLOBAL_SETTINGS_ANONYMOUS_SUCCESS = '[GLOBAL_SETTINGS] Fetch Global Settings Anonymous Success',
  FETCH_DUPLICATE_SETTINGS = '[GLOBAL_SETTINGS] Fetch Duplicate Feature Settings',
  FETCH_DUPLICATE_SETTINGS_SUCCESS = '[GLOBAL_SETTINGS] Fetch Duplicate Feature Settings Success',
  FETCH_DUPLICATE_SETTINGS_ANONYMOUS = '[GLOBAL_SETTINGS] Fetch Duplicate Feature Settings Anonymous',
  FETCH_DUPLICATE_SETTINGS_ANONYMOUS_SUCCESS = '[GLOBAL_SETTINGS] Fetch Duplicate Feature Settings Anonymous Success',
  UPDATE_GLOBAL_SETTINGS = '[GLOBAL_SETTINGS] Update Global Settings',
  UPDATE_ALLOW_DUPLICATES_SETTINGS = '[GLOBAL_SETTINGS] Update Allow Duplicates Settings',
  UPDATE_MANDATORY_NOTES_SETTINGS = '[GLOBAL_SETTINGS] Update Mandatory Notes Settings',
  FETCH_CURRENCY_LIST = '[GLOBAL_SETTINGS] Fetch Currency List',
  FETCH_CURRENCY_LIST_SUCCESS = '[GLOBAL_SETTINGS] Fetch Currency List Success',
  UPDATE_MANDATORY_PROJECTS_SETTING = '[GLOBAL_SETTINGS] Update Mandatory Projects Settings',
  UPDATE_MANDATORY_PROPERTY_SETTING = '[GLOBAL_SETTINGS] Update Mandatory Property Settings',
  UPDATE_OTP_SETTINGS = '[GLOBAL_SETTINGS] Update OTP Settings',
  OTP_GLOBAL_SETTINGS = '[LOGIN] Fetch OTP Global Settings',
  OTP_GLOBAL_SETTINGS_SUCCESS = '[LOGIN] Fetch OTP Global Settings Success',
  FETCH_TEMP_VARIABLES = '[GLOBAL_SETTINGS] Fetch Temp Variables',
  FETCH_TEMP_VARIABLES_SUCCESS = '[GLOBAL_SETTINGS] Fetch Temp Variables Success',
  LISTING_UPDATE = '[GLOBAL_SETTINGS] Update Listing Settings',
  // All sources actions
  FETCH_ALL_SOURCES = '[GLOBAL_SETTINGS] Fetch All Sources',
  FETCH_ALL_SOURCES_SUCCESS = '[GLOBAL_SETTINGS] Fetch All Sources Success',
}
export class FetchGlobalSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS;
  constructor() { }
}
export class FetchGlobalSettingsSuccess implements Action {
  readonly type: string =
    GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_SUCCESS;
  constructor(public response: GlobalSettings) { }
}
export class FetchGlobalSettingsAnonymous implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_ANONYMOUS;
  constructor() { }
}
export class FetchGlobalSettingsAnonymousSuccess implements Action {
  readonly type: string =
    GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_ANONYMOUS_SUCCESS;
  constructor(public response: GlobalSettings) { }
}

export class UpdateGlobalSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.UPDATE_GLOBAL_SETTINGS;
  constructor(public payload: any) { }
}

export class UpdateAllowDuplicatesSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.UPDATE_ALLOW_DUPLICATES_SETTINGS;
  constructor(public payload: any) { }
}

export class UpdateMandatoryNotesSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.UPDATE_MANDATORY_NOTES_SETTINGS;
  constructor(public payload: any) { }
}

export class UpdateMandatoryProjectsSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.UPDATE_MANDATORY_PROJECTS_SETTING;
  constructor(public payload: any) { }
}

export class UpdateMandatoryPropertySettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.UPDATE_MANDATORY_PROPERTY_SETTING;
  constructor(public payload: any) { }
}

export class FetchCurrencyList implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_CURRENCY_LIST;
  constructor() { }
}

export class FetchCurrencyListSuccess implements Action {
  readonly type: string =
    GlobalSettingsActionTypes.FETCH_CURRENCY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class UpdateOTPSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.UPDATE_OTP_SETTINGS;
  constructor(public payload: any) { }
}
export class FetchOTPGlobalSettings implements Action {
  readonly type: string = GlobalSettingsActionTypes.OTP_GLOBAL_SETTINGS;
  constructor() { }
}
export class FetchOTPGlobalSettingsSuccess implements Action {
  readonly type: string = GlobalSettingsActionTypes.OTP_GLOBAL_SETTINGS_SUCCESS;
  constructor(public resp: any) { }
}

export class FetchTempVariables implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_TEMP_VARIABLES;
  constructor() { }
}
export class FetchTempVariablesSuccess implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_TEMP_VARIABLES_SUCCESS;
  constructor(public resp: any) { }
}

export class UpdateListing implements Action {
  readonly type: string = GlobalSettingsActionTypes.LISTING_UPDATE;
  constructor(public payload: any) { }
}

// All sources actions
export class FetchAllSources implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_ALL_SOURCES;
  constructor() { }
}

export class FetchAllSourcesSuccess implements Action {
  readonly type: string = GlobalSettingsActionTypes.FETCH_ALL_SOURCES_SUCCESS;
  constructor(public payload: any) { }
}