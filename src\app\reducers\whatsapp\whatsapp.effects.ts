import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { select, Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { firstValueFrom, from, of } from 'rxjs';
import { catchError, map, skipWhile, switchMap, withLatestFrom } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AddWhatsappTemplate,
  DeleteWhatsappTemplate,
  Fetch24HrValidation,
  Fetch24HrValidationSuccess,
  FetchConversation,
  FetchConversationSuccess,
  FetchWhatsappChildList,
  FetchWhatsappChildListSuccess,
  FetchWhatsappList,
  FetchWhatsappListSuccess,
  FetchWhatsappTemplate,
  FetchWhatsappTemplateSuccess,
  UpdateWhatsappTemplate,
  WhatsappActionTypes,
} from 'src/app/reducers/whatsapp/whatsapp.actions';
import { WhatsappService } from 'src/app/services/controllers/whatsapp.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { getFetchModifiedDatesList } from '../master-data/master-data.reducer';
import { handleCachedData } from 'src/app/core/utils/common.util';

@Injectable()
export class WhatsappEffects {
  getWhatsappTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.FETCH_WHATSAPP_TEMPLATE),
      map((action: FetchWhatsappTemplate) => action),
      switchMap((data: any) =>
        from(
          handleCachedData(
            'whatsappTemplates',
            'whatsappTemplatesList',
            async (): Promise<any> => {
              const value: any = await firstValueFrom(
                this.store.select(getFetchModifiedDatesList).pipe(
                  skipWhile((v: any) => v.isLoading)
                )
              );
              return value?.data?.WhatsAppTemplate || null;
            },
            async () => {
              const resp: any = await firstValueFrom(this.api.getWAList());
              if (resp?.succeeded) {
                return resp.data || {};
              } else {
                this._notificationService.error('Failed to fetch whatsapp templates');
                return {};
              }
            },
            (data: any, lastModified: string | null) => ({
              id: 'whatsappTemplatesList',
              items: data || [],
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || [] : data || [];
            }
          )
        ).pipe(
          map((list: any) => new FetchWhatsappTemplateSuccess(list)),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );


  addWhatsappTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.ADD_WHATSAPP_TEMPLATE),
      switchMap((action: AddWhatsappTemplate) => {
        return this.api.add(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Template added successfully.`);
              return new FetchWhatsappTemplate();
            }
            return new FetchWhatsappTemplateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateWhatsappTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.UPDATE_WHATSAPP_TEMPLATE),
      switchMap((action: UpdateWhatsappTemplate) => {
        return this.api.updateWhatsappTemplate(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Template updated successfully.`
              );
              return new FetchWhatsappTemplate();
            }
            return new FetchWhatsappTemplateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteWhatsappTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.DELETE_WHATSAPP_TEMPLATE),
      map((action: DeleteWhatsappTemplate) => action),
      switchMap((action: DeleteWhatsappTemplate) => {
        return this.api.delete(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Template deleted successfully.`
              );
              return new FetchWhatsappTemplate();
            }
            return new FetchWhatsappTemplateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  get24HrValidation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.FETCH_24HR_VALIDATION),
      switchMap((action: Fetch24HrValidation) => {
        return this.api.get24HrValidation(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new Fetch24HrValidationSuccess(resp.data);
            }
            return new Fetch24HrValidationSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getConversation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.FETCH_CONVERSATION),
      switchMap((action: FetchConversation) => {
        return this.api.getConversation(action.payload).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchConversationSuccess(resp.items);
            }
            return new FetchConversationSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getWhatsAppList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.FETCH_WHATSAPP_LIST),
      map((action: FetchWhatsappList) => action),
      withLatestFrom(
        this.store.pipe(select((state) => state?.whatsapp?.filtersPayload))
      ),
      switchMap(([action, filtersFromState]: any) => {
        let filters = Object.keys(action.filtersPayload).length
          ? action.filtersPayload
          : filtersFromState;
        const filterPayload: any = { ...filters };

        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchWhatsappListSuccess(resp);
            }
            return new FetchWhatsappListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getWhatsAppChildList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappActionTypes.FETCH_WHATSAPP_CHILD_LIST),
      switchMap((action: FetchWhatsappChildList) => {
        return this.api.getWAChildList(action.payload).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchWhatsappChildListSuccess(resp.items);
            }
            return new FetchWhatsappChildListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: WhatsappService,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    private commonService: CommonService
  ) { }
}
