import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { EMPTY, firstValueFrom, from, of } from 'rxjs';
import { catchError, exhaustMap, map, skipWhile, switchMap } from 'rxjs/operators';
import { ManageSourceIndexDBService } from 'src/app/services/shared/managesource.indexdb.service';

import { OnError } from 'src/app/app.actions';
import { GlobalSettings } from 'src/app/core/interfaces/global-settings';
import {
  FetchAllSourcesSuccess,
  FetchCurrencyList,
  FetchCurrencyListSuccess,
  FetchGlobalSettings,
  FetchGlobalSettingsAnonymous,
  FetchGlobalSettingsAnonymousSuccess,
  FetchOTPGlobalSettings,
  FetchOTPGlobalSettingsSuccess,
  FetchTempVariables,
  FetchTempVariablesSuccess,
  GlobalSettingsActionTypes,
  UpdateAllowDuplicatesSettings,
  UpdateGlobalSettings,
  UpdateListing,
  UpdateMandatoryNotesSettings,
  UpdateMandatoryProjectsSettings,
  UpdateMandatoryPropertySettings,
  UpdateOTPSettings
} from 'src/app/reducers/global-settings/global-settings.actions';
import { GlobalSettingsService } from 'src/app/services/controllers/global-settings.service';
import { handleCachedData } from 'src/app/core/utils/common.util';
import { getFetchModifiedDatesList } from '../master-data/master-data.reducer';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchModifiedDatesList } from '../master-data/master-data.actions';

@Injectable()
export class GlobalSettingsEffects {
  getGlobalSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS),
      map((action: FetchGlobalSettings) => action),
      switchMap((action: FetchGlobalSettings) => {
        return this.api.getList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGlobalSettingsAnonymousSuccess(resp.data);
            }
            return new FetchGlobalSettingsAnonymousSuccess({} as GlobalSettings);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGlobalSettingsAnonymous$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_ANONYMOUS),
      exhaustMap(() => {
        return from(
          handleCachedData(
            'globalSettingsData',
            'globalSettings',
            async () => {
              let value: any = await firstValueFrom(
                this.store.select(getFetchModifiedDatesList)
                  .pipe(skipWhile((v: any) => v.isLoading))
              );
              return value?.data?.GlobalSettings || null;
            },
            async () => {
              const resp: any = await firstValueFrom(this.api.getGlobalSettingsAnonymous());
              return resp?.data || {};
            },
            (data, lastModified) => ({
              id: 'globalSettings',
              items: data,
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || {} : data || {};
            }
          )
        ).pipe(
          map((settings: any) => new FetchGlobalSettingsAnonymousSuccess(settings)),
          catchError(err => of(new OnError(err))),
        )
      })
    )
  );

  UpdateGlobalSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.UPDATE_GLOBAL_SETTINGS),
      switchMap((action: UpdateGlobalSettings) =>
        this.api.updateGlobalSettings(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              return of(
                new FetchModifiedDatesList(),
                new FetchGlobalSettingsAnonymous()
              );
            }
            return of(new FetchGlobalSettingsAnonymousSuccess({} as GlobalSettings));
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );


  UpdateAllowDuplicatesSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.UPDATE_ALLOW_DUPLICATES_SETTINGS),
      switchMap((action: UpdateAllowDuplicatesSettings) => {
        return this.api.updateAllowDuplicatesSettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              this.store.dispatch(new FetchModifiedDatesList());
              return new FetchGlobalSettingsAnonymous();
            }
            this.store.dispatch(new FetchModifiedDatesList());
            return new FetchGlobalSettingsAnonymous();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  UpdateMandatoryNotesSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.UPDATE_MANDATORY_NOTES_SETTINGS),
      switchMap((action: UpdateMandatoryNotesSettings) => {
        return this.api.updateMandatoryNotesSettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              this.store.dispatch(new FetchModifiedDatesList());
              return new FetchGlobalSettingsAnonymous();
            }
            return new FetchGlobalSettingsAnonymousSuccess({} as GlobalSettings);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  UpdateMandatoryProjectsSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.UPDATE_MANDATORY_PROJECTS_SETTING),
      switchMap((action: UpdateMandatoryProjectsSettings) => {
        return this.api.updateMandatoryProjectsSettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              this.store.dispatch(new FetchModifiedDatesList());
              return new FetchGlobalSettingsAnonymous();
            }
            return new FetchGlobalSettingsAnonymousSuccess({} as GlobalSettings);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  UpdateMandatoryPropertySettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.UPDATE_MANDATORY_PROPERTY_SETTING),
      switchMap((action: UpdateMandatoryPropertySettings) => {
        return this.api.updateMandatoryPropertySettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              this.store.dispatch(new FetchModifiedDatesList());
              return new FetchGlobalSettingsAnonymous();
            }
            return new FetchGlobalSettingsAnonymousSuccess({} as GlobalSettings);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCurrencyList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.FETCH_CURRENCY_LIST),
      map((action: FetchCurrencyList) => action),
      switchMap((data: any) => {
        return this.api.getAllCurrencies().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCurrencyListSuccess(resp.data);
            }
            return new FetchCurrencyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  UpdateOTPSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.UPDATE_OTP_SETTINGS),
      switchMap((action: UpdateOTPSettings) => {
        return this.api.updateOTP(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              this.store.dispatch(new FetchModifiedDatesList());
              return new FetchGlobalSettingsAnonymous();
            }
            return new FetchGlobalSettingsAnonymousSuccess({} as GlobalSettings);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getOTPGlobalSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.OTP_GLOBAL_SETTINGS),
      switchMap((action: FetchOTPGlobalSettings) =>
        this.api.getOTPGlobalSettings().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchOTPGlobalSettingsSuccess(resp);
            } else {
              return null;
            }
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getTempVariables$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.FETCH_TEMP_VARIABLES),
      switchMap((action: FetchTempVariables) =>
        this.api.getTempVariables().pipe(
          map((resp: any) => {
            return new FetchTempVariablesSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  updateListing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.LISTING_UPDATE),
      switchMap((action: UpdateListing) =>
        this.api.updateGlobalSettings(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.store.dispatch(new FetchModifiedDatesList());
              return new FetchGlobalSettingsAnonymous();
              // return action.payload?.shouldEnablePropertyListing
              //   ? of(
              //     new FetchGlobalSettings(),
              //     new FetchGlobalSettingsAnonymous(),
              //     new FetchPropertyTypesList('listing')
              //   )
              //   : of(
              //     new FetchGlobalSettings(),
              //     new FetchGlobalSettingsAnonymous(),
              //     new FetchPropertyTypesList()
              //   );
            }
            return null;
          }),
          catchError((error) => of(new OnError(error)))
        )
      )
    )
  );

  // All sources effects
  getAllSources$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GlobalSettingsActionTypes.FETCH_ALL_SOURCES),
      switchMap(() => {
        this.manageSourceIndexDBService.clearCaches();
        return this.manageSourceIndexDBService.getSourcesWithCaching().pipe(
          map((sources: any[]) => {
            if (!sources || sources.length === 0) {
              return new FetchAllSourcesSuccess([]);
            }
            return new FetchAllSourcesSuccess(sources);
          }),
          catchError((err) => {
            console.error('Error in getAllSources$ effect:', err);
            return of(new FetchAllSourcesSuccess([]));
          })
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: GlobalSettingsService,
    private _notificationService: NotificationsService,
    private manageSourceIndexDBService: ManageSourceIndexDBService,
    private store: Store<AppState>
  ) { }
}
