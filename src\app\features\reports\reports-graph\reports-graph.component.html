<!-- <div class="bg-dark w-100 px-16 py-12 text-white flex-between">
  <h3 class="fw-semi-bold">Visualization Graph</h3>
  <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
</div> -->
<div class="bg-white">
  <!-- Chart Controls Section -->
  <div class="align-center px-16 flex-wrap">
    <!-- Parent Category Selector (only shown if parent columns exist) -->
    <div class="w-25 tb-w-33 ip-w-50 tb-mb-16 ph-w-100 ph-mb-12" *ngIf="parentColumns.length > 0">
      <div class="mr-16 ph-mr-0">
        <div class="field-label-req mt-0" for="parentCategorySelector">Select Category:</div>
        <ng-select id="parentCategorySelector" [items]="parentColumns" bindLabel="headerName"
          [(ngModel)]="selectedParent" (change)="onParentSelectionChange($event)" placeholder="Select" [multiple]="true"
          [closeOnSelect]="false" class="ng-select-gray">

          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
            <div class="checkbox-container">
              <input type="checkbox" id="parent-category-{{index}}" [checked]="item$.selected"
                (click)="$event.stopPropagation()">
              <span class="checkmark"></span>
              <span class="text-truncate-1 break-all">{{ item.headerName }}</span>
            </div>
          </ng-template>
        </ng-select>
      </div>
    </div>

    <!-- Chart Data Fields Selector -->
    <div class="w-25 tb-w-33 ip-w-50 tb-mb-16 ph-w-100 ph-mb-12">
      <div class="mr-16 ph-mr-0">
        <div class="field-label-req mt-0" for="chartDataFieldsSelector">Select Data Field(s):</div>
        <ng-select [virtualScroll]="true" [items]="childColumns" [bindLabel]="getBindLabel()"
          [bindValue]="getBindValue()" [multiple]="true" [closeOnSelect]="false" placeholder="Select"
          [(ngModel)]="selectedChildren" (change)="onChildrenSelectionChange($event)"
          [compareWith]="getCompareFunction()" class="ng-select-gray">
          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
            <div class="checkbox-container">
              <input type="checkbox" id="chart-data-field-{{index}}" [checked]="item$.selected"
                (click)="$event.stopPropagation()">
              <span class="checkmark"></span>
              <span class="text-truncate-1 break-all">{{ getDisplayLabel(item) }}</span>
            </div>
          </ng-template>
          <ng-template ng-label-tmp let-item="item" let-clear="clear">
            <div class="align-center text-truncate-1 break-all">
              <span class="ic-cancel ic-dark icon ic-xx-xs mr-4" (click)="clear(item)"></span>
              <span class="ng-value-label"> {{ getDisplayLabel(item) }} </span>
            </div>
          </ng-template>
        </ng-select>
      </div>
    </div>

    <!-- Chart Type Selector -->
    <div class="w-25 tb-w-33 ip-w-50 tb-mb-16 ph-w-100 ph-mb-12">
      <div class="mr-16 ph-mr-0">
        <div class="field-label-req mt-0" for="chartTypeSelector">Chart Type:</div>
        <ng-select id="chartTypeSelector" [items]="chartTypes" bindLabel="label" bindValue="value"
          [(ngModel)]="selectedChartType" (change)="onChartTypeSelectionChange($event)" placeholder="Select Chart Type"
          [clearable]="false">
        </ng-select>
      </div>
    </div>

    <!-- Reset Chart Selections Button -->
    <div class="btn-coal my-4 h-32 min-w-32 mt-20" title="Reset Chart Selections" (click)="resetChartSelections()">
      <span class="icon ic-refresh ic-xxs"></span>
    </div>
  </div>

  <!-- No Selection Message -->
  <div *ngIf="showSelectionMessage" class="text-center">
    <div class="flex-center-col h-100-260">
      <div class="header-3 fw-600 text-center">Please Select Data Field(s)</div>
    </div>
  </div>

  <!-- Chart Display Section -->
  <div class="mt-16 w-100 justify-center px-10 chart-wrapper">
    <canvas id="activityChart" class="h-500"></canvas>
  </div>

  <!-- Chart Legend Container -->
  <div id="chart-legend-container" class="mt-8 px-10"></div>
</div>