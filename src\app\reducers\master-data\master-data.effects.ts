import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { asyncScheduler, firstValueFrom, from, of } from 'rxjs';
import { catchError, delay, exhaustMap, map, skipWhile, switchMap, throttleTime } from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';
import {
  FetchAnonymousAttributeList,
  FetchAnonymousAttributeListSuccess,
  FetchAreaUnitList,
  FetchAreaUnitListSuccess,
  FetchAssociatedBanks,
  FetchAssociatedBanksSuccess,
  FetchBrandsList,
  FetchBrandsListSuccess,
  FetchFetchProjectAmenitiesSuccess,
  FetchLeadSourceList,
  FetchLeadSourceListSuccess,
  FetchLeadStatusList,
  FetchLeadStatusListSuccess,
  FetchModifiedDatesListLoading,
  FetchModifiedDatesListSuccess,
  FetchProjectAmenities,
  FetchProjectAttributes,
  FetchProjectAttributesSuccess,
  FetchProjectProjectTypesSuccess,
  FetchProjectTypes,
  FetchPropertyAmenityList,
  FetchPropertyAmenityListSuccess,
  FetchPropertyTypesList,
  FetchPropertyTypesListSuccess,
  FetchQRAreaUnitList,
  FetchQRAreaUnitListSuccess,
  FetchQRPropertyTypesList,
  FetchQRPropertyTypesListSuccess,
  FetchUserServicesList,
  FetchUserServicesListSuccess,
  masterDataActionTypes
} from './master-data.actions';
import { MasterDataService } from 'src/app/services/controllers/master-data.service';
import { handleCachedData } from 'src/app/core/utils/common.util';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { getFetchModifiedDatesList } from './master-data.reducer';


@Injectable()
export class MasterDataEffects {
  fetchModifiedDatesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_MODIFIED_DATES_LIST),
      throttleTime(3000, asyncScheduler, { leading: true, trailing: false }),
      exhaustMap(() => {
        this.store.dispatch(new FetchModifiedDatesListLoading());
        return this.api.fetchModifiedDatesList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchModifiedDatesListSuccess(resp.data);
            }
            return new FetchModifiedDatesListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyAmenityList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_AMENITY_LIST),
      switchMap((action: FetchPropertyAmenityList) => {
        return this.api.fetchAmenityList(action?.ApiType).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyAmenityListSuccess(resp.data);
            }
            return new FetchPropertyAmenityListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );


  getAnonymousAttributeList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_ANONYMOUS_ATTRIBUTE_LIST),
      switchMap((action: FetchAnonymousAttributeList) => {
        return this.api.fetchAnonymousAttributeList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAnonymousAttributeListSuccess(resp.items);
            }
            return new FetchAnonymousAttributeListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadSourceList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_LEAD_SOURCE_LIST),
      switchMap((action: FetchLeadSourceList) => {
        return this.api.fetchLeadSourceList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadSourceListSuccess(resp.items);
            }
            return new FetchLeadSourceListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyTypes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_PROPERTY_TYPES_LIST),
      switchMap((action: FetchPropertyTypesList) => {
        return this.api.fetchPropertyTypes(action.ApiType).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyTypesListSuccess(resp.items);
            }
            return new FetchPropertyTypesListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getQRPropertyTypes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_QR_PROPERTY_TYPES_LIST),
      switchMap((action: FetchQRPropertyTypesList) => {
        return this.api.fetchQRPropertyTypes(action?.ApiType).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchQRPropertyTypesListSuccess(resp.items);
            }
            return new FetchQRPropertyTypesListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_LEAD_STATUS_LIST),
      switchMap((action: FetchLeadStatusList) => {
        return this.api.fetchLastModifiedList().pipe(
          switchMap((response: any) => {
            const masterleadstatusLastModified = localStorage?.getItem(
              'masterleadstatusLastModified'
            );
            if (
              !masterleadstatusLastModified ||
              !localStorage?.getItem('masterleadstatus') ||
              !JSON.parse(localStorage?.getItem('masterleadstatus'))?.length ||
              new Date(response?.data?.CustomMasterLeadStatus) >
              new Date(masterleadstatusLastModified)
            ) {
              return this.api.fetchLeadStatusList().pipe(
                map((resp: any) => {
                  if (resp.succeeded) {
                    localStorage.setItem(
                      'masterleadstatus',
                      JSON.stringify(resp?.data?.masterLeadStatusDtos || [])
                    );
                    localStorage.setItem(
                      'masterleadstatusLastModified',
                      resp?.data?.lastModifiedOn
                    );
                    return new FetchLeadStatusListSuccess(
                      resp?.data?.masterLeadStatusDtos
                    );
                  }
                  return new FetchLeadStatusListSuccess();
                }),
                catchError((err) => of(new OnError(err)))
              );
            }
            return of(new FetchLeadStatusListSuccess());
          }),
          catchError((preliminaryErr) => of(new OnError(preliminaryErr)))
        );
      })
    )
  );

  getAreaUnitList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_AREA_UNIT_LIST),
      switchMap(() =>
        from(
          handleCachedData(
            'areaUnitData',
            'areaUnitList',
            async (): Promise<any> => {
              let value: any = await firstValueFrom(this.store.select(getFetchModifiedDatesList).pipe(skipWhile((value: any) => value.isLoading)));
              return value?.data?.MasterAreaUnit || null;
            },
            async () => {
              const resp: any = await firstValueFrom(this.api.fetchAreaUnitList());
              return resp?.succeeded ? resp.items : [];
            },
            (data, lastModified) => ({
              id: 'areaUnitList',
              items: data,
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || [] : data || [];
            },
          )
        ).pipe(
          map((list: any) => new FetchAreaUnitListSuccess(list)),
          catchError(err => of(new OnError(err))),
        )
      )
    )
  );


  getProjectAttributes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_PROJECT_ATTRIBUTES),
      switchMap((action: FetchProjectAttributes) => {
        return this.api.fetchProjectAttributes().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectAttributesSuccess(resp);
            }
            return new FetchProjectAttributesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectAmenities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_PROJECT_AMENITIES),
      switchMap((action: FetchProjectAmenities) => {
        return this.api.fetchProjectAmenities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchFetchProjectAmenitiesSuccess(resp);
            }
            return new FetchFetchProjectAmenitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectTypes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_PROJECT_TYPE),
      switchMap((action: FetchProjectTypes) => {
        return this.api.fetchProjectType().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectProjectTypesSuccess(resp);
            }
            return new FetchProjectProjectTypesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAssociatedBanks$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_ASSOCIATE_BANK),
      switchMap((action: FetchAssociatedBanks) => {
        return this.api.fetchAssociateBanks().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAssociatedBanksSuccess(resp);
            }
            return new FetchAssociatedBanksSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getQRAreaUnitList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_QR_AREA_UNIT_LIST),
      switchMap((action: FetchQRAreaUnitList) => {
        return this.api.fetchQRAreaUnitList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchQRAreaUnitListSuccess(resp.items);
            }
            return new FetchQRAreaUnitListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUserServicesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_USER_SERVICES_LIST),
      switchMap((action: FetchUserServicesList) => {
        return this.api.fetchUserServicesList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserServicesListSuccess(resp.items);
            }
            return new FetchUserServicesListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getBrandsList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(masterDataActionTypes.FETCH_BRANDS_LIST),
      switchMap((action: FetchBrandsList) => {
        return this.api.fetchBrandsList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchBrandsListSuccess(resp.items);
            }
            return new FetchBrandsListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );


  constructor(private actions$: Actions, private api: MasterDataService, private store: Store<AppState>) { }
}
