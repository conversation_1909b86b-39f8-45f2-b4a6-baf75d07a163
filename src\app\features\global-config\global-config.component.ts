import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs/operators';

import {
  COMMUNICATION,
  communicationSettings,
  INTEGRATION_LIST,
  moduleSettings,
} from 'src/app/app.constants';
import { IntegrationSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ModuleSettings } from 'src/app/core/interfaces/global-settings';
import {
  AddIntegration,
  FetchIntegrationByIdSuccess,
  FetchIntegrationCount,
  GmailIntegration,
} from 'src/app/reducers/Integration/integration.actions';
import {
  getExcelFileLink,
  getIntegrationCount,
} from 'src/app/reducers/Integration/integration.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getGlobalSettingsAnonymous,
  getGlobalAnonymousIsLoading,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProfile } from 'src/app/reducers/profile/profile.actions';
import {
  getProfile,
  getProfileIsLoading,
} from 'src/app/reducers/profile/profile.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { FetchModifiedDatesList } from 'src/app/reducers/master-data/master-data.actions';
@Component({
  selector: 'global-config',
  templateUrl: './global-config.component.html',
})
export class IntegrateComponent implements OnInit, OnDestroy {
  currentLang: string = localStorage.getItem('locale')
    ? localStorage.getItem('locale')
    : 'en';
  communicationList: Array<{
    name: string;
    image: string;
    description: string;
  }> = COMMUNICATION;
  integrationMap: Array<{
    displayName: string;
    name: string;
    image: string;
    logo: string;
    description: string;
    category?: number; // 1 - 3rd party, 2 - social, 3 - others
    isEnabled?: boolean;
    value?: number;
  }> = [];
  filteredIntegrationMap: Array<{
    displayName: string;
    name: string;
    image: string;
    logo: string;
    description: string;
    category?: number; // 1 - 3rd party, 2 - social, 3 - others
    isEnabled?: boolean;
    value?: number;
  }> = [];
  integrationCommonFloorJustLead: Array<{
    name: string;
    logo: string;
    displayName: string;
    image: string;
  }> = [];
  integrationIvr: Array<{
    name: string;
    logo: string;
  }> = [];

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canGlobalSettingsView: boolean = false;
  canTemplatesView: boolean = false;
  canIntegrationView: boolean = false;
  zoneLocationEnabled: boolean = false;
  isGlobalSettingsLoading: boolean = true;
  isProfileLoading: boolean = true;
  canAdd: boolean = false;
  filteredModuleSettings: ModuleSettings[] = moduleSettings;
  communicationSettings: ModuleSettings[] = communicationSettings;
  selectedSection: string = 'Third';
  viewMore: boolean = false;
  closeAlertBox: boolean = false;
  listingEnabled: any;
  functionType: any;
  connectNowClicked: any;
  permissionsSet: Set<unknown>;
  allSources: any[] = [];
  hasThirdPartySources: boolean = false;
  hasSocialSources: boolean = false;
  hasOthersSources: boolean = false;
  hasAnySources: boolean = false;

  constructor(
    private headerTitle: HeaderTitleService,
    private router: Router,
    private _store: Store<AppState>,
    public metaTitle: Title,
  ) { }
  accountCount: any;
  searchTerm: string;
  profile: any;
  s3BucketUrl: string = 'https://leadrat-black.s3.ap-south-1.amazonaws.com/';
  tenantUrl: string = location?.href?.split?.('/')?.[2]
    ? 'https://' + location?.href?.split?.('/')?.[2]
    : '';

  getFullImageUrl(imageUrl: string): string {
    if (!imageUrl) {
      return 'assets/images/integration/default.svg';
    }
    if (imageUrl.startsWith('http') || imageUrl.startsWith('assets')) {
      return imageUrl;
    }
    const fullUrl = this.s3BucketUrl + imageUrl;
    return fullUrl;
  }

  createIntegrationObjects() {
    if (!this.allSources || this.allSources.length === 0) {
      return;
    }
    this.integrationMap = [];
    this.integrationCommonFloorJustLead = [];
    this.integrationIvr = [];
    const sourcesByDisplayName = new Map();
    const filteredSources = this.allSources.filter(source => {
      return INTEGRATION_LIST.some(integration => integration.value === source.value);
    });
    filteredSources.forEach(source => {
      const sourceName = IntegrationSource[source.value];
      if (!sourceName) {
        return;
      }
      const matchingIntegration = INTEGRATION_LIST.find(
        (integration: any) => integration.value === source.value
      );
      const displayName = source.displayName ||
        matchingIntegration?.displayName ||
        sourceName;
      if (!sourcesByDisplayName.has(displayName)) {
        sourcesByDisplayName.set(displayName, source);
      }
    });
    Array.from(sourcesByDisplayName.values()).forEach(source => {
      const sourceName = IntegrationSource[source.value];
      const matchingIntegration = INTEGRATION_LIST.find(
        (integration: any) => integration.value === source.value
      );
      const integration = {
        name: sourceName,
        displayName: matchingIntegration?.displayName || source.displayName || sourceName,
        image: matchingIntegration?.image || this.getFullImageUrl(source.imageURL || ''),
        logo: matchingIntegration?.logo || this.getFullImageUrl(source.imageURL || ''),
        description: matchingIntegration?.description || `Integration with ${source.displayName || sourceName}`,
        category: source.leadSourceType, // 0 for Others, 1 for Social, 2 for 3rd Party
        isEnabled: source.isEnabled,
        value: source.value
      };
      this.integrationMap.push(integration);
      if ([39, 20, 40, 30, 38]?.includes(source.value)) {
        const exists = this.integrationCommonFloorJustLead.some(
          item => item.name === integration.name
        );
        if (!exists) {
          this.integrationCommonFloorJustLead.push(integration);
        }
      }
      if (sourceName === 'IVR') {
        const exists = this.integrationIvr.some(
          item => item.name === integration.name
        );
        if (!exists) {
          this.integrationIvr.push(integration);
        }
      }
    });

    this.updateSourceAvailability();
    this.filterIntegration();
  }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.closeAlertBox = JSON.parse(
      localStorage.getItem('closeAlertBox') || 'false'
    );

    this._store
      .select(getIntegrationCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.accountCount = res;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.zoneLocationEnabled = data?.isZoneLocationEnabled;
        this.listingEnabled = data?.shouldEnablePropertyListing;
        this.updateModuleSettings();
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isGlobalSettingsLoading = isLoading;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissionsSet = new Set(permissions);
        this.canAdd = permissions?.includes('Permissions.Integration.Create');
        this.canIntegrationView = this.permissionsSet.has(
          'Permissions.Integration.View'
        );
        this.canGlobalSettingsView = this.permissionsSet.has(
          'Permissions.GlobalSettings.View'
        );
        this.canTemplatesView = this.permissionsSet.has(
          'Permissions.Templates.View'
        );
        if (permissions?.includes('Permissions.OrgProfile.View')) {
          this._store.dispatch(new FetchProfile());
        }
        if (this.canIntegrationView) {
          this._store.dispatch(new FetchIntegrationCount());
          this._store.dispatch(new FetchAllSources());
        }
        this._store.dispatch(new FetchModifiedDatesList());
        this._store.dispatch(new FetchGlobalSettingsAnonymous());
      });

    let authCode = decodeURIComponent(
      new URL(location.href)?.search
        .split('&')[1]
        ?.slice(5, new URL(location.href)?.search.split('&')[1]?.length)
    );
    let payload = {
      authorizationCode: authCode,
    };
    if (payload.authorizationCode !== 'undefined') {
      this._store.dispatch(new GmailIntegration(payload));
      this.router.navigate(['global-config']);
    }

    this._store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((resp: any) => {
        this.profile = resp;
      });

    this._store
      .select(getProfileIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProfileLoading = isLoading;
      });

    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((sources: any) => {
        if (sources) {
          this.allSources = sources;
          this.createIntegrationObjects();
        }
      });
  }

  updateModuleSettings(): void {
    this.filteredModuleSettings = moduleSettings?.filter((module) => {
      if (module?.name === 'locality') {
        return this.zoneLocationEnabled;
      }
      if (module?.name === 'listing-management') {
        return this.listingEnabled;
      }
      return true;
    });
  }

  updateSourceAvailability() {
    const validSources = this.allSources.filter(source =>
      INTEGRATION_LIST.some(integration => integration.value === source.value)
    );
    this.hasThirdPartySources = validSources.some(source =>
      source.isEnabled && source.leadSourceType === 2 // ThirdParty = 2
    );
    this.hasSocialSources = validSources.some(source =>
      source.isEnabled && source.leadSourceType === 1 // SocialProfiles = 1
    );

    // // Check if Instagram is in the sources
    // const instagramSource = validSources.find(source =>
    //   IntegrationSource[source.value] === 'Instagram'
    // );

    this.hasOthersSources = validSources.some(source =>
      source.isEnabled && source.leadSourceType === 0 // Others = 0
    );

    this.hasAnySources = this.hasThirdPartySources || this.hasSocialSources || this.hasOthersSources;

    if (this.selectedSection === 'Third' && !this.hasThirdPartySources) {
      if (this.hasSocialSources) {
        this.selectedSection = 'Social';
      } else if (this.hasOthersSources) {
        this.selectedSection = 'Others';
      }
    } else if (this.selectedSection === 'Social' && !this.hasSocialSources) {
      if (this.hasThirdPartySources) {
        this.selectedSection = 'Third';
      } else if (this.hasOthersSources) {
        this.selectedSection = 'Others';
      }
    } else if (this.selectedSection === 'Others' && !this.hasOthersSources) {
      if (this.hasThirdPartySources) {
        this.selectedSection = 'Third';
      } else if (this.hasSocialSources) {
        this.selectedSection = 'Social';
      }
    }
  }

  selectTab(section: string) {
    if (this.selectedSection !== section) {
      this.selectedSection = section;
      this.filterIntegration();
    }
  }

  filterIntegration() {
    const typeMap: any = {
      Third: 2,  // ThirdParty = 2
      Social: 1, // SocialProfiles = 1
      Others: 0, // Others = 0
    };
    const validSources = this.allSources.filter(source =>
      INTEGRATION_LIST.some(integration => integration.value === source.value)
    );
    const enabledSources = validSources.filter(source => {
      return source.isEnabled && source.leadSourceType === typeMap[this.selectedSection];
    });
    const sourcesByDisplayName = new Map();
    enabledSources.forEach(source => {
      const matchingIntegration = INTEGRATION_LIST.find(
        (integration: any) => integration.value === source.value);
      const displayName = source.displayName ||
        matchingIntegration?.displayName;
      if (!sourcesByDisplayName.has(displayName)) {
        sourcesByDisplayName.set(displayName, source);
      }
    });
    this.filteredIntegrationMap = Array.from(sourcesByDisplayName.values()).flatMap(source => {
      const sourceName = IntegrationSource[source.value];
      const matchingIntegrations = INTEGRATION_LIST.filter(
        (integration: any) => integration.value === source.value
      );
      if (matchingIntegrations.length) {
        return matchingIntegrations.map(matchingIntegration => ({
          name: sourceName,
          displayName: matchingIntegration.displayName || source.displayName || sourceName,
          image: matchingIntegration.image || this.getFullImageUrl(source.imageURL || ''),
          logo: matchingIntegration.logo || this.getFullImageUrl(source.imageURL || ''),
          description: matchingIntegration.description || `Integration with ${source.displayName || matchingIntegration.displayName || sourceName}`,
          category: source.leadSourceType,
          isEnabled: source.isEnabled,
          value: source.value
        }));
      }
      return {
        name: sourceName,
        displayName: source.displayName || sourceName,
        image: this.getFullImageUrl(source.imageURL || ''),
        logo: this.getFullImageUrl(source.imageURL || ''),
        description: `Integration with ${source.displayName || sourceName}`,
        category: source.leadSourceType,
        isEnabled: source.isEnabled,
        value: source.value
      };
    })?.filter(integration => ![39, 20, 40, 30, 38]?.includes(integration.value))
      ?.sort((a, b) => a.displayName.localeCompare(b.displayName));
  }

  isSourceEnabled(sourceName: string): boolean {
    const isInIntegrationList = INTEGRATION_LIST.some(
      integration => integration.name === sourceName
    );
    if (!isInIntegrationList) return false;
    const matchingSource = this.allSources.find(
      (source: any) => IntegrationSource[source.value] === sourceName);
    return matchingSource && matchingSource.isEnabled;
  }

  // openIntegration(image: string, displayName: string, name: string) {
  //   let initialState: any = {
  //     image: image,
  //     displayName: displayName,
  //     name: name,
  //   };
  //   this.modalService.show(
  //     ThirdPartyIntegrationComponent,
  //     Object.assign({}, { class: 'right-modal ip-modal-unset', initialState })
  //   );
  // }

  openOthersIntegration(_image: string, _displayName: string, name: string) {
    if (name === 'Instagram') {
      this.router.navigate(['/global-config/facebook']);
    } else if (name === 'GoogleCampaign') {
      this.router.navigate(['/global-config/google-campaign']);
    } else {
      this.router.navigate(['/global-config/facebook']);
    }
  }

  openCommonFloorJustLeadIntegration(
    image: string,
    displayName: string,
    name: string,
    count: string
  ) {
    if (this.functionType) {
      this.connectNowClicked?.emit();
      return;
    }

    localStorage.setItem('integrationData', JSON.stringify({ image, displayName, name, count }));

    this.router.navigate([`/global-config/integrations`]);
  }

  integrate(type: any) {
    let payload = {
      source: IntegrationSource[type],
    };
    this._store.dispatch(new FetchIntegrationByIdSuccess(''));
    this._store.dispatch(new AddIntegration(payload));
    this._store
      .select(getExcelFileLink)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          window.open(res, '_self');
        }
      });
  }

  navigateToManageSms() {
    this.router.navigate(['integration/manage-sms']);
  }

  searchRoleName(searchTerm: string) {
    this.searchTerm = searchTerm.trim().toLowerCase();
  }

  closeFBAlert() {
    this.closeAlertBox = true;
    localStorage.setItem('closeAlertBox', JSON.stringify(true));
  }

  checkpermission(settings: ModuleSettings[]) {
    return settings.filter((setting) => {
      if (setting.name === 'templates') {
        return this.canTemplatesView;
      }
      return this.canIntegrationView;
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
