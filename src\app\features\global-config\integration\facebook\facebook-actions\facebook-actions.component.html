<div class="align-center">
    <ng-container *ngFor="let account of filteredAccounts; let first = first">
        <ng-container *ngIf="first">
            <div title="Agency Name" class="bg-blue-850 icon-badge"
                (click)="openAgentNameModal(agencyModal, params.data?.id, account?.leadSource, actionLabel ==='Ads Action' ? params.data.adName : params?.data.name, params.data?.agencyName)">
                <span class="icon ic-suitcase m-auto ic-xxs"></span>
            </div>
            <div title="Config" class="bg-dark-red-30 icon-badge"
                (click)="openProjectAndLocationModal(params.data?.id, account?.leadSource, actionLabel ==='Ads Action' ? true : false, account.facebookAccountName, actionLabel ==='Ads Action' ? params.data.adName : params?.data.name);">
                <span class="icon ic-user-setting ic-xxxs"></span>
            </div>
            <div title="Assign To">
                <a class="bg-blue-800 icon-badge"
                    (click)="openAssignmentModal(params.data?.id,actionLabel ==='Ads Action' ? true : '',actionLabel ==='Ads Action' ? false : true, account, actionLabel ==='Ads Action' ? params.data.adName : params?.data.name);">
                    <span class="icon ic-assign-to ic-xxxs"></span>
                </a>
            </div>
            <a (click)="subscribeSingleAd(params.data, actionLabel ==='Ads Action' ? 'ads' : 'form');">
                <div *ngIf="!params.data?.isSubscribed else unsubscribe">
                    <div title="Subscribe">
                        <a class="bg-accent-green icon-badge">
                            <span class="icon ic-subscribe ic-xxxs"></span>
                        </a>
                    </div>
                </div>
                <ng-template #unsubscribe>
                    <div title="Unsubscribe">
                        <a class="bg-light-red icon-badge">
                            <span class="icon ic-unsubscribe ic-xxxs"></span>
                        </a>
                    </div>
                </ng-template>
            </a>
            <ng-template #agencyModal>
                <div class="p-20 border br-8">
                    <a class="ic-close-secondary ic-close-modal-coal ip-ic-close-modal" (click)="modalRef.hide()"></a>
                    <div>
                        <span class="fw-semi-bold fv-sm-caps">
                            {{ (actionLabel === 'Ads Action' ? 'INTEGRATION.ad-name' : 'INTEGRATION.lead-form') |
                            translate }}:
                        </span>
                        <span class="fw-700 text-large ml-4 text-truncate-1 break-all">{{selectedAccountName}}</span>
                    </div>
                    <div class="form-group mb-30">
                        <div class="field-label">{{ 'INTEGRATION.agency-name' | translate }}</div>
                        <ng-select [virtualScroll]="true" [items]="agencyNameList" [addTag]="true" ResizableDropdown
                            bindLabel="agencyName" bindValue="agencyName" [formControl]="agencyName" class="bg-white"
                            addTagText="Create New Agency Name" placeholder="ex. Mounika pampana"></ng-select>
                    </div>
                    <div class="flex-center">
                        <button class="btn-gray mr-20" (click)="closeModal()">
                            {{ 'BUTTONS.cancel' | translate }}</button>
                        <button class="btn-coal" (click)="updateAgentName()">
                            {{ 'BUTTONS.save' | translate }}</button>
                    </div>
                </div>
            </ng-template>
            <ng-template #assignmentModal>
                <form class="h-100vh prevent-text-select">
                    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
                        <h3 class="fw-semi-bold">{{ 'GLOBAL.lead'| translate}}
                            {{ 'LEADS.assignment' | translate }}</h3>
                        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()">
                        </div>
                    </div>
                    <div class="p-16 h-100-114 scrollbar">
                        <integration-assignment [image]="image" [isBulkAssignModel]="isBulkAssignModel"
                            [selectedAccountName]="selectedAccountName" [selectedIntegrations]="selectedIntegrations"
                            [canAllowSecondaryUsers]="canAllowSecondaryUsers" [sameAsPrimaryUsers]="sameAsPrimaryUsers"
                            [assignedSecondaryUsers]="assignedSecondaryUsers"
                            [assignedDuplicateUser]="assignedDuplicateUser"
                            [assignedPrimaryUsers]="assignedPrimaryUsers" [sameAsSelectedUsers]="sameAsSelectedUsers"
                            [assignedUser]="assignedUser" [assignedUserDetails]="assignedUserDetails"
                            [updatedIntegrationList]="updatedIntegrationList" [moduleId]="moduleId"
                            [canAssignToAny]="canAssignToAny" [allActiveUsers]="allActiveUsers"
                            [activeUsers]="activeUsers" [selectedAccountId]="selectedAccountId"
                            [selectedCount]="selectedCount" [canEnableAllowDuplicates]="canEnableAllowDuplicates"
                            [canEnableAllowSecondaryUsers]="canEnableAllowSecondaryUsers" [allUserList]="allUserList"
                            [userList]="userList" [isFbComponent]="true" [isAdAccount]="isAdAccount"
                            [isFormAccount]="isFormAccount" [selectedAdName]="selectedAdName"
                            (isShowAssignModalChanged)="closeModal()"></integration-assignment>
                    </div>
                </form>
            </ng-template>
        </ng-container>
    </ng-container>
</div>