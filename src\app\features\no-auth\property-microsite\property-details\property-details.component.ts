import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { BHKType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  formatCurrency,
  getBHKDisplayString,
  getBRDisplayString,
  getLocationDetailsByObj,
  isEmptyObject,
} from 'src/app/core/utils/common.util';
import { FetchGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';

@Component({
  selector: 'property-details',
  templateUrl: './property-details.component.html',
})
export class PropertyDetailsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() propertyInfo: any;
  @Input() isPropertySoldOut: boolean;
  formatCurrency = formatCurrency;
  formatBudget = formatBudget;
  getLocationDetailsByObj = getLocationDetailsByObj;
  isEmptyObject = isEmptyObject;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  BHKType = BHKType;
  defaultCurrency: string = 'INR';
  isListing: boolean = window.location.pathname.includes('listing')

  tick: AnimationOptions = {
    path: 'assets/animations/circle-tick.json',
  };

  constructor(private store: Store<AppState>) { }

  ngOnInit() {
    this.store.dispatch(new FetchGlobalSettingsAnonymous());

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency = data.countries && data.countries.length > 0 ? data.countries[0].defaultCurrency : null;
      });
  }
}
