import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
    AmenitiesAttributesActionTypes,
    CreateAmenitiesSuccess,
    CreateAttributesSuccess,
    DeleteAmenitySuccess,
    DeleteAttributeSuccess,
    DoesAmenityNameExistsSuccess,
    DoesAttributeNameExistsSuccess,
    FetchAmenityByIdSuccess,
    FetchAttributeByIdSuccess,
    FetchCategoryListSuccess,
    GetAllAmenitiesSuccess,
    GetAllAttributesSuccess,
    UpdateAmenitiesSuccess,
    UpdateAttributesSuccess
} from './amenities-attributes.action';

export type AmenitiesAttributesState = {
    amenities: any[],
    attributes: any[],
    amenitiesLoading: boolean,
    attributesLoading: boolean,
    categoryList?: any[];
    categoryListIsLoading: boolean;
    doesAmenityNameExists: boolean;
    doesAttrNameExists: boolean;
    amenityData: any;
    amenityDataIsLoading: boolean;
    attributeData: any;
    attrDataIsLoading: boolean;
}

const initialState: AmenitiesAttributesState = {
    amenities: [],
    attributes: [],
    amenitiesLoading: true,
    attributesLoading: true,
    categoryList: [],
    categoryListIsLoading: true,
    doesAmenityNameExists: false,
    doesAttrNameExists: false,
    amenityData: {},
    amenityDataIsLoading: false,
    attributeData: {},
    attrDataIsLoading: false,

};

export function amenitiesAttribuesReducer(state: AmenitiesAttributesState = initialState, action: Action): AmenitiesAttributesState {
    switch (action.type) {
        case AmenitiesAttributesActionTypes.FETCH_ALL_AMENITIES:
            return {
                ...state,
                amenitiesLoading: true
            }
        case AmenitiesAttributesActionTypes.FETCH_ALL_AMENITIES_SUCCESS:
            return {
                ...state,
                amenities: (action as GetAllAmenitiesSuccess).resp,
                amenitiesLoading: false
            }
        case AmenitiesAttributesActionTypes.FETCH_ALL_ATTRIBUTES:
            return {
                ...state,
                attributesLoading: true
            }
        case AmenitiesAttributesActionTypes.FETCH_ALL_ATTRIBUTES_SUCCESS:
            return {
                ...state,
                attributes: (action as GetAllAttributesSuccess).resp,
                attributesLoading: false
            }
        case AmenitiesAttributesActionTypes.CREATE_AMENITIES_SUCCESS:

            return {
                ...state,
                amenities: [...state?.amenities]?.map((category: any) => {
                    if (category.categoryName === (action as CreateAmenitiesSuccess)?.resp.category) {
                        return { ...category, amenities: [...category.amenities, (action as UpdateAmenitiesSuccess).resp] }
                    }
                    return category
                })
            }
        case AmenitiesAttributesActionTypes.CREATE_ATTRIBUTES_SUCCESS:
            return {
                ...state,
                attributes: [...state?.attributes, (action as CreateAttributesSuccess).resp]
            }
        case AmenitiesAttributesActionTypes.UPDATE_ATTRIBUTE_SUCCESS:
            return {
                ...state,
                attributes: [...state?.attributes]?.map((attribute: any) => {
                    if (attribute.id === (action as UpdateAttributesSuccess)?.resp?.id) {
                        return (action as UpdateAttributesSuccess)?.resp
                    }
                    return attribute;
                })
            }
        case AmenitiesAttributesActionTypes.UPDATE_AMENITY_SUCCESS:
            return {
                ...state,
                amenities: [...state?.amenities]?.map((category: any) => {
                    if (category.categoryName === (action as UpdateAmenitiesSuccess).resp.category) {
                        let updatedAmenity = category.amenities.map((amenity: any) => {
                            if (amenity?.id === (action as UpdateAmenitiesSuccess).resp?.id) {
                                return (action as UpdateAmenitiesSuccess).resp
                            }
                            return amenity
                        })
                        return { ...category, amenities: [...updatedAmenity] }
                    }
                    return category
                })
            }
        case AmenitiesAttributesActionTypes.DELETE_AMENITIES_SUCCESS:
            return {
                ...state,
                amenities: [...state.amenities]?.map((category: any) => {
                    return {
                        ...category, amenities: [...category.amenities]?.filter((amenity: any) => {
                            return amenity.id !== (action as DeleteAmenitySuccess).id
                        })
                    }
                })
            }
        case AmenitiesAttributesActionTypes.DELETE_ATTRIBUTE_SUCCESS:
            return {
                ...state,
                attributes: [...state.attributes]?.filter((attribute: any) => attribute.id !== (action as DeleteAttributeSuccess).id)
            }
        case AmenitiesAttributesActionTypes.FETCH_CATEGORY_LIST:
            return {
                ...state,
                categoryListIsLoading: true,
            };
        case AmenitiesAttributesActionTypes.FETCH_CATEGORY_LIST_SUCCESS:
            return {
                ...state,
                categoryList: (action as FetchCategoryListSuccess).response,
                categoryListIsLoading: false,
            };
        case AmenitiesAttributesActionTypes.DOES_AMENITY_NAME_EXISTS_SUCCESS:
            return {
                ...state,
                doesAmenityNameExists: (action as DoesAmenityNameExistsSuccess).response,
            };
        case AmenitiesAttributesActionTypes.DOES_ATTRIBUTE_NAME_EXISTS_SUCCESS:
            return {
                ...state,
                doesAttrNameExists: (action as DoesAttributeNameExistsSuccess).response,
            };
        case AmenitiesAttributesActionTypes.FETCH_AMENITY_BY_ID:
            return {
                ...state,
                amenityDataIsLoading: true,
            };
        case AmenitiesAttributesActionTypes.FETCH_AMENITY_BY_ID_SUCCESS:
            return {
                ...state,
                amenityData: (action as FetchAmenityByIdSuccess).response,
                amenityDataIsLoading: false,
            };
        case AmenitiesAttributesActionTypes.FETCH_ATTRIBUTE_BY_ID:
            return {
                ...state,
                attrDataIsLoading: true,
            };
        case AmenitiesAttributesActionTypes.FETCH_ATTRIBUTE_BY_ID_SUCCESS:
            return {
                ...state,
                attributeData: (action as FetchAttributeByIdSuccess).response,
                attrDataIsLoading: false,
            };
        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.amenitiesAttributes;



export const getAllAmenities = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.amenities
);

export const getAmenitiesLoading = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.amenitiesLoading
);

export const getAllAttributes = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.attributes
);

export const getAttributesLoading = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.attributesLoading
);

export const getCategoryList = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.categoryList
);

export const getCategoryListIsLoading = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.categoryListIsLoading
);

export const doesAemnityNameExists = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.doesAmenityNameExists
);

export const doesAttrNameExists = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.doesAttrNameExists
);

export const getAmenityData = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.amenityData
);

export const getAmenityDataIsLoading = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.amenityDataIsLoading
);

export const getAttributeData = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.attributeData
);

export const getAttributeDataIsLoading = createSelector(
    selectFeature,
    (state: AmenitiesAttributesState) => state.attrDataIsLoading
);