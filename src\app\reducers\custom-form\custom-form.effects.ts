import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import {
  catchError,
  map,
  switchMap,
} from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';
import { CustomFormService } from 'src/app/services/controllers/custom-form.service';
import { AddCustomForm, AddCustomFormSuccess, CustomFormActionTypes, DeleteCustomForm, DeleteCustomFormSuccess, FetchCustomForm, FetchCustomFormField, FetchCustomFormFieldExist, FetchCustomFormFieldExistSuccess, FetchCustomFormFieldSuccess, FetchCustomFormSuccess } from './custom-form.action';

@Injectable()
export class CustomFormEffects {
  addProjectsToLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.ADD_CUSTOMFORM),
      switchMap((action: AddCustomForm) => {
        return this.api.addCustomForm(action?.payload).pipe(
          map((resp: any) => {
            return new AddCustomFormSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomForm$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.FETCH_CUSTOMFORM),
      map((action: FetchCustomForm) => action),
      switchMap((data: any) => {
        return this.api.getCustomForm().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCustomFormSuccess(resp.items);
            }
            return new FetchCustomFormSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomFormFieldExist$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD_EXIST),
      switchMap((action: FetchCustomFormFieldExist) => {
        return this.api.getCustomFieldExist(action?.payload).pipe(
          map((resp: any) => {
            return new FetchCustomFormFieldExistSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomFormFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD),
      map((action: FetchCustomFormField) => action),
      switchMap((data: any) => {
        return this.api.getCustomFormField().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCustomFormFieldSuccess(resp.data);
            }
            return new FetchCustomFormFieldSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteCustomForm$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.DELETE_CUSTOMFORM),
      map((action: DeleteCustomForm) => action),
      switchMap((data: any) => {
        return this.api.deleteCustomForm(data.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Deleted Successfully');
              return new DeleteCustomFormSuccess();
            }
            return new DeleteCustomFormSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: CustomFormService,
    private _notificationService: NotificationsService
  ) { }
}