import { Injectable } from '@angular/core';
import { IntegrationEffects } from './reducers/Integration/integration.effects';
import { AmenitiesAttributesEffects } from './reducers/amenities-attributes/amenities-attributes.effects';
import { AnalyticsEffects } from './reducers/analytics/analytics.effects';
import { AttendanceEffects } from './reducers/attendance/attendance.effects';
import { AutomationEffects } from './reducers/automation/automation.effects';
import { CustomSubStatusEffects } from './reducers/custom-sub-status/custom-sub-status.effects';
import { TagEffects } from './reducers/custom-tags/custom-tags.effects';
import { DashboardEffects } from './reducers/dashboard/dashboard.effects';
import { DataReportsEffects } from './reducers/data-reports/data-reports.effects';
import { DataManagementEffects } from './reducers/data/data-management.effects';
import { EmailSettingsEffects } from './reducers/email/email-settings.effects';
import { FieldsEffects } from './reducers/fields/fields.effects';
import { FilterEffects } from './reducers/filter/filter.effects';
import { ForgotPasswordEffects } from './reducers/forgot-password/forgot-password.effects';
import { GlobalSettingsEffects } from './reducers/global-settings/global-settings.effects';
import { InvoiceEffects } from './reducers/invoice/invoice.effects';
import { LeadEffects } from './reducers/lead/lead.effects';
import { ListingSiteEffects } from './reducers/listing-site/listing-site.effects';
import { LoginEffects } from './reducers/login/login.effects';
import { MarketingEffects } from './reducers/manage-marketing/marketing.effects';
import { MasterDataEffects } from './reducers/master-data/master-data.effects';
import { NotificationInfoEffects } from './reducers/notification-info/notification-info.effect';
import { PlacesEffects } from './reducers/places/places.effects';
import { ProfileEffects } from './reducers/profile/profile.effects';
import { ProjectEffects } from './reducers/project/project.effects';
import { PropertyEffects } from './reducers/property/property.effects';
import { QrFormEffects } from './reducers/qr-form/qr-form.effects';
import { ReferenceIdEffects } from './reducers/reference-id-management/reference-id-management.effects';
import { ReportsEffects } from './reducers/reports/reports.effects';
import { ShiftTimingEffects } from './reducers/shift-timing/shift-timing.effects';
import { SiteEffects } from './reducers/site/site.effects';
import { StatusEffects } from './reducers/status/status.effects';
import { TeamsEffects } from './reducers/teams/teams.effects';
import { TemplateEffects } from './reducers/template/template.effects';
import { TodoEffects } from './reducers/todo/todo.effects';
import { WhatsappCloudEffects } from './reducers/whatsapp-cloud/whatsapp-cloud.effects';
import { WhatsappEffects } from './reducers/whatsapp/whatsapp.effects';
import { TenantEffects } from './reducers/tenant/tenant.effects';
import { ProductsEffects } from './reducers/this/products/products.effects';
import { CustomFormEffects } from './reducers/custom-form/custom-form.effects';

@Injectable()
export class AppGlobalEffects {
  constructor() { }
}

export const AppEffects = [
  AppGlobalEffects,
  MasterDataEffects,
  IntegrationEffects,
  LeadEffects,
  PlacesEffects,
  TodoEffects,
  PropertyEffects,
  TeamsEffects,
  LoginEffects,
  ProfileEffects,
  DashboardEffects,
  ForgotPasswordEffects,
  GlobalSettingsEffects,
  TemplateEffects,
  WhatsappEffects,
  WhatsappCloudEffects,
  ReportsEffects,
  ProjectEffects,
  AttendanceEffects,
  AutomationEffects,
  TagEffects,
  DataManagementEffects,
  SiteEffects,
  QrFormEffects,
  InvoiceEffects,
  DataReportsEffects,
  CustomSubStatusEffects,
  StatusEffects,
  EmailSettingsEffects,
  ShiftTimingEffects,
  AmenitiesAttributesEffects,
  MarketingEffects,
  FieldsEffects,
  MarketingEffects,
  ListingSiteEffects,
  FilterEffects,
  ReferenceIdEffects,
  NotificationInfoEffects,
  AnalyticsEffects,
  NotificationInfoEffects,
  TenantEffects,
  ProductsEffects,
  CustomFormEffects
];
