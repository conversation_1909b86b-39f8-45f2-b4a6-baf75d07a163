<div class="position-relative w-100 h-100">
  <!-- Application loader component -->
  <div class="flex-center h-100 w-100" *ngIf="isLoading">
    <application-loader></application-loader>
  </div>

  <div class="flex-center h-100 w-100" *ngIf="!isLoading && !applicationUrl">
    <div class="text-center">
      <p class="text-muted">Application not available</p>
    </div>
  </div>

  <iframe #iframeElement [src]="safeUrl" class="w-100 h-100" [title]="applicationDetails?.description || 'Application'"
    (load)="onIframeLoad()" [style.visibility]="isLoading ? 'hidden' : 'visible'" *ngIf="applicationUrl">
  </iframe>
</div>