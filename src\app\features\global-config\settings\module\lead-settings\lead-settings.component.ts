import {
  Component,
  <PERSON><PERSON><PERSON>ter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { map, skipWhile, take, takeUntil } from 'rxjs';

import { DAYS } from 'src/app/app.constants';
import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  AllowDuplicateOption,
  MandatoryNotesOption,
  MandatoryProjectOption,
  MandatoryPropertyOption,
} from 'src/app/core/interfaces/global-settings';
import {
  getAssignedToDetails,
  getTimeZoneTime,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  FetchPriorityList,
  updatePriorityList,
} from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import {
  CreateSubStatus,
  DeleteSubStatus,
  ExistSubStatus,
  FetchLeadCount,
  UpdateCustomSubStatus,
} from 'src/app/reducers/custom-sub-status/custom-sub-status.actions';
import {
  getSubStatusCountIsLoading,
  getSubStatusExist,
  getSubStatusLeadCount,
} from 'src/app/reducers/custom-sub-status/custom-sub-status.reducers';
import {
  FetchGlobalSettingsAnonymous,
  UpdateAllowDuplicatesSettings,
  UpdateGlobalSettings,
  UpdateMandatoryNotesSettings,
  UpdateMandatoryProjectsSettings,
  UpdateMandatoryPropertySettings
} from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getDuplicateSettings,
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  getIsLeadCustomStatusEnabled
} from 'src/app/reducers/lead/lead.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchLeadStatusList } from 'src/app/reducers/master-data/master-data.actions';
import { getStatusIsLoading } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchCustomStatus } from 'src/app/reducers/status/status.actions';
import {
  DeleteRetention,
  FetchAllTeams,
  FetchRetentionList,
  FetchRotationList
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAllTeamsList,
  getRetentionList,
  getRetentionListisLoading,
  getRotationList,
  getRotationListIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'lead-settings',
  templateUrl: './lead-settings.component.html',
})
export class LeadSettingsComponent implements OnInit, OnDestroy {
  @ViewChild('statusModal') statusModal: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  leadSettingsForm: FormGroup;
  leadRotationAddGroupForm: FormGroup;
  settingData: any;
  canUpdate: boolean;
  canView: boolean;
  message: string;
  notes: string;
  isPriorityListOpen: boolean = false;
  allPriorityOptions: Object[] = [];
  priorityOptions: Object[] = [];
  isAllowDuplicateOptionsValueChanged = false;
  allowDuplicateOptions: AllowDuplicateOption[] = [];
  isPriorityListChanged: boolean = false;
  priorityList: any[];
  zoneLocationEnabled: any;
  settingType: string;
  duplicateSettingData: any;
  mandatoryNotesOptions: MandatoryNotesOption[] = [];
  mandatoryProjectOptions: MandatoryProjectOption[] = [];
  mandatoryPropertyOptions: MandatoryPropertyOption[] = [];
  isMandatoryNotesOptionsValueChanged = false;
  isMandatoryProjectOptionsValueChanged = false;
  isMandatoryPropertyOptionsValueChanged = false;
  remainingStatuses: any[] = [];
  showStatusList: any[] = [];
  popUpName: string;
  retentionListIsLoading: boolean = true;
  leadRotationIsLoading: boolean = true;
  //custom sub status
  subStatusName: FormControl = new FormControl('', Validators.required);
  masterLeadStatus: Array<any> = JSON.parse(
    localStorage.getItem('masterleadstatus')
  )
    ?.filter(
      (status: any) =>
        status?.actionName !== 'New' &&
        status?.actionName !== 'Pending' &&
        status?.actionName !== 'Invoiced'
    )
    .sort((a: any, b: any) => a?.actionName?.localeCompare(b?.actionName));
  selectedStatus: any;
  subStatusData: any;
  updatingSubStatusId: string;
  matchedDisplayNames: any[];
  doesExistSubStatus: boolean;
  isSubStatusCountLoading: boolean = true;
  isLeadRotationOpen: boolean = true;
  isLeadRetentionOpen: boolean = true;
  canEditGroup: boolean = false;
  allUsers: any[] = [];
  users: any[] = [];
  filteredUsers: any[] = [];
  leadRotationGroups: any[] = [];
  canAddGroup: boolean = false;
  isUsersListLoading: boolean = true;
  isGlobalSettingsLoading: boolean = true;
  isCustomStatusEnabled: boolean = false;
  canAddRetention: boolean = false;
  retentionList: any[];
  allTeams: any[];
  deleteGroup: string = '';
  getTimeZoneTime = getTimeZoneTime
  getAssignedToDetails = getAssignedToDetails;
  deleteGroupId: string;
  userData: any;
  constructor(
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title,
    public router: Router,
    public notificationService: NotificationsService
  ) {
    this.leadSettingsForm = fb.group({
      exportLeads: [null],
      isLeadRotationEnabled: [false],
      isPastDateSelectionEnabled: [false],
      isLeadRetentionEnabled: [false],
      leadsSourceUpdate: [null],
      internationalNo: [null],
      callDetection: [null],
      maskLeadContactNo: [null],
      isLocationMandatory: [null],
      allowDuplicateLeads: [null],
      dualOwnership: [null],
      stickyAgent: [null],
      mandatoryNotes: [null],
      mandatoryProjects: [null],
      mandatoryProperty: [null],
      priority1: [null, Validators.required],
      priority2: [null, Validators.required],
      isCustomLeadFormEnabled: [null],
      isWhatsAppDeepIntegration: [null],
    });

    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.store.dispatch(new FetchGlobalSettingsAnonymous());
    this.store.dispatch(new FetchLeadCount());
    this.store.dispatch(new FetchAllTeams());
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.store
      .select(getAllTeamsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allTeams = data;
      });

    this.store.dispatch(new FetchRotationList());
    this.store
      .select(getRotationList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.store
          .select(getUsersListForReassignment)
          .pipe(takeUntil(this.stopper))
          .subscribe((users: any) => {
            if (users) {
              this.users = users.map((user: any) => {
                return {
                  ...user,
                  fullName: `${user.firstName} ${user.lastName}`,
                };
              });
            }
          });
        this.leadRotationGroups = data.map((group: any) => {
          return {
            ...group,
            isEditEnabled: false,
          };
        });
      });

    this.store
      .select(getRotationListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.leadRotationIsLoading = loading;
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canView = permissionsSet.has('Permissions.GlobalSettings.View');
        this.canUpdate = permissionsSet.has(
          'Permissions.GlobalSettings.Update'
        );
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.getAllUsers();
        } else {
          this.getAdminAndReportees();
        }
      });

    this.store
      .select(getSubStatusCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isSubStatusCountLoading = isLoading;
      });
    this.store.dispatch(new FetchRetentionList());

    this.store
      .select(getRetentionList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.retentionList = [...data].map((retention) => {
          return {
            ...retention,
            isEditEnabled: false,
          };
        });
      });

    this.store
      .select(getRetentionListisLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.retentionListIsLoading = loading;
      });

    this.store
      .select(getSubStatusLeadCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.masterLeadStatus = this.masterLeadStatus?.map((item: any) => {
          if (item.childTypes && item.childTypes.length) {
            item.childTypes = item.childTypes.map((child: any) => {
              const matchedData = data.find((d: any) => d.id === child.id);
              if (matchedData) {
                return { ...child, leadCount: matchedData.leadCount };
              }
              return child;
            });
          }
          return item;
        });
      });

    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((priorities: any[]) => {
        this.priorityList = priorities;
        this.priorityOptions = [];
        this.allPriorityOptions = [];
        this.priorityList.forEach((priority: any) => {
          this.priorityOptions.push({
            label: priority.name,
            value: priority.id,
          });
          this.allPriorityOptions.push({
            label: priority.name,
            value: priority.id,
          });
        });

        if (!this.zoneLocationEnabled && !this.settingData?.shouldEnablePropertyListing) {
          this.priorityOptions = this.priorityOptions.filter(
            (option: any) =>
              !['Location', 'Zone', 'City', 'ReferenceId'].includes(option.label)
          );
          this.allPriorityOptions = this.priorityOptions;
          this.setPriorityFields(2);
        } else if (this.zoneLocationEnabled && !this.settingData?.shouldEnablePropertyListing) {
          this.priorityOptions = this.priorityOptions.filter(
            (option: any) =>
              !['ReferenceId'].includes(option.label)
          );
          this.allPriorityOptions = this.priorityOptions;

          this.setPriorityFields(5);
        } else if (!this.zoneLocationEnabled && this.settingData?.shouldEnablePropertyListing) {
          this.priorityOptions = this.priorityOptions.filter(
            (option: any) =>
              !['Location', 'Zone', 'City'].includes(option.label)
          );
          this.allPriorityOptions = this.priorityOptions;
          this.setPriorityFields(3);
        } else {
          this.allPriorityOptions = this.priorityOptions;
          this.setPriorityFields(6);
        }

        this.leadSettingsForm.patchValue({
          priority1: this.priorityOptions[0],
          priority2: this.priorityOptions[1],
          priority3: this.priorityOptions[2],
          priority4: this.priorityOptions[3],
          priority5: this.priorityOptions[4],
          priority6: this.priorityOptions[5],
        });
        this.priorityOptions = [];
      });
    this.patchValues(true);
  }

  async ngOnInit() {
    await this.store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          this.isGlobalSettingsLoading = isLoading;
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this.isCustomStatusEnabled = await this.store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();

    this.modalRef?.onHide?.subscribe(() => (this.deleteGroupId = ''));
  }

  setPriorityFields(count: number) {
    ['priority1', 'priority2', 'priority3', 'priority4', 'priority5', 'priority6'].forEach((field, index) => {
      if (index < count) {
        if (!this.leadSettingsForm.controls[field]) {
          this.leadSettingsForm.addControl(field, this.fb.control(null, Validators.required));
        }
      } else {
        if (this.leadSettingsForm.controls[field]) {
          this.leadSettingsForm.removeControl(field);
        }
      }
    });
  }

  convertTimeIntervalToMinutes(timeInterval: string): string {
    if (!timeInterval) return '';
    const [hoursStr, minutesStr, secondsStr] = timeInterval?.split(':');

    const hours = parseInt(hoursStr);
    const minutes = parseInt(minutesStr);
    const seconds = parseInt(secondsStr);

    const totalMinutes = hours * 60 + minutes + seconds / 60;

    return totalMinutes + ' ' + 'min(s)';
  }

  AddGroupHandler() {
    // Show all active users instead of filtering out users in other teams
    this.filteredUsers = this.users.filter(user => user.isActive);
    this.canAddGroup = true;
    this.leadRotationGroups.forEach((group) => (group.isEditEnabled = false));
  }

  AddRetentionHandler() {
    this.canAddRetention = true;
    this.retentionList.forEach((group) => (group.isEditEnabled = false));
  }

  editGroup(group: any) {
    // Show all active users instead of filtering out users in other teams
    this.filteredUsers = this.users.filter(user => user.isActive);

    this.leadRotationGroups.forEach((leadRotationGroup) => {
      leadRotationGroup.isEditEnabled = leadRotationGroup === group;
    });

    this.canEditGroup = true;
    this.canAddGroup = false;
  }

  editRetentionGroup(group: any) {
    this.retentionList.forEach((retentionGroup) => {
      retentionGroup.isEditEnabled = retentionGroup === group;
    });

    this.canAddRetention = false;
  }

  getAllUsers() {
    this.allUsers = this.users;
  }

  getAdminAndReportees() {
    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.users = users;
      });
  }

  closeAddGroupWithId(group: any) {
    if (group) {
      this.leadRotationGroups = this.leadRotationGroups.map(
        (rotationGroup: any) => {
          if (group?.id == rotationGroup?.id) {
            rotationGroup.isEditEnabled == false;
          }
          return rotationGroup;
        }
      );
      return;
    }
    this.canAddGroup = false;
  }

  closeAddRetention(group: any) {
    if (group) {
      this.retentionList = this.retentionList.map((retentionGroup: any) => {
        if (group?.id == retentionGroup?.id) {
          retentionGroup.isEditEnabled == false;
        }
        return retentionGroup;
      });
      return;
    }
    this.canAddRetention = false;
  }

  patchValues(isClickCancel: boolean) {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingData = data;
        this.zoneLocationEnabled = data?.isZoneLocationEnabled;
        this.isLeadRotationOpen = this.settingData?.isLeadRotationEnabled;
        this.isLeadRetentionOpen = this.settingData?.isLeadRetentionEnabled;
        isClickCancel && this.store.dispatch(new FetchPriorityList());
        this.leadSettingsForm.patchValue({
          ...this.settingData,
          exportLeads: this.settingData.isLeadsExportEnabled,
          isLeadRotationEnabled: this.settingData?.isLeadRotationEnabled,
          isPastDateSelectionEnabled: this.settingData?.isPastDateSelectionEnabled,
          isLeadRetentionEnabled: this.settingData?.isTeamLeadRotationEnabled,
          leadsSourceUpdate: this.settingData.isLeadSourceEditable,
          internationalNo: this.settingData.hasInternationalSupport,
          callDetection: this.settingData.isCallDetectionActivated,
          dualOwnership: this.settingData.isDualOwnershipEnabled,
          maskLeadContactNo: !this.settingData.isMaskedLeadContactNo,
          mandatoryNotes:
            this.settingData?.leadNotesSetting?.isNotesMandatoryEnabled,
          stickyAgent: this.settingData?.isStickyAgentEnabled,
          mandatoryProjects:
            this.settingData?.leadProjectSetting?.isProjectMandatoryEnabled,
          mandatoryProperty:
            this.settingData?.leadPropertySetting?.isPropertyMandatoryEnabled,
          isCustomLeadFormEnabled: this.settingData?.isCustomLeadFormEnabled,
          isWhatsAppDeepIntegration: this.settingData?.isWhatsAppDeepIntegration,
          isLocationMandatory: this.settingData?.generalSettings?.isLocationMandatory,
        });
        this.mandatoryNotesOptions = [
          {
            label: 'Add lead/Edit lead',
            key: 'isNotesMandatoryOnAddLead',
            value:
              this.settingData?.leadNotesSetting?.isNotesMandatoryOnAddLead,
            disabled:
              this.settingData?.leadNotesSetting?.isNotesMandatoryEnabled,
          },
          {
            label: 'Lead Status',
            key: 'isNotesMandatoryOnUpdateLead',
            value:
              this.settingData?.leadNotesSetting?.isNotesMandatoryOnUpdateLead,
            disabled:
              this.settingData?.leadNotesSetting?.isNotesMandatoryEnabled,
          },
          {
            label: 'Meeting Done',
            key: 'isNotesMandatoryOnMeetingDone',
            value:
              this.settingData?.leadNotesSetting?.isNotesMandatoryOnMeetingDone,
            disabled:
              this.settingData?.leadNotesSetting?.isNotesMandatoryEnabled,
          },
          {
            label: this.settingData?.shouldRenameSiteVisitColumn ? 'Referral Taken' : 'Site visit Done',
            key: 'isNotesMandatoryOnSiteVisitDone',
            value:
              this.settingData?.leadNotesSetting
                ?.isNotesMandatoryOnSiteVisitDone,
            disabled:
              this.settingData?.leadNotesSetting?.isNotesMandatoryEnabled,
          },
        ];
        this.mandatoryProjectOptions = [
          {
            label: 'Meeting Done',
            key: 'isProjectMandatoryOnMeetingDone',
            value:
              this.settingData?.leadProjectSetting
                ?.isProjectMandatoryOnMeetingDone,
            disabled:
              this.settingData?.leadProjectSetting?.isProjectMandatoryEnabled,
          },
          {
            label: this.settingData?.shouldRenameSiteVisitColumn ? 'Referral Taken' : 'Site visit Done',
            key: 'isProjectMandatoryOnSiteVisitDone',
            value:
              this.settingData?.leadProjectSetting
                ?.isProjectMandatoryOnSiteVisitDone,
            disabled:
              this.settingData?.leadProjectSetting?.isProjectMandatoryEnabled,
          },
          {
            label: 'Book',
            key: 'isProjectMandatoryOnBooking',
            value:
              this.settingData?.leadProjectSetting?.isProjectMandatoryOnBooking,
            disabled:
              this.settingData?.leadProjectSetting?.isProjectMandatoryEnabled,
          },
        ];
        this.mandatoryPropertyOptions = [
          {
            label: 'Meeting Done',
            key: 'isPropertyMandatoryOnMeetingDone',
            value:
              this.settingData?.leadPropertySetting
                ?.isPropertyMandatoryOnMeetingDone,
            disabled:
              this.settingData?.leadPropertySetting?.isPropertyMandatoryEnabled,
          },
          {
            label: 'Site visit Done',
            key: 'isPropertyMandatoryOnSiteVisitDone',
            value:
              this.settingData?.leadPropertySetting
                ?.isPropertyMandatoryOnSiteVisitDone,
            disabled:
              this.settingData?.leadPropertySetting?.isPropertyMandatoryEnabled,
          },
          {
            label: 'Book',
            key: 'isPropertyMandatoryOnBooking',
            value:
              this.settingData?.leadPropertySetting
                ?.isPropertyMandatoryOnBooking,
            disabled:
              this.settingData?.leadPropertySetting?.isPropertyMandatoryEnabled,
          },
        ];

      });

    this.store
      .select(getDuplicateSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.duplicateSettingData = data;
        this.leadSettingsForm.patchValue({
          ...this.duplicateSettingData,
          allowDuplicateLeads: this.duplicateSettingData?.isFeatureAdded,
          stickyAgent: this.settingData?.isStickyAgentEnabled,
        });
        this.allowDuplicateOptions = [
          {
            label: 'All Cases',
            description: 'duplicate leads from anywhere will be allowed',
            key: 'allowAllDuplicates',
            value: this.duplicateSettingData?.allowAllDuplicates,
            disabled: this.duplicateSettingData?.isFeatureAdded,
          },
          {
            label: 'Project',
            description:
              'duplicate leads will be allowed based on assigned project',
            key: 'isProjectBased',
            value: this.duplicateSettingData?.isProjectBased,
            disabled: this.duplicateSettingData?.isFeatureAdded,
          },
          {
            label: 'Source',
            description:
              'duplicate leads will be allowed based on assigned source',
            key: 'isSourceBased',
            value: this.duplicateSettingData?.isSourceBased,
            disabled: this.duplicateSettingData?.isFeatureAdded,
          },
          {
            label: 'Location',
            description:
              'duplicate leads will be allowed based on assigned location',
            key: 'isLocationBased',
            value: this.duplicateSettingData?.isLocationBased,
            disabled: this.duplicateSettingData?.isFeatureAdded,
          },
          {
            label: 'Sub-source',
            description:
              'duplicate leads will be allowed based on assigned sub-source',
            key: 'isSubSourceBased',
            value: this.duplicateSettingData?.isSubSourceBased,
            disabled: this.duplicateSettingData?.isFeatureAdded,
          },
        ];

        this.leadSettingsForm
          .get('allowDuplicateLeads')
          .valueChanges.pipe(takeUntil(this.stopper))
          .subscribe((allowDuplicates) => {
            if (!allowDuplicates) {
              this.leadSettingsForm.patchValue({
                stickyAgent: false,
              });
            }
          });

      });
  }

  deleteLeadRotationGroup(deletePopup: any, group: any) {
    this.modalRef = this.modalService.show(deletePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    this.message = 'Are you sure You want delete the Group?';
    this.notes = 'The deleted data Cannot be reverted back.';
    this.deleteGroupId = group.id;
    this.deleteGroup = 'rotation';
  }

  deleteLeadRetentionGroup(deletePopup: any, group: any) {
    this.modalRef = this.modalService.show(deletePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    this.message =
      'Are you sure you want to delete the retention configuration?';
    this.notes = 'The deleted configuration cannot be reverted back.';
    this.deleteGroupId = group.id;
    this.deleteGroup = 'retention';
  }

  deleteGroupConfirm() {
    if (this.deleteGroupId) {
      if (this.deleteGroup === 'rotation') {
        this.store.dispatch(new DeleteRetention(this.deleteGroupId));
      } else if (this.deleteGroup === 'retention') {
        this.store.dispatch(new DeleteRetention(this.deleteGroupId));
      }
    }
    this.deleteGroupId = '';
    this.modalRef.hide();
  }

  getLeadSourceName(leadSources: any[]): string {
    if (!leadSources || !Array.isArray(leadSources)) return '';
    return leadSources.map(source => LeadSource[source]).join(', ');
  }

  openConfirmModal(
    changePopup: any,
    settingType: string,
    childTypeCount: number
  ) {
    if (this.selectedStatus) {
      this.selectedStatus.isFormVisible = false;
    }
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    this.settingType = settingType;
    switch (settingType) {
      case 'exportLeads':
        if (this.leadSettingsForm.value.exportLeads) {
          this.message =
            'Are you sure you want to disable the “Export” option?';
          this.notes =
            'Users will the permission to export leads will not be able to export leads anymore.';
        } else {
          this.message = 'Are you sure you want to enable the “Export” option?';
          this.notes =
            'Users with export permission will be able to export leads.';
        }
        break;
      case 'isLeadRotationEnabled':
        if (this.leadSettingsForm.value.isLeadRotationEnabled) {
          this.message =
            'Are you sure you want to disable the “Lead Rotation” option?';
          this.notes =
            'This will remove all lead rotational settings and created Groups will not be removed';
        } else {
          this.message =
            'Are you sure you want to enable the “Lead Rotation” option?';
          this.notes =
            'This will rotate the Integrational Leads within the created Groups of the Users';
        }
        break;
      case 'isLeadRetentionEnabled':
        if (this.leadSettingsForm.value.isLeadRetentionEnabled) {
          this.message =
            'Are you sure you want to disable the "Retention Configurations"?';
          this.notes =
            'Retention feature is implemented to focus on recovering dead / dropped leads';
        } else {
          this.message =
            'Are you sure you want to enable the "Retention Configurations" ?';
          this.notes =
            'Retention feature is implemented to focus on recovering dead / dropped leads';
        }
        break;
      case 'isPastDateSelectionEnabled':
        if (this.leadSettingsForm.value.isPastDateSelectionEnabled) {
          this.message =
            'Are you sure you want to disable the Past Date selection for the scheduling or marking scheduler-enabled lead statuses?';
          this.notes =
            'This will disable past date selection.'
        } else {
          this.message =
            'Are you sure you want to enable the Past Date selection for the scheduling or marking scheduler-enabled lead statuses?';
          this.notes =
            'This will enable past date selection.';
        }
        break;
      case 'leadsSourceUpdate':
        if (this.leadSettingsForm.value.leadsSourceUpdate) {
          this.message =
            'Are you sure you want to disable the “Lead source update” option?';
          this.notes =
            'This will disable your users from updating lead source.';
        } else {
          this.message =
            'Are you sure you want to enable the “Lead source update” option?';
          this.notes =
            'This will allow your users (with edit permission) to update lead source.';
        }
        break;
      case 'internationalNo':
        if (this.leadSettingsForm.value.internationalNo) {
          this.message =
            'Are you sure you want to disable the “International Number” option?';
          this.notes =
            'This will disable users from adding leads with international numbers.';
        } else {
          this.message =
            'Are you sure you want to enable the “International Number” option?';
          this.notes =
            'This will allow the users to add leads with International numbers';
        }
        break;
      case 'callDetection':
        if (this.leadSettingsForm.value.callDetection) {
          this.message =
            'Are you sure you want to disable the “Call Detection”';
          this.notes =
            'This will disable the call detection for your organization.';
        } else {
          this.message = 'Are you sure you want to enable the “Call Detection”';
          this.notes =
            'This will allow users to track calls from leads if the lead is assigned to them';
        }
        break;
      case 'dualOwnership':
        if (this.leadSettingsForm.value.dualOwnership) {
          this.message =
            'Are you Sure you want Disable Dual Ownership for each Lead?';
          this.notes =
            'Disabling This will Remove all secondary owners from The leads';
        } else {
          this.message =
            'Are you Sure you want Enable Dual Ownership for each Lead?';
          this.notes =
            'This will Enable the permission to assign two owners to one lead.';
        }
        break;
      case 'maskLeadContactNo':
        if (this.leadSettingsForm.value.maskLeadContactNo) {
          this.message =
            'Are you sure you want to Hide the “Lead Contact Info”';
          this.notes = 'This will Hide the lead contact info for your leads.';
        } else {
          this.message =
            'Are you sure you want to Show the “Lead Contact Info”';
          this.notes = 'This will allow users to see lead contact info';
        }
        break;
      case 'isLocationMandatory':
        if (this.leadSettingsForm.value.isLocationMandatory) {
          this.message =
            'Are you sure you want to disable the “Mandatory Location” option?';
          this.notes =
            'This will disable mandatory location while schedule appointment.';
        } else {
          this.message =
            'Are you sure you want to enable the “Mandatory Location” option?';
          this.notes =
            'This will make location mandatory while schedule appointment.';
        }
        break;
      case 'mandatoryNotes':
        if (this.leadSettingsForm.value.mandatoryNotes) {
          this.message =
            'Are you sure you want to disable the “Mandatory Notes” option?';
          this.notes = 'this will make notes mandatory for selected options.';
        } else {
          this.message =
            'Are you sure you want to enable the “Mandatory Notes” option?';
          this.notes =
            'This will disable mandatory notes for selected options.';
        }
        break;
      case 'stickyAgent':
        if (this.leadSettingsForm.value.stickyAgent) {
          this.message = 'Are you sure you want to disable the “Sticky Agent”';
          this.notes =
            'This will not override the duplicate settings and assign duplicate leads to orginal lead owner';
        } else {
          this.message = 'Are you sure you want to enable the “Sticky Agent”';
          this.notes =
            'This will override the duplicate settings and assign duplicate leads to orginal lead owner';
        }
        break;
      case 'mandatoryProjects':
        if (this.leadSettingsForm.value.mandatoryProjects) {
          this.message =
            'Are you sure you want to disable the “Mandatory Projects” option?';
          this.notes =
            'this will disable projects mandatory for selected options.';
        } else {
          this.message =
            'Are you sure you want to enable the “Mandatory Projects” option?';
          this.notes =
            'This will make mandatory projects for selected options.';
        }
        break;
      case 'mandatoryProperty':
        if (this.leadSettingsForm.value.mandatoryProperty) {
          this.message =
            'Are you sure you want to disable the “Mandatory Property” option?';
          this.notes =
            'this will disable property mandatory for selected options.';
        } else {
          this.message =
            'Are you sure you want to enable the “Mandatory Property” option?';
          this.notes =
            'This will make mandatory property for selected options.';
        }
        break;
      case 'allowDuplicateLeads':
        if (this.leadSettingsForm?.value?.allowDuplicateLeads) {
          this.message =
            'Are you sure you want to disable the "Allow Duplicates” option?';
          this.notes =
            'This will disable allowing duplicates into CRM (Disabling will not affect leads which are already in the CRM) ';
        } else {
          this.message =
            'Are you sure you want to enable the “Allow Duplicates” option?';
          this.notes =
            'This will allow lead duplicates into the CRM with specified options as selected';
        }
        break;
      case 'subStatus':
        if (childTypeCount > 0) {
          this.message =
            'Cannot delete as this sub-status has lead associated with it';
          this.notes =
            'Reassign the leads to a different status to proceed further ';
        } else {
          this.message = 'Are you sure you want to delete this sub-status?';
          this.notes = 'This process permanent cannot be undone ';
        }
        break;
      case 'deleteGroup':
        this.message = 'Are you sure You want delete the Group?';
        this.notes = 'The deleted data Cannot be reverted back.';
        break;
    }
  }

  onAddEditSubStatus(category: string, selectedStatus: any, subStatus?: any) {
    this.doesExistSubStatus = false;
    this.subStatusName.reset();
    this.selectedStatus = selectedStatus;
    if (category == 'edit') {
      this.updatingSubStatusId = subStatus.id;
      if (this.subStatusName) {
        this.subStatusName.setValue(subStatus.displayName);
      }
    } else {
      this.updatingSubStatusId = null;

      if (this.subStatusName) {
        this.subStatusName.setValue(null);
      }
    }

    this.masterLeadStatus = this.masterLeadStatus?.map((status: any) => {
      if (status?.id !== selectedStatus?.id) {
        return {
          ...status,
          isFormVisible: false,
        };
      }
      return status;
    });
    selectedStatus.isFormVisible = true;

    setTimeout(() => {
      var subStatusInput = document.getElementById('subStatusInput');
      if (subStatusInput) {
        subStatusInput.focus();
      }
    });
  }

  doesSubStatusExist() {
    if (this.subStatusName.value) {
      this.store.dispatch(new ExistSubStatus(this.subStatusName.value));
      this.store.dispatch(new LoaderHide());
      this.store
        .select(getSubStatusExist)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: boolean) => {
          this.doesExistSubStatus = data;
          this.subStatusName?.setErrors(
            this.doesExistSubStatus ? { alreadyExist: true } : null
          );
        });
    }
  }

  subStatusSave() {
    if (this.subStatusName.invalid || this.doesExistSubStatus) {
      this.subStatusName.markAsTouched({ onlySelf: true });
      return;
    }
    let payload: any = {
      status: this.subStatusName.value,
      displayName: this.subStatusName.value,
      actionName: this.subStatusName.value,
      baseId: this.selectedStatus.id, // Parent - Id
      masterLeadStatusBaseId: this.selectedStatus.masterLeadStatusId, // Parent - masterLeadStatusId
      level: 1,
    };
    if (this.updatingSubStatusId) {
      payload = {
        ...payload,
        id: this.updatingSubStatusId,
      };
      this.store.dispatch(new UpdateCustomSubStatus(payload));
    } else {
      this.store.dispatch(new CreateSubStatus(payload));
    }
    if (this.isCustomStatusEnabled) {
      this.store.dispatch(new FetchCustomStatus());
    }
    this.store.dispatch(new FetchLeadStatusList());
    this.store.dispatch(new FetchLeadCount());
    this.store
      .select(getStatusIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        const filteredStatuses = JSON.parse(
          localStorage.getItem('masterleadstatus')
        )
          ?.filter(
            (status: any) =>
              status?.actionName !== 'New' &&
              status?.actionName !== 'Pending' &&
              status?.actionName !== 'Invoiced'
          )
          .sort((a: any, b: any) => a?.actionName?.localeCompare(b?.actionName));
        this.store
          .select(getSubStatusLeadCount)
          .pipe(take(1))
          .subscribe((leadCountData: any) => {
            this.masterLeadStatus = filteredStatuses?.map((item: any) => {
              if (item.childTypes && item.childTypes.length) {
                item.childTypes = item.childTypes.map((child: any) => {
                  const matchedData = leadCountData.find(
                    (d: any) => d.id === child.id
                  );
                  if (matchedData) {
                    return { ...child, leadCount: matchedData.leadCount };
                  }
                  return child;
                });
              }
              return item;
            });
          });
      });
    this.subStatusName.reset();
  }

  deleteSubStatus() {
    this.store.dispatch(new DeleteSubStatus(this.updatingSubStatusId));
    this.store
      .select(getStatusIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        const filteredStatuses = JSON.parse(
          localStorage.getItem('masterleadstatus')
        )
          ?.filter(
            (status: any) =>
              status?.actionName !== 'New' &&
              status?.actionName !== 'Pending' &&
              status?.actionName !== 'Invoiced'
          )
          .sort((a: any, b: any) => a?.actionName?.localeCompare(b?.actionName));
        this.store
          .select(getSubStatusLeadCount)
          .pipe(take(1))
          .subscribe((leadCountData: any) => {
            this.masterLeadStatus = filteredStatuses?.map((item: any) => {
              if (item.childTypes && item.childTypes.length) {
                item.childTypes = item.childTypes.map((child: any) => {
                  const matchedData = leadCountData.find(
                    (d: any) => d.id === child.id
                  );
                  if (matchedData) {
                    return { ...child, leadCount: matchedData.leadCount };
                  }
                  return child;
                });
              }
              return item;
            });
          });
      });
    this.modalRef.hide();
  }

  onReassign() {
    this.router.navigate(['leads', 'manage-leads'], {
      queryParams: {
        SubStatusIds: JSON.stringify([this.updatingSubStatusId]),
        isNavigatedFromLeadSettings: true,
      },
    });
    this.modalRef.hide();
  }

  onSave() {
    if (this.settingType == 'deleteGroup') {
      return;
    }
    let payload: any;
    const settingsData: any = this.leadSettingsForm.value;
    if (this.settingType == 'allowDuplicateLeads') {
      let payload: any = {
        ...this.duplicateSettingData,
        isFeatureAdded: settingsData?.allowDuplicateLeads,
      };
      this.store.dispatch(new UpdateAllowDuplicatesSettings(payload));
    } else if (this.settingType == 'mandatoryNotes') {
      payload = {
        ...this.settingData?.leadNotesSetting,
        isNotesMandatoryEnabled: settingsData?.mandatoryNotes,
      };
      if (!settingsData?.mandatoryNotes) {
        payload = {
          ...payload,
          isNotesMandatoryOnAddLead: false,
          isNotesMandatoryOnSiteVisitDone: false,
          isNotesMandatoryOnMeetingDone: false,
          isNotesMandatoryOnUpdateLead: false,
        };
      } else if (settingsData?.mandatoryNotes) {
        payload = {
          ...payload,
          isNotesMandatoryOnAddLead: true,
          isNotesMandatoryOnSiteVisitDone: true,
          isNotesMandatoryOnMeetingDone: true,
          isNotesMandatoryOnUpdateLead: true,
        };
      }
      this.store.dispatch(new UpdateMandatoryNotesSettings(payload));
    } else if (this.settingType == 'mandatoryProjects') {
      payload = {
        ...this.settingData?.leadProjectSetting,
        isProjectMandatoryEnabled: settingsData?.mandatoryProjects,
      };
      if (!settingsData?.mandatoryProjects) {
        payload = {
          ...payload,
          isProjectMandatoryOnMeetingDone: false,
          isProjectMandatoryOnSiteVisitDone: false,
          isProjectMandatoryOnBooking: false,
        };
      } else if (settingsData?.mandatoryProjects) {
        payload = {
          ...payload,
          isProjectMandatoryOnMeetingDone: true,
          isProjectMandatoryOnSiteVisitDone: true,
          isProjectMandatoryOnBooking: true,
        };
      }
      this.store.dispatch(new UpdateMandatoryProjectsSettings(payload));
    } else if (this.settingType == 'mandatoryProperty') {
      payload = {
        ...this.settingData?.leadPropertySetting,
        isPropertyMandatoryEnabled: settingsData?.mandatoryProperty,
      };
      if (!settingsData?.mandatoryProperty) {
        payload = {
          ...payload,
          isPropertyMandatoryOnMeetingDone: false,
          isPropertyMandatoryOnSiteVisitDone: false,
          isPropertyMandatoryOnBooking: false,
        };
      } else if (settingsData?.mandatoryProperty) {
        payload = {
          ...payload,
          isPropertyMandatoryOnMeetingDone: true,
          isPropertyMandatoryOnSiteVisitDone: true,
          isPropertyMandatoryOnBooking: true,
        };
      }
      this.store.dispatch(new UpdateMandatoryPropertySettings(payload));
    } else {
      let payload: any = {
        ...this.settingData,
        isLeadsExportEnabled: settingsData.exportLeads,
        isLeadRotationEnabled: settingsData?.isLeadRotationEnabled,
        isPastDateSelectionEnabled: settingsData.isPastDateSelectionEnabled,
        isLeadSourceEditable: settingsData.leadsSourceUpdate,
        hasInternationalSupport: settingsData.internationalNo,
        isCallDetectionActivated: settingsData.callDetection,
        isDualOwnershipEnabled: settingsData.dualOwnership,
        isMaskedLeadContactNo: !settingsData.maskLeadContactNo,
        isStickyAgentEnabled: settingsData.stickyAgent,
        isTeamLeadRotationEnabled: settingsData.isLeadRetentionEnabled,
        isCustomLeadFormEnabled: settingsData?.isCustomLeadFormEnabled,
        isWhatsAppDeepIntegration: settingsData?.whatsAppDeepIntegration,
        generalSettings: {
          isLocationMandatory: settingsData?.isLocationMandatory,
        },
      };
      this.store.dispatch(new UpdateGlobalSettings(payload));
    }
    this.modalRef.hide();
    this.settingType = null;
  }

  closePopup(isClickCancel: boolean = false) {
    this.patchValues(isClickCancel);
    this.modalRef.hide();
    this.deleteGroupId = '';
  }

  saveAllowDuplicates(): void {
    const payload: Record<string, boolean> = {
      ...this.duplicateSettingData,
    };
    this.allowDuplicateOptions.forEach((option: AllowDuplicateOption) => {
      payload[option.key] = option.value;
    });
    this.store.dispatch(new UpdateAllowDuplicatesSettings(payload));
    this.isAllowDuplicateOptionsValueChanged = false;
  }

  resetAllowDuplicateOptions(): void {
    this.allowDuplicateOptions.forEach((option) => {
      option.value = this.settingData?.duplicateFeatureInfo?.[option.key];
      option.disabled =
        this.settingData?.duplicateFeatureInfo?.allowAllDuplicates;
      if (option.key == 'allowAllDuplicates') {
        option.disabled = false;
      }
    });
    this.allowDuplicateOptions = [...this.allowDuplicateOptions];
    this.isAllowDuplicateOptionsValueChanged = false;
  }

  allowDuplicateOptionSelected(option: AllowDuplicateOption): void {
    this.isAllowDuplicateOptionsValueChanged = true;
    if (option.key == 'allowAllDuplicates' && !option.value) {
      this.allowDuplicateOptions.forEach((option: AllowDuplicateOption) => {
        option.value = true;
      });
      this.allowDuplicateOptions = [...this.allowDuplicateOptions];
      return;
    }
    option.value = !option.value;
    if (this.allowDuplicateOptions.slice(1).every((option) => option.value)) {
      return;
    } else {
      this.allowDuplicateOptions[0].value = false;
    }
  }

  saveMandatoryProjects(): void {
    const payload: Record<string, boolean> = {
      ...this.settingData?.leadProjectSetting,
    };
    this.mandatoryProjectOptions.forEach((option: MandatoryProjectOption) => {
      payload[option.key] = option.value;
    });
    this.store.dispatch(new UpdateMandatoryProjectsSettings(payload));
    this.isMandatoryProjectOptionsValueChanged = false;
  }

  resetMandatoryProjectOptions(): void {
    this.mandatoryProjectOptions.forEach((option) => {
      option.value = this.settingData?.leadProjectSetting?.[option.key];
      option.disabled =
        this.settingData?.leadProjectSetting?.isProjectMandatoryEnabled;
      if (option.key == 'isProjectMandatoryEnabled') {
        option.disabled = false;
      }
    });
    this.mandatoryProjectOptions = [...this.mandatoryProjectOptions];
    this.isMandatoryProjectOptionsValueChanged = false;
  }

  mandatoryProjectOptionSelected(option: MandatoryProjectOption): void {
    this.isMandatoryProjectOptionsValueChanged = true;
    option.value = !option.value;
  }

  saveMandatoryProperty(): void {
    const payload: Record<string, boolean> = {
      ...this.settingData?.leadPropertySetting,
    };
    this.mandatoryPropertyOptions.forEach((option: MandatoryPropertyOption) => {
      payload[option.key] = option.value;
    });
    this.store.dispatch(new UpdateMandatoryPropertySettings(payload));
    this.isMandatoryPropertyOptionsValueChanged = false;
  }

  resetMandatoryPropertyOptions(): void {
    this.mandatoryPropertyOptions.forEach((option) => {
      option.value = this.settingData?.leadPropertySetting?.[option.key];
      option.disabled =
        this.settingData?.leadPropertySetting?.isPropertyMandatoryEnabled;
      if (option.key == 'isPropertyMandatoryEnabled') {
        option.disabled = false;
      }
    });
    this.mandatoryPropertyOptions = [...this.mandatoryPropertyOptions];
    this.isMandatoryPropertyOptionsValueChanged = false;
  }

  mandatoryPropertyOptionSelected(option: MandatoryPropertyOption): void {
    this.isMandatoryPropertyOptionsValueChanged = true;
    option.value = !option.value;
  }

  saveMandatoryNotes(): void {
    const payload: Record<string, boolean> = {
      ...this.settingData?.leadNotesSetting,
    };
    this.mandatoryNotesOptions.forEach((option: MandatoryNotesOption) => {
      payload[option.key] = option.value;
    });
    this.store.dispatch(new UpdateMandatoryNotesSettings(payload));
    this.isMandatoryNotesOptionsValueChanged = false;
  }

  resetMandatoryNotesOptions(): void {
    this.mandatoryNotesOptions.forEach((option) => {
      option.value = this.settingData?.leadNotesSetting?.[option.key];
      option.disabled =
        this.settingData?.leadNotesSetting?.isNotesMandatoryEnabled;
      if (option.key == 'isNotesMandatoryEnabled') {
        option.disabled = false;
      }
    });
    this.mandatoryNotesOptions = [...this.mandatoryNotesOptions];
    this.isMandatoryNotesOptionsValueChanged = false;
  }

  mandatoryNotesOptionSelected(option: MandatoryNotesOption): void {
    this.isMandatoryNotesOptionsValueChanged = true;
    option.value = !option.value;
  }

  onPriorityOptionChanged(): void {
    this.isPriorityListChanged = true;
    const selectedValues: string[] = [
      this.leadSettingsForm.get('priority1')?.value?.value,
      this.leadSettingsForm.get('priority2')?.value?.value,
      this.leadSettingsForm.get('priority3')?.value?.value,
      this.leadSettingsForm.get('priority4')?.value?.value,
      this.leadSettingsForm.get('priority5')?.value?.value,
      this.leadSettingsForm.get('priority6')?.value?.value,
    ];
    this.priorityOptions = this.allPriorityOptions.filter(
      (option: any) => selectedValues.indexOf(option.value) == -1
    );
  }

  clearPriorityValue(i: number): void {
    this.isPriorityListChanged = true;
    const clearedOption: { label: string; value: string } =
      this.leadSettingsForm.get('priority' + i)?.value;
    if (!clearedOption) {
      return;
    }
    this.leadSettingsForm.get('priority' + i)?.setValue(null);
    this.onPriorityOptionChanged();
  }

  savePriorityList(): void {
    if (!this.zoneLocationEnabled) {
      ['priority4', 'priority5', 'priority6'].forEach((property: string) => {
        if (this.leadSettingsForm.value.hasOwnProperty(property)) {
          delete this.leadSettingsForm.value[property];
        }
        if (this.leadSettingsForm.controls.hasOwnProperty(property)) {
          delete this.leadSettingsForm.controls[property];
        }
      });
    }
    if (!this.leadSettingsForm.valid) {
      validateAllFormFields(this.leadSettingsForm);
      return;
    }

    let modules: any[] = [];
    let values = [
      this.leadSettingsForm.get('priority1').value,
      this.leadSettingsForm.get('priority2').value,
    ];
    if (this.zoneLocationEnabled && !this.settingData?.shouldEnablePropertyListing) {
      values.push(
        this.leadSettingsForm.get('priority3').value,
        this.leadSettingsForm.get('priority4').value,
        this.leadSettingsForm.get('priority5').value
      );
    } else if (!this.zoneLocationEnabled && this.settingData?.shouldEnablePropertyListing) {
      values.push(
        this.leadSettingsForm.get('priority3').value,
      );
    } else if (this.zoneLocationEnabled && this.settingData?.shouldEnablePropertyListing) {
      values.push(
        this.leadSettingsForm.get('priority3').value,
        this.leadSettingsForm.get('priority4').value,
        this.leadSettingsForm.get('priority5').value,
        this.leadSettingsForm.get('priority6').value
      );
    }
    values.forEach((priority: any, i: number) => {
      modules.push({
        id: priority.value,
        name: priority.label,
        priority: i + 1,
      });
    });
    this.store.dispatch(
      new updatePriorityList({
        modules,
      })
    );
    this.isPriorityListChanged = false;
  }

  resetPriorityListValues(): void {
    const sortOrder = ['Project', 'Location', 'Zone', 'City', 'SubSource'];
    this.allPriorityOptions.sort((optionA: any, optionB: any) => {
      const indexOptionA = sortOrder.indexOf(optionA.label);
      const indexOptionB = sortOrder.indexOf(optionB.label);
      return indexOptionA - indexOptionB;
    });

    this.leadSettingsForm
      .get('priority1')
      ?.setValue(this.allPriorityOptions?.[0]);
    this.leadSettingsForm
      .get('priority2')
      ?.setValue(this.allPriorityOptions?.[1]);
    this.leadSettingsForm
      .get('priority3')
      ?.setValue(this.allPriorityOptions?.[2]);
    this.leadSettingsForm
      .get('priority4')
      ?.setValue(this.allPriorityOptions?.[3]);
    this.leadSettingsForm
      .get('priority5')
      ?.setValue(this.allPriorityOptions?.[4]);
    this.leadSettingsForm
      .get('priority6')
      ?.setValue(this.allPriorityOptions?.[5]);

    this.priorityOptions = [];

    this.savePriorityList();
  }

  getTeamName(teamId: string) {
    return this.allTeams?.filter((team) => team.id === teamId)[0]?.teamName;
  }

  timeToMinutes(timeString: string): number {
    if (timeString) {
      const [hours, minutes] = timeString?.split(':').map(Number);
      const totalMinutes = hours * 60 + minutes;
      return totalMinutes;
    }
    return null;
  }

  getDays(days: any[]): string {
    return days?.length === 7
      ? 'Everyday'
      : days
        ?.map(
          (dayValue) => DAYS?.find((day) => day.value === dayValue)?.shortName
        )
        ?.filter(Boolean)
        ?.join(', ') || '--';
  }

  getStatusInfo(statuses: any[]): { visible: string; remainingCount: number } {
    if (statuses?.length) {
      const visibleStatuses = statuses
        .slice(0, 1)
        .map((status: any) => status?.displayName)
        .join(', ');
      const remainingCount = statuses.length - 1;

      this.remainingStatuses = statuses.slice(1);

      return {
        visible: visibleStatuses,
        remainingCount: remainingCount > 0 ? remainingCount : 0,
      };
    }

    this.remainingStatuses = [];
    return { visible: '---', remainingCount: 0 };
  }

  openStatusPopup(statuses: any, Name: string): void {
    this.popUpName = Name;
    this.showStatusList = statuses;
    this.modalService.show(this.statusModal, {
      class: 'modal-400 modal-dialog-centered ip-modal-unset',
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
