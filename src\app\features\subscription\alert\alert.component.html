<div>
    <div class="bg-coal d-flex justify-content-between px-8 py-16">
        <div>
        </div>
        <h4 class="fw-600 text-white">Your Leadrat CRM subscription is about to expire</h4>
        <div class="icon ic-xs ic-close ic-white cursor-pointer" (click)="modalRef.hide()"></div>
    </div>

    <div class="justify-content-center flex-col align-center w-100 mx-auto p-16">
        <div class="max-w-800 align-center-col">
            <img src="../../../../assets/images/whatsapp-notification.svg" alt="img" />
            <h4 class="mt-16">We noticed that your subscription is nearing its expiry.</h4>
            <h4 class="mt-6"> Please ensure timely renewal to avoid any disruption in your services.</h4>

            <h4 class="text-decoration-underline fw-600 mt-16">Current Plan</h4>
            <div class="mt-16 w-100 border br-4">
                <div class="d-flex bg-light-slate">
                    <div class="w-33 border-right border-bottom py-10 px-12">No. of License</div>
                    <div class="w-33 border-right border-bottom py-10 px-12">Expiry Date</div>
                    <div class="w-33 border-bottom py-10 px-12">Days Remaining</div>
                </div>
                <div class="d-flex">
                    <div class="w-33 border-right py-10 px-12">{{ userData?.subscriptionDetails?.totalSoldLicenses}}
                    </div>
                    <div class="w-33 border-right py-10 px-12">{{userData?.subscriptionDetails?.licenseValidity ?
                        getTimeZoneDate(userData?.subscriptionDetails?.licenseValidity,
                        userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'): '' }}</div>
                    <div class="w-33 py-10 px-12">{{userData?.subscriptionDetails?.validDays}}</div>
                </div>
            </div>
            <div class="mt-16 align-center-col">
                <div class="d-flex flex-wrap">
                    <h4> In case of non-renewal, your account will be </h4>
                    <h4 class="fw-600 px-4 text-red-350"> Suspended in {{userData?.subscriptionDetails?.validDays}} days
                        {{userData?.subscriptionDetails?.licenseValidity ?
                        getTimeZoneDate(userData?.subscriptionDetails?.licenseValidity,
                        userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYearText'): ''}}</h4>
                    <h4>
                        automatically.</h4>
                </div>
                <h4> Please contact your Leadrat Representative for renewal at:</h4>
                <div class="d-flex">
                    <h4 class="fw-600 align-center pr-4"> +91 ********** </h4>
                    <h4>or</h4>
                    <h4 class="fw-600 pl-4">+91 **********</h4>
                </div>
                <div class="btn-coal align-center mt-16" (click)=contactNow()><span
                        class="icon ic-xxs ic-phone-ring-solid ic-white mr-6"></span>Contact Now</div>
            </div>
        </div>
    </div>

</div>