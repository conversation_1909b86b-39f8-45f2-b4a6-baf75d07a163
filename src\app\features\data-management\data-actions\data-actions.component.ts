import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { skipWhile, take, takeUntil } from 'rxjs';

import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { DataTopFilter } from 'src/app/core/interfaces/data-management.interface';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import { DataAssigntoComponent } from 'src/app/features/data-management/data-assignto/data-assignto.component';
import { LeadCallComponent } from 'src/app/features/leads/lead-call/lead-call.component';
import { LeadsEmailShareComponent } from 'src/app/features/leads/leads-email-share/leads-email-share.component';
import { LeadsTemplateShareComponent } from 'src/app/features/leads/leads-template-share/leads-template-share.component';
import {
  CommonClickToCall,
  FetchVNAssignment,
  FetchVirtualNos,
} from 'src/app/reducers/Integration/integration.actions';
import {
  getVNAssignment,
  getVNAssignmentIsLoading,
  getVirtualNos,
  getVirtualNosIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  BulkDeleteData,
  CommunicationDataCount,
  CommunicationDataMessage,
  PermanentDeleteData,
  RestoreData,
} from 'src/app/reducers/data/data-management.actions';
import {
  DataManagementFilters,
  getDataCommunication,
  getDataCommunicationIsLoading,
  getDataFiltersPayload,
  getDataTopFilters,
} from 'src/app/reducers/data/data-management.reducer';
import { getEmailSMTPByUserId } from 'src/app/reducers/email/email-settings.reducer';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { NotificationCall } from 'src/app/reducers/notification-info/notification-info.action';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchIVRSettingListById } from 'src/app/reducers/teams/teams.actions';
import {
  getIVRSettingById,
  getIVRSettingByIdIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UnassignedComponent } from 'src/app/shared/components/unassigned/unassigned.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'data-actions',
  templateUrl: './data-actions.component.html',
})
export class DataActionsComponent implements OnInit, OnDestroy {
  @ViewChild('mobileNotification') mobileNotification!: TemplateRef<any>;
  @ViewChild('noEmailModal') noEmailModal!: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() data: any;
  @Input() isLeadPreviewOpen: boolean = false;
  @Output() changeSelection: EventEmitter<string> = new EventEmitter<string>();
  params: any;
  leadSource: string;
  callRecordingDetails: Array<Object>;
  filteredCallLogs: any;
  ivrRecordingDetails: any = [];
  fieldSections: any = [
    {
      displayName: 'Call Recording(s)',
      isHide: false,
      icon: 'ic-notes-pen',
      name: 'call',
    },
    {
      displayName: 'IVR Recording(s)',
      isHide: false,
      icon: 'ic-circle-exclamation-solid',
      name: 'ivr',
    },
  ];
  moment = moment;
  agents: boolean = false;
  userPhoneNo: string;
  assignedToUserId: string;
  agentList: Array<Object>;
  agentPhoneNo: Array<any>;
  isIVREnabled: boolean = false;
  loader: AnimationOptions = { path: 'assets/animations/circle-loader.json' };
  userPermissions: string;
  isLoading: boolean = true;
  canEditData: boolean = false;
  canDeleteData: boolean = false;
  canAssignData: boolean = false;
  ivrAccountCount: any;
  EMPTY_GUID = EMPTY_GUID;
  isConvertedView: boolean = true;
  topFilters: DataTopFilter[] = [];
  filtersPayload: DataManagementFilters;
  @Input() selectedLead: any;
  @Input() isMobileView: boolean = false;
  @Input() isCardView: boolean = false;
  globalSettingsDetails: any;
  userId: any;
  userCallType: any;
  VNAssignment: any;
  virtualNosList: any;
  selectedCallType: any;
  contactAdminType: string;
  clickedDataId: string;
  selectedVirtualNo: null;
  getTimeZoneDate = getTimeZoneDate;
  canPermanentDelete: boolean = false;
  dataCommunication: Object;
  isDataCommunicationLoading: boolean = false;
  @Input() showCommunicationCount: boolean = false;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    public router: Router,
    private shareDataService: ShareDataService,
    public NotifyModalRef: BsModalRef,
    public trackingService: TrackingService
  ) {
    this.userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this.userPermissions = localStorage.getItem('userPermissions');
  }
  ngOnInit() {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canAssignData = permissionsSet.has('Permissions.Prospects.Assign');
        this.canEditData = permissionsSet.has('Permissions.Prospects.Update');
        this.canDeleteData = permissionsSet.has('Permissions.Prospects.Delete');
        this.canPermanentDelete = permissionsSet.has(
          'Permissions.Prospects.PermanentDelete'
        );
      });

    if (this.data) {
      this.params = {
        data: this.data,
      };
    }
    this.filteredCallLogs = this.params?.data?.prospectCallLogs?.filter(
      (call: any) => call?.callRecordingUrl
    );

    this.store
      .select(getDataFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: DataManagementFilters) => {
        this.filtersPayload = filters;
        this.checkFilterView();
      });

    this.store
      .select(getDataTopFilters)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (dataTopFilters: {
          dataTopLevelFilters: DataTopFilter[];
          isTopFiltersLoadedOnce: boolean;
        }) => {
          this.topFilters = dataTopFilters?.dataTopLevelFilters;
          this.checkFilterView();
        }
      );
    this.store
      .select(getDataCommunication)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        this.dataCommunication = res;
      });

    this.store
      .select(getDataCommunicationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isDataCommunicationLoading = isLoading;
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
      });

    this.store
      .select(getIVRSettingById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userCallType = data?.callThrough;
      });

    this.store
      .select(getVNAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.VNAssignment = item;
      });
  }

  agInit(params: any): void {
    this.params = params;
    this.showCommunicationCount = params.showCommunicationCount;

  }

  refresh(params: any): boolean {
    this.showCommunicationCount = params.showCommunicationCount;
    return true;
  }

  /**
   * Function to check if the top level filter selected is Deleted or Converted
   */
  checkFilterView(): void {
    this.isConvertedView =
      this.topFilters?.filter(
        (filter: DataTopFilter) => filter?.isConverted
      )?.[0]?.enumValue === this.filtersPayload?.ProspectVisiblity;
  }

  openNotesModal() {
    this.router.navigate([`/data/data-preview/${this.params?.data?.id}`], {
      queryParams: { notes: true },
    });
  }

  editData() {
    const id: string = this.params?.data?.id;
    this.router.navigate(['/data/edit-data/' + id]);
  }

  /**
   * Function to open data preview
   */
  openDataHistory(): void {
    this.router.navigate([`/data/data-preview/${this.params?.data?.id}`]);
  }

  async checkToCall(
    event: any,
    virtualNo: TemplateRef<any>,
    askBefore: TemplateRef<any>,
    contactAdmin: TemplateRef<any>,
    chooseToCallType: TemplateRef<any>,
    dataId: string
  ) {
    this.clickedDataId = dataId;
    event.stopPropagation();
    await this.store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    if (this.globalSettingsDetails.callSettings.callType === 2) {
      this.store.dispatch(new FetchIVRSettingListById(this.userId));
      await this.store
        .select(getIVRSettingByIdIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => {
            return isLoading;
          }),
          take(1)
        )
        .toPromise();
      switch (this.userCallType) {
        case 0:
          this.openAskBeforeCall(askBefore);
          break;
        case 1:
          this.openIVROnly(virtualNo, contactAdmin, chooseToCallType);
          break;
        case 2:
          this.initCall();
          break;
        default:
          this.openAskBeforeCall(askBefore);
          break;
      }
    } else {
      this.initCall();
    }
  }

  openAskBeforeCall(askBefore: TemplateRef<any>) {
    this.modalRef = this.modalService.show(askBefore, {
      class: 'modal-350 top-modal ip-modal-unset',
    });
  }

  async openIVROnly(
    virtualNo: TemplateRef<any>,
    contactAdmin: TemplateRef<any>,
    chooseToCallType: TemplateRef<any>
  ) {
    if (this.userPermissions?.includes('IVRCall')) {
      if (this.globalSettingsDetails?.isIVROutboundEnabled) {
        if (this.globalSettingsDetails?.isVirtualNumberRequiredForOutbound) {
          this.store.dispatch(new FetchVNAssignment(this.clickedDataId));
          await this.store
            .select(getVNAssignmentIsLoading)
            .pipe(
              skipWhile((isLoading: boolean) => {
                return isLoading;
              }),
              take(1)
            )
            .toPromise();
          if (this.VNAssignment?.isVirtualNumberAssigned) {
            this.ivrClickToCheck(
              this.VNAssignment?.virtualNumber,
              chooseToCallType
            );
          } else if (this.VNAssignment?.shouldFetchVirtualNumbers) {
            this.store.dispatch(new FetchVirtualNos());
            await this.store
              .select(getVirtualNosIsLoading)
              .pipe(
                skipWhile((isLoading: boolean) => {
                  return isLoading;
                }),
                take(1)
              )
              .toPromise();
            this.checkVirtualNumber(virtualNo);
          }
        } else {
          this.ivrClickToCheck(null, chooseToCallType);
        }
      } else {
        this.openContactAdmin(contactAdmin, 'NoPrimaryOutBound');
      }
    } else {
      this.openContactAdmin(contactAdmin, 'UserPermission');
    }
  }

  initCall() {
    if (this.params.data.alternateContactNo) {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      let initialState: any = {
        data: { ...this.params.data, isData: true },
      };
      this.modalRef = this.modalService.show(
        LeadCallComponent,
        Object.assign(
          {},
          {
            class: 'modal-400 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
    } else if (this.globalSettingsDetails.isMobileCallEnabled) {
      let payload = {
        contactNo: this.params.data.contactNo,
        userId: this.userId,
        name: this.params.data.name,
      };
      this.store.dispatch(new NotificationCall(payload));
      this.mobileNotifyInfo();
      this.onInitiateCall(1);
      this.modalRef.hide();
    } else {
      location.href = 'tel:' + this.params.data.contactNo;
      this.onInitiateCall(1);
    }
  }

  mobileNotifyInfo() {
    let initialState: any = {
      class: 'modal-400 top-modal ph-modal-unset',
    };
    this.NotifyModalRef = this.modalService.show(
      this.mobileNotification,
      initialState
    );
    const intervalId = setInterval(() => {
      this.NotifyModalRef.hide();
      clearInterval(intervalId);
    }, 5000);
  }

  openDialerPad() {
    location.href = 'tel:' + this.params.data.contactNo;
    this.modalRef.hide();
  }

  openContactAdmin(contactAdmin: TemplateRef<any>, type: string) {
    switch (type) {
      case 'UserPermission':
        this.contactAdminType =
          "You don't have permission to call through IVR, Please contact your admin";
        break;
      case 'NoPrimaryOutBound':
        this.contactAdminType =
          'No primary IVR account found in Integration, please integrate an "Outbound" account with valid token and make it primary';
        break;
      case 'NoAgents':
        this.contactAdminType =
          'No Agents are registered with your IVR Service Provider. Please contact your Admin.';
        break;
    }

    this.modalRef = this.modalService.show(contactAdmin, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
  }

  checkVirtualNumber(virtualNo: any) {
    this.store
      .select(getVirtualNos)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.virtualNosList = item;
        this.modalRef = this.modalService.show(virtualNo, {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
        });
      });
  }

  ivrClickToCheck(virtualNo: string, chooseToCallType: TemplateRef<any>) {
    if (this.params.data.alternateContactNo) {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.selectedCallType = this.params.data.contactNo;
      this.modalRef = this.modalService.show(chooseToCallType, {
        class: 'modal-400 top-modal ph-modal-unset',
      });
    } else {
      this.ivrClickToCall(virtualNo, false);
    }
  }

  ivrClickToCall(virtualNo: string, alternateContactNo: boolean = false) {
    this.onInitiateCall(1);
    let payload = {
      destinationNumber: !alternateContactNo
        ? this.params.data.contactNo
        : this.selectedCallType,
      agentNumber: this.userPhoneNo,
      callerIdOrVirtualNumber: virtualNo,
      prospectId: this.params.data.id,
      userId: this.userId,
    };
    this.store.dispatch(new CommonClickToCall(payload));
    this.store.dispatch(new LoaderHide());
    this.modalRef.hide();
  }

  onInitiateCall(contactType: number) {
    const payload: any = {
      prospectId: this.params.data?.id,
      contactType: contactType,
    };
    let payloadCount = {
      id: this.params.data?.id,
      contactType: contactType,
    };
    this.store.dispatch(
      new CommunicationDataCount(payloadCount.id, payloadCount)
    );
    this.store.dispatch(new CommunicationDataMessage(payload));
    this.store.dispatch(new LoaderHide());
  }

  closeModal() {
    this.modalRef.hide();
    this.selectedVirtualNo = null;
  }

  openAudioPlayer(event: any, aP: TemplateRef<any>) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (
      this.params?.data?.callRecordingUrls &&
      Object.entries(this.params.data.callRecordingUrls).length > 0
    ) {
      this.ivrRecordingDetails = Object.entries(
        this.params?.data?.callRecordingUrls
      )
        .map((item: any) => {
          return {
            years: item[0],
            yearData: Object.entries(item[1])
              .reverse()
              .map((item1: any) => {
                return {
                  months: item1[0],
                  monthData: Object.keys(item1[1])
                    .reverse()
                    .map((key) => {
                      let audioDate = key.toString();
                      let audioFile = item1[1][key];
                      return { date: audioDate, audioUrl: audioFile };
                    }),
                };
              }),
          };
        })
        .reverse();
    } else {
      this.fieldSections = this.fieldSections.filter(
        (field: any) => field?.name !== 'ivr'
      );
    }
    if (
      this.params?.data?.prospectCallLogs &&
      this.params?.data?.prospectCallLogs?.length > 0
    ) {
      this.callRecordingDetails = this.groupByYearAndMonth(
        this.filteredCallLogs
      );
    } else {
      this.fieldSections = this.fieldSections.filter(
        (field: any) => field?.name !== 'call'
      );
    }

    this.modalRef = this.modalService.show(aP, {
      class: 'right-modal modal-350 ph-modal-unset',
    });
  }

  pauseOtherAudio(audioPlayer: any) {
    let audioElements = document.getElementsByTagName('audio');
    for (let i = 0; i < audioElements.length; i++) {
      if (audioElements[i] !== audioPlayer) {
        audioElements[i].pause();
      }
    }
  }

  deleteData(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'data',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-350 top-modal',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new BulkDeleteData([data?.id]));
          // this.store.dispatch(new DeleteData(data?.id));
        }
      });
    }
    this.shareDataService.triggerApiCall(true);
  }

  permanentDelete(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: 'Delete data permanently?',
        message: `You are about to delete the data "<b>${data?.name}</b>" permanently. 🚫`,
        description:
          'Delete all information associated with this data. Once the data is deleted, the action is irreversible, and data recovery will not be possible.',
        title: data?.name,
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new PermanentDeleteData([data?.id]));
        }
      });
    }
    this.shareDataService.triggerApiCall(true);

  }

  openAssignModal() {
    let initialState: any = {
      data: this.params.data,
    };
    this.modalRef = this.modalService.show(DataAssigntoComponent, {
      class: 'modal-400 right-modal ph-modal-unset',
      initialState,
    });
  }

  openTemplateModal(event: any, shareType: string) {
    event.stopPropagation();

    if (this.params.data.assignTo === EMPTY_GUID) {
      this.openUnassignModal();
      return;
    }

    if (this.modalRef) {
      this.modalRef.hide();
    }

    let initialState: any = {
      data: { ...this.params.data, shareType: shareType, isData: true },
    };

    if (shareType === 'Email') {
      if (!this.params?.data?.email) {
        this.modalRef = this.modalService.show(
          this.noEmailModal,
          Object.assign(
            {},
            { class: 'modal-400 top-modal ph-modal-unset' }
          )
        );
        return;
      }
      this.store
        .select(getEmailSMTPByUserId)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (data && data.length) {
            this.modalRef = this.modalService.show(
              LeadsEmailShareComponent,
              Object.assign(
                {},
                {
                  class: 'modal-600 right-modal ip-modal-unset',
                  initialState,
                }
              )
            );
          } else {
            this.modalRef = this.modalService.show(
              LeadsTemplateShareComponent,
              Object.assign(
                {},
                {
                  class: 'modal-300 right-modal',
                  initialState,
                }
              )
            );
          }
        });
    } else {
      this.modalRef = this.modalService.show(
        LeadsTemplateShareComponent,
        Object.assign({}, { class: 'modal-300 right-modal', initialState })
      );
    }
  }

  openUnassignModal() {
    this.modalRef = this.modalService.show(UnassignedComponent, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    });
  }

  /**
   * Function to make an api call which restores the data
   * @param data
   */
  restoreData(event: any, data: any): void {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'Restore',
      title: data?.name,
      fieldType: 'data',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-350 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new RestoreData([data?.id]));
        }
      });
    }
  }

  groupByYearAndMonth(data: any[]) {
    const groupedData: any = {};
    data.forEach((item) => {
      const date = new Date(item.callStartTime);
      const year = date.getFullYear();
      const month = date.getMonth();
      if (!groupedData[year]) {
        groupedData[year] = {};
      }
      if (!groupedData[year][month]) {
        groupedData[year][month] = [];
      }
      groupedData[year][month].push({
        ...item,
        months: date,
      });
    });

    return Object.keys(groupedData)
      .sort((a, b) => +b - +a)
      .map((year) => ({
        years: +year,
        yearData: Object.keys(groupedData[year])
          .sort((a, b) => +b - +a)
          .map((month) => ({
            months: new Date(+year, +month, 1),
            monthData: groupedData[year][month].sort((a: any, b: any) =>
              new Date(b.callStartTime).getTime() - new Date(a.callStartTime).getTime()
            ),
          })),
      }));
  }

  decodeAudioUrl(url: string): string | null {
    try {
      return decodeURIComponent(url);
    } catch {
      return url;
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
