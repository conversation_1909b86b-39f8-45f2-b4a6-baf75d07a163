<ng-container>
    <div routerLink='/global-config'
        class="icon ic-circle-chevron-left ml-10 ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
    </div>
    <div class="p-24 position-relative">
        <div class="flex-between align-center mb-20">
            <div class="align-center">
                <div routerLink="/global-config" class="icon ic-triangle-up rotate-270 ic-x-xs ic-black cursor-pointer mr-10"
                    tabindex="0"></div><span class="icon ic-setting-connections ic-sm ic-blue-1150 mr-8"></span>
                <h5 class="fw-600 header-3">Automation Settings</h5>
            </div>
            <div class="flex-center">
                <button *ngIf="canBulkUpload || canExport" class="btn-coal  mr-10" id="btnExportTracker"
                    data-automate-id="btnExportTracker" (click)="openreportAutomationTracker()">
                    <span class="ic-tracker icon ic-xxs"></span>
                    <span class="ml-8 ip-d-none">Tracker</span>
                </button>
                <div class="btn-coal" (click)="openReportAutomationModal()">
                    <span class="ic-add icon ic-xxs"></span>
                    <span class="ml-8 ip-d-none">Add Automation</span>
                </div>
            </div>
        </div>
        <div class="bg-white px-12 py-16 mt-12 ip-flex-wrap gap-1 flex-between br-6">
            <div>
                <h5 class="fw-600">Automated Reports</h5>
                <h6 class="text-dark-gray pt-4">Configure automation to get your reports automatically</h6>
            </div>
            <div class="d-flex flex-wrap gap-4">
                <div class="align-center ip-mt-10">
                    <h6 class="text-dark-gray mr-10 text-nowrap">Select Report :</h6>
                    <div>
                        <ng-select #reportSelect [virtualScroll]="true" class="br-20 w-170 border-0 manage-dropdown" [items]="reportTypes"
                             ResizableDropdown [closeOnSelect]="true" [clearable]="false"
                            (change)="onSelectReportType($event)" placeholder="{{'GLOBAL.select' | translate}}"
                             [(ngModel)]="selectedReports" bindLabel="key" bindValue="key"
                             (mousedown)="preventDeselect($event, reportSelect)">
                        </ng-select>
                    </div>
                </div>
                <div class="show-dropdown-white align-center position-relative ip-br-0">
                    <span class="fw-600 position-absolute left-5 z-index-2"> <span>
                            {{ 'GLOBAL.show' | translate }}</span> {{ 'GLOBAL.entries' | translate }}</span>
                    <ng-select [virtualScroll]="true" [(ngModel)]="selectedPageSize" [placeholder]="PageSize"
                        class="w-150" [searchable]="false" ResizableDropdown (change)="onPageSizeChange($event)">
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                            {{pageSize}}</ng-option>
                    </ng-select>
                </div>
            </div>
        </div>
        <div *ngIf="isReportAutomationLoading; else content" class="d-flex justify-center align-center h-100-270">
            <application-loader></application-loader>
        </div>
        <ng-template #content>
            <div *ngIf="!reportAutomationData?.items?.length; else gridData">
                <div class="flex-col flex-center h-100-270">
                    <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
                    <div class="fw-semi-bold text-xl text-mud">No Data Found!</div>
                </div>
            </div>
            <ng-template #gridData>
                <div class="reports pinned-grid checkbox-align">
                    <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true"
                        [paginationPageSize]="PageSize + 1" [gridOptions]="gridOptions"
                        [rowData]="reportAutomationData.items" [suppressPaginationPanel]="true"
                        [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                        (gridReady)="onGridReady($event)" (filterChanged)="onFilterChanged($event)">
                    </ag-grid-angular>
                </div>
            </ng-template>
        </ng-template>
        <div *ngIf="!isReportAutomationLoading && reportAutomationData?.totalCount > 0" class="mt-20 flex-end">
            <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
                {{(currOffset * PageSize) + 1}} {{ 'GLOBAL.to-small' | translate }}
                {{reportAutomationData?.items?.length > 1 ?
                currOffset*PageSize + reportAutomationData?.items?.length : currOffset*PageSize +
                reportAutomationData?.items?.length}}
                {{ 'GLOBAL.of-small' | translate }} {{reportAutomationData?.totalCount}} {{ 'GLOBAL.entries-small' |
                translate }}</div>
            <pagination [offset]=currOffset [limit]="1" [range]="1"
                [size]='getPages(reportAutomationData?.totalCount,PageSize)' (pageChange)="onPageChange($event)">
            </pagination>
        </div>
    </div>
</ng-container>