import { Action } from '@ngrx/store';

export enum AmenitiesAttributesActionTypes {
  CREATE_AMENITIES = '[Amenities-Attributes] Create Amenities',
  CREATE_AMENITIES_SUCCESS = '[Amenities-Attributes] Create Amenities Success',
  CREATE_ATTRIBUTES = '[Amenities-Attributes] Create Attributes',
  CREATE_ATTRIBUTES_SUCCESS = '[Amenities-Attributes] Create Attributes Success',
  FETCH_ALL_AMENITIES = '[AMENITIES-ATTRIBUTES] Fetch All Amenities',
  FETCH_ALL_AMENITIES_SUCCESS = '[AMENITIES-ATTRIBUTES] Fetch All Amenities Success',
  FETCH_ALL_ATTRIBUTES = '[AMENITIES-ATTRIBUTES] Fetch All Attributes',
  FETCH_ALL_ATTRIBUTES_SUCCESS = '[AMENITIES-ATTRIBUTES] Fetch All Attributes Success',
  UPDATE_AMENITY = '[AMENITIES-ATTRIBUTES] Update Amenity',
  UPDATE_AMENITY_SUCCESS = '[AMENITIES-ATTRIBUTES] Update Amenity Success',
  UPDATE_ATTRIBUTE = '[AMENITIES-ATTRIBUTES]Update Attribute',
  UPDATE_ATTRIBUTE_SUCCESS = '[AMENITIES-ATTRIBUTES]Update Attribute Success',
  DELETE_AMENITIES = '[AMENITIES-ATTRIBUTES] Delete Amenity',
  DELETE_AMENITIES_SUCCESS = '[AMENITIES-ATTRIBUTES] Delete Amenity Success',
  DELETE_ATTRIBUTE = '[AMENITIES-ATTRIBUTES] Delete Attribute',
  DELETE_ATTRIBUTE_SUCCESS = '[AMENITIES-ATTRIBUTES] Delete Attribute Success',
  CREATE_CATEGORY = '[AMENITIES-ATTRIBUTES] Create Category',
  FETCH_CATEGORY_LIST = '[AMENITIES-ATTRIBUTES] Fetch Category List',
  FETCH_CATEGORY_LIST_SUCCESS = '[AMENITIES-ATTRIBUTES] Fetch Category List Success',
  DOES_AMENITY_NAME_EXISTS = '[AMENITIES-ATTRIBUTES] Does Amenity Name Exists',
  DOES_AMENITY_NAME_EXISTS_SUCCESS = '[AMENITIES-ATTRIBUTES] Does Amenity Name Exists Success',
  DOES_ATTRIBUTE_NAME_EXISTS = '[AMENITIES-ATTRIBUTES] Does Attribute Name Exists',
  DOES_ATTRIBUTE_NAME_EXISTS_SUCCESS = '[AMENITIES-ATTRIBUTES] Does Attribute Name Exists Success',
  FETCH_AMENITY_BY_ID = '[AMENITIES-ATTRIBUTES] Fetch Amenity By Id',
  FETCH_AMENITY_BY_ID_SUCCESS = '[AMENITIES-ATTRIBUTES] Fetch Amenity By Id Success',
  FETCH_ATTRIBUTE_BY_ID = '[AMENITIES-ATTRIBUTES] Fetch Attribute By Id',
  FETCH_ATTRIBUTE_BY_ID_SUCCESS = '[AMENITIES-ATTRIBUTES] Fetch Attribute By Id Success',
}


export class CreateAmenities implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.CREATE_AMENITIES;
  constructor(public payload: any) { }
}

export class CreateAmenitiesSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.CREATE_AMENITIES_SUCCESS;
  constructor(public resp: any = []) { }
}

export class CreateAttributes implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.CREATE_ATTRIBUTES;
  constructor(public payload: any) { }
}

export class CreateAttributesSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.CREATE_ATTRIBUTES_SUCCESS;
  constructor(public resp: any = []) { }
}

export class FetchAllAmenities implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_ALL_AMENITIES;
  constructor() { }
}

export class GetAllAmenitiesSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_ALL_AMENITIES_SUCCESS;
  constructor(public resp: any = []) { }
}

export class FetchAllAttributes implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_ALL_ATTRIBUTES;
  constructor() { }
}

export class GetAllAttributesSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_ALL_ATTRIBUTES_SUCCESS;
  constructor(public resp: any = []) { }
}

export class UpdateAmenities implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.UPDATE_AMENITY;
  constructor(public payload: any) { }
}

export class UpdateAmenitiesSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.UPDATE_AMENITY_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class UpdateAttributes implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.UPDATE_ATTRIBUTE;
  constructor(public payload: any) { }
}

export class UpdateAttributesSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.UPDATE_ATTRIBUTE_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class DeleteAmenity implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DELETE_AMENITIES;
  constructor(public id: string = '') { }
}

export class DeleteAmenitySuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DELETE_AMENITIES_SUCCESS;
  constructor(public id: string = '') { }
}

export class DeleteAttribute implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DELETE_ATTRIBUTE;
  constructor(public id: string = '') { }
}

export class DeleteAttributeSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DELETE_ATTRIBUTE_SUCCESS;
  constructor(public id: string = '') { }
}

export class AddCategory implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.CREATE_CATEGORY;
  constructor(public category: string) { }
}

export class FetchCategoryList implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_CATEGORY_LIST;
  constructor() { }
}

export class FetchCategoryListSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_CATEGORY_LIST_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class DoesAmenityNameExists implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DOES_AMENITY_NAME_EXISTS;
  constructor(public amenityName: string) { }
}

export class DoesAmenityNameExistsSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DOES_AMENITY_NAME_EXISTS_SUCCESS;
  constructor(public response: boolean) { }
}

export class DoesAttributeNameExists implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DOES_ATTRIBUTE_NAME_EXISTS;
  constructor(public attrName: string) { }
}

export class DoesAttributeNameExistsSuccess implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.DOES_ATTRIBUTE_NAME_EXISTS_SUCCESS;
  constructor(public response: boolean) { }
}

export class FetchAmenityById implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_AMENITY_BY_ID;
  constructor(public id: string = '') { }
}

export class FetchAmenityByIdSuccess implements Action {
  readonly type: string =
    AmenitiesAttributesActionTypes.FETCH_AMENITY_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchAttributeById implements Action {
  readonly type: string = AmenitiesAttributesActionTypes.FETCH_ATTRIBUTE_BY_ID;
  constructor(public id: string = '') { }
}

export class FetchAttributeByIdSuccess implements Action {
  readonly type: string =
    AmenitiesAttributesActionTypes.FETCH_ATTRIBUTE_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}