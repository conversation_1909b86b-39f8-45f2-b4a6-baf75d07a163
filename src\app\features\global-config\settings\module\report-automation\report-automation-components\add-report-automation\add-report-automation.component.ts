import { DatePipe } from '@angular/common';
import { Component, HostListener, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, onPickerOpened } from 'src/app/core/utils/common.util';
import { AddReportAutomation, ExistReportAutomation, FetchUserWithRole, UpdateReportAutomation } from 'src/app/reducers/reports/reports.actions';
import { getIsReportAutomationLoading, getIsUserWithRoleLoading, getReportAutomationExist, getReportAutomationTypeList, getUserWithRole } from 'src/app/reducers/reports/reports.reducer';

const ScheduleTypeEnum: any = {
  Everyday: 1,
  Weekly: 2,
  Monthly: 3,
};

const ServiceTypeEnum: any = {
  Email: 1,
  WhatsApp: 2,
  SMS: 3,
};

@Component({
  selector: 'add-report-automation',
  templateUrl: './add-report-automation.component.html',
  providers: [DatePipe]
})
export class AddReportAutomationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  selectedReportAutomation: any;
  addReportAutomationForm: FormGroup;
  isAddEditReportAutomation: boolean = false;
  allUserList: any[] = [];
  userListWithRole: any[] = [];
  filteredUserList: any[] = [];
  reportTypes: any[] = [];
  subReportTypes: any[] = [];
  // roleList: any[] = [];
  dateRange: number[] = Array.from({ length: 31 }, (_, i) => i + 1);
  daysList = [
    { name: 'Sunday', value: 0 },
    { name: 'Monday', value: 1 },
    { name: 'Tuesday', value: 2 },
    { name: 'Wednesday', value: 3 },
    { name: 'Thursday', value: 4 },
    { name: 'Friday', value: 5 },
    { name: 'Saturday', value: 6 }
  ];
  frequencyList: any[] = [
    { label: 'Today', value: 0 },
    { label: 'Last 2 days', value: 2 },
    { label: '7 days', value: 7 },
    { label: '30 days', value: 30 },
    { label: 'Custom Days', value: 'Custom Days' },
  ];
  isCustomStatusEnabled: boolean = false;
  isDropdownOpen: boolean = false;
  isPatching = false;
  selectedDate: number | null = null;
  isOpenReportFrequencyModal: boolean = false;
  doesExistReportAutomation: boolean = false
  selectedReportFrequency: string;
  isCloseReportFrequencyModal: boolean;
  isUserWithRoleLoading: boolean = false;
  isShowCustomError: boolean;
  selectFrequencyError: boolean;
  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const dropdownElement = document.querySelector('.date-grid');
    if (dropdownElement && !dropdownElement.contains(target)) {
      this.isDropdownOpen = false;
    }
  }

  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    private datePipe: DatePipe,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new FetchUserWithRole());
    this.initReportAutomationForm();
    combineLatest([
      this.store.select(getReportAutomationTypeList),
      this.store.select(getUserWithRole),
      this.store.select(getIsUserWithRoleLoading)
    ])
      .pipe(takeUntil(this.destroy$))
      .subscribe(([reportTypes, users, isUserWithRoleLoading]) => {
        if (reportTypes) {
          this.reportTypes = Object.keys(reportTypes).map((key: any) => ({ key, value: reportTypes[key] }));
        }
        if (users && Array.isArray(users)) {
          this.allUserList = assignToSort(
            users.map((user: any) => ({
              ...user,
              fullName: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
            }))
          );
          this.userListWithRole = [...this.allUserList];
          this.filteredUserList = [...this.allUserList];
          const enabledUsers = this.userListWithRole?.filter((u: any) => u.isActive)
          this.selectAllForDropdownItems(enabledUsers)
        }
        this.isUserWithRoleLoading = isUserWithRoleLoading
        if (this.selectedReportAutomation) {
          this.patchReportAutomationForm(this.selectedReportAutomation);
        }
      });
    this.addReportAutomationForm.get('selectedDay').valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((selectedDays: string[]) => {
        if (selectedDays && selectedDays.length > 5) {
          selectedDays = selectedDays.slice(0, 5);
          this.addReportAutomationForm.get('selectedDay').setValue(selectedDays, { emitEvent: false });
        }
        const allDaysSelected = selectedDays && selectedDays.length === this.daysList.length;
        const selectAllCheckbox = document.getElementById('select-all-days') as HTMLInputElement;
        if (selectAllCheckbox) {
          selectAllCheckbox.checked = allDaysSelected;
        }
      });

    this.addReportAutomationForm.get('receiveReports').valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((value: string) => {
        this.updateFormValidators(value);
      });

    this.addReportAutomationForm.get('selectedReportFrequency').valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((value: string) => {
        this.addReportAutomationForm.get('customFrequency').reset()
      });
  }

  initReportAutomationForm(): void {
    this.addReportAutomationForm = this.fb.group({
      displayname: [null, Validators.required],
      parentReportName: [null, Validators.required],
      reportName: [null, Validators.required],
      reportFrequency: [null, Validators.required],
      selectedReportFrequency: [null],
      customFrequency: [null],
      receiveReports: ['Everyday', Validators.required],
      selectedDay: [null, Validators.required],
      selectedDate: [null, Validators.required],
      selectedTime: [null, Validators.required],
      mode: [[], Validators.required],
      // RoleIds: [null, Validators.required],
      user: [[], Validators.required],
      isEnabled: [null],
      jobId: [null]
    });
    this.updateFormValidators(this.addReportAutomationForm.get('receiveReports').value);
  }

  doesReportAutomationExist(reportName: string) {
    if (reportName) {
      this.store.dispatch(new ExistReportAutomation(reportName));
      this.store.select(getReportAutomationExist).subscribe((doesExistReportAutomation: boolean) => {
        this.doesExistReportAutomation = doesExistReportAutomation;
        this.addReportAutomationForm.get('displayname')?.setErrors(
          doesExistReportAutomation ? { alreadyExist: true } : null
        );
      });
    }
  }

  updateFormValidators(receiveReportsValue: string): void {
    const selectedDayControl = this.addReportAutomationForm.get('selectedDay');
    const selectedDateControl = this.addReportAutomationForm.get('selectedDate');
    if (receiveReportsValue === 'Weekly') {
      selectedDayControl.setValidators([Validators.required]);
      selectedDateControl.clearValidators();
      selectedDateControl.reset();
    } else if (receiveReportsValue === 'Monthly') {
      selectedDateControl.setValidators([Validators.required]);
      selectedDayControl.clearValidators();
      selectedDayControl.reset();
    } else if (receiveReportsValue === 'Everyday') {
      selectedDayControl.clearValidators();
      selectedDateControl.clearValidators();
      selectedDayControl.reset();
      selectedDateControl.reset();
    }
    selectedDayControl.updateValueAndValidity();
    selectedDateControl.updateValueAndValidity();
  }

  onSubmit(): void {
    Object.keys(this.addReportAutomationForm.controls).forEach((field) => {
      const control = this.addReportAutomationForm.get(field);
      if (control && control.invalid) {
        console.log(`Invalid field: ${field}`);
      }
    });

    if (this.addReportAutomationForm.valid) {
      const value = this.addReportAutomationForm.value;
      const selectedTime = new Date(`01/01/2000 ${this.datePipe.transform(value.selectedTime, 'h:mm a')}`);
      const formattedTime = this.datePipe.transform(selectedTime, 'hh:mm a', 'UTC');
      const id = this.selectedReportAutomation?.id ?? null;
      const payload: any = {
        displayname: value.displayname,
        parentReportName: value.parentReportName,
        reportName: value.reportName,
        scheduleType: ScheduleTypeEnum[value.receiveReports],
        scheduleTime: formattedTime,
        serviceType: value.mode?.map((mode: string) => ServiceTypeEnum[mode]) || [],
        userIds: value.user?.map((u: any) => u.id) || [],
        ...(this.selectedReportAutomation ? { id } : {}),
        isEnabled: value?.isEnabled,
        jobId: value?.jobId
      };
      if (value.selectedReportFrequency === 'Custom Days') {
        payload.frequency = value.customFrequency;
      } else {
        payload.frequency = this.frequencyList.find(item => item.label === value.reportFrequency)?.value;
      }
      if (value.selectedDate) {
        payload.duration = value.selectedDate;
      } else if (value.selectedDay) {
        payload.selectedDays = value.selectedDay;
      }
      if (this.selectedReportAutomation) {
        this.store.dispatch(new UpdateReportAutomation(payload));
      } else {
        this.store.dispatch(new AddReportAutomation(payload));
      }
      this.store.select(getIsReportAutomationLoading)
        .pipe(takeUntil(this.destroy$))
        .subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isAddEditReportAutomation = false;
            this.modalRef.hide();
          }
        });
    } else {
      this.addReportAutomationForm.markAllAsTouched();
    }
  }

  patchReportAutomationForm(data: any): void {
    this.isPatching = true;
    if (this.allUserList.length === 0) {
      setTimeout(() => this.patchReportAutomationForm(data), 500);
      return;
    }
    const time24 = data.scheduleTime;
    const [hours, minutes] = time24.split(':');
    const utcDate = new Date();
    utcDate.setUTCHours(parseInt(hours, 10));
    utcDate.setUTCMinutes(parseInt(minutes, 10));
    const localTime = this.datePipe.transform(utcDate, 'h:mm a');
    const timeDateObj = new Date(`01/01/2000 ${localTime}`);
    const users = data.userIds.map((id: string) => this.allUserList.find((u: any) => u.id === id));
    const mode = data.serviceType.map((st: number) => {
      return Object.keys(ServiceTypeEnum).find(key => ServiceTypeEnum[key] === st);
    });
    const receiveReports = Object.keys(ScheduleTypeEnum).find(key => ScheduleTypeEnum[key] === data.scheduleType);
    this.updateFormValidators(receiveReports);
    const frequencyItem = this.frequencyList.find(item => item.value === data.frequency);
    const frequencyLabel = frequencyItem ? frequencyItem.label : 'Custom Days';
    const isCustomFrequency = !frequencyItem;
    isCustomFrequency ? this.selectedReportFrequency === 'Custom Days' : null
    this.addReportAutomationForm.patchValue({
      displayname: data.displayName || '',
      parentReportName: data.parentReportName || '',
      reportName: data.reportName || '',
      reportFrequency: isCustomFrequency
        ? data.frequency
          ? data.frequency + ' Days'
          : null
        : frequencyLabel,
      selectedReportFrequency: isCustomFrequency
        ? data.frequency
          ? 'Custom Days'
          : null
        : frequencyItem?.label,
      customFrequency: isCustomFrequency && data.frequency ? data.frequency : null,
      receiveReports: receiveReports || '',
      ...(receiveReports === 'Weekly'
        ? { selectedDay: data?.selectedDays || [] }
        : receiveReports === 'Monthly'
          ? { selectedDate: data?.duration || '' }
          : {}),
      selectedTime: timeDateObj,
      mode: mode || [],
      user: (users || []).filter((user: any) => user !== undefined),
      isEnabled: data?.isEnabled ?? true,
      jobId: data?.jobId || ''
    });

    const parentReport = this.reportTypes.find((rt: any) => rt.key === data.parentReportName);
    if (parentReport) {
      this.onChangeReport(parentReport);
    }
    this.selectDate(data?.duration);
    this.isPatching = false;
  }
  onSelectMode(event: any, mode: string): void {
    const modeControl = this.addReportAutomationForm.get('mode');
    const currentModes = modeControl.value as string[];
    if (event.target.checked) {
      if (!currentModes.includes(mode)) {
        modeControl.setValue([...currentModes, mode]);
      }
    } else {
      modeControl.setValue(currentModes.filter(m => m !== mode));
    }
  }
  onChangeReport($event: any): void {
    this.addReportAutomationForm.get('parentReportName').setValue($event?.key);
    if (!this.isPatching) {
      this.addReportAutomationForm.get('reportName').reset();
    }
    if ($event) {
      this.subReportTypes = Object.entries($event.value)
        .filter(([key, value]) => {
          if (this.isCustomStatusEnabled) {
            return !key.startsWith("Status");
          } else {
            return !key.startsWith("Custom Status");
          }
        })
        .map(([key, value]) => ({ key, value }));
    } else {
      this.addReportAutomationForm.get('reportName').setValue(null)
      this.subReportTypes = [];
    }
  }
  toggleDropdown(event: MouseEvent) {
    event.stopPropagation();
    this.isDropdownOpen = !this.isDropdownOpen;
  }
  selectDate(date: number) {
    this.selectedDate = date;
    this.addReportAutomationForm.controls['selectedDate'].setValue(date);
    this.isDropdownOpen = false;
  }
  onRemoveUser(user: any): void {
    const currentUsers = this.addReportAutomationForm.get('user').value as any[];
    const updatedUsers = currentUsers.filter(u => u.id !== user.id);
    this.addReportAutomationForm.get('user').setValue(updatedUsers);
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };
    allSelect(items);
  }

  // This method is called when the Select All checkbox is clicked
  // It will be triggered through the ng-select (change) event
  handleSelectAll(selectedUsers: any[]) {
    // If selectedUsers includes 'selectedAllGroup', it means the Select All checkbox was clicked
    if (selectedUsers?.includes('selectedAllGroup')) {
      // Get only the filtered users that are currently visible in the dropdown
      const filteredActiveUsers = this.filteredUserList
        .filter(user => user.isActive)
        .map(user => user);

      // Set the selected users to the filtered list
      this.addReportAutomationForm.get('user').setValue(filteredActiveUsers);
    }
  }
  openFrequencyModel() {
    let customFrequency = this.addReportAutomationForm.get('customFrequency').value
    if (customFrequency) {
      this.addReportAutomationForm.get('reportFrequency').setValue(`${customFrequency} Days`);
    }
    this.isOpenReportFrequencyModal = !this.isOpenReportFrequencyModal
  }
  getSelectedReportFrequencyDisplay(): string {
    if (this.selectedReportFrequency === 'Custom Days') {
      return this.addReportAutomationForm.get('selectedReportFrequency').value;
    }
    return this.selectedReportFrequency || '';
  }
  onSelectReportFrequency(value: any): void {
    this.selectedReportFrequency = value?.label;
    if (value.label === 'Custom Days') {
      this.isOpenReportFrequencyModal = true;
      this.addReportAutomationForm.get('selectedReportFrequency').setValue('Custom Days');
    } else {
      this.isOpenReportFrequencyModal = false;
      this.addReportAutomationForm.get('reportFrequency').setValue(value?.label);
    }
  }
  onPickerClosed($event: any): void {
    this.isDropdownOpen = false;
    this.isOpenReportFrequencyModal = false
    this.addReportAutomationForm.get('selectedReportFrequency').setValue('Custom Days');
    this.addReportAutomationForm.get('reportFrequency').setValue($event);
  }
  reportFrequencyCloseModal(): void {
    let customFrequency = this.addReportAutomationForm.get('customFrequency').value
    let frequency = this.addReportAutomationForm.get('selectedReportFrequency').value
    if (frequency && frequency !== 'Custom Days') {
      this.isOpenReportFrequencyModal = false;
      this.isCloseReportFrequencyModal = true;
    }
    if (frequency === 'Custom Days' && !customFrequency) {
      this.isShowCustomError = true
      this.selectFrequencyError = false
    }
    if (frequency === 'Custom Days' && customFrequency) {
      this.isOpenReportFrequencyModal = false;
      this.isCloseReportFrequencyModal = true;
      this.selectFrequencyError = false
      this.isShowCustomError = false
      this.addReportAutomationForm.get('reportFrequency').setValue(`${customFrequency} Days`);
    }
    if (!frequency && !customFrequency) {
      this.selectFrequencyError = true
    }
  }

  onClickCancelCustom(): void {
    this.isOpenReportFrequencyModal = false;
    this.isShowCustomError = false;
    this.selectFrequencyError = false;
    this.addReportAutomationForm.get('customFrequency').reset();
    this.addReportAutomationForm.get('selectedReportFrequency').reset();
    this.addReportAutomationForm.get('reportFrequency').reset();
    this.selectedReportFrequency = null;
  }

  formatUserRoles(userRoles: any[]): string {
    return userRoles.map(role => role.name).join(', ');
  }

  restrictInput(event: any) {
    let value = event.target.value;
    if (value.length > 9) {
      event.target.value = value.slice(0, 9);
    }
  }

  onSelectUser(user: any) {
    // Check if the Select All option was clicked
    if (user?.includes('selectedAllGroup')) {
      this.handleSelectAll(user);
    } else {
      const enabledUsers = user?.filter((u: any) => u.isActive) || [];
      this.addReportAutomationForm.get('user').setValue(enabledUsers);
    }
  }

  onUserSearch(event: any) {
    const searchTerm = event.term?.toLowerCase() || '';
    if (searchTerm) {
      this.filteredUserList = this.allUserList.filter(user =>
        user.fullName.toLowerCase().includes(searchTerm));
    } else {
      // When search is cleared, reset to show all users
      this.filteredUserList = [...this.allUserList];
    }

    // Re-apply the selectedAllGroup to ensure the Select All option works correctly
    this.selectAllForDropdownItems(this.filteredUserList.filter(u => u.isActive));
  }

  // This method is called when the clear button (X) is clicked or dropdown is closed
  onClearSearch() {
    // Reset to show all users
    this.filteredUserList = [...this.allUserList];

    // Re-apply the selectedAllGroup to ensure the Select All option works correctly
    this.selectAllForDropdownItems(this.filteredUserList.filter(u => u.isActive));

    // Force the component to detect changes by triggering a setTimeout
    setTimeout(() => {
      // This will ensure the UI is updated with the full list
      this.filteredUserList = [...this.allUserList];
    }, 0);
  }

  // Handle keyboard events in the search box
  onKeyUp(event: KeyboardEvent, selectComponent: any) {
    // If the user presses Escape or Delete key, reset the filter
    if (event.key === 'Escape' || event.key === 'Delete' || event.key === 'Backspace') {
      if (!selectComponent.searchTerm || selectComponent.searchTerm === '') {
        this.onClearSearch();
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}