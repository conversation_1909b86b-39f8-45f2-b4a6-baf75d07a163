<ng-container *ngIf="params.data.isArchived">
    <div class="bg-accent-green-40 text-green-900 br-50px text-center p-6 text-xs" *ngIf="params.data.isEnabled===false">
      Available
    </div>
    <div class="bg-accent-red-40 text-dark-red br-50px text-center p-6 text-xs" *ngIf="params.data.isEnabled===true">
      Unavailable
    </div>
  </ng-container>
  <ng-container *ngIf="!params.data.isArchived">
    <div class="text-center align-center" [title]="status ? 'Available' : 'Unavailable'">
      <input type="checkbox" class="toggle-switch toggle-active-sold" [(ngModel)]="status"
        (click)="canUpdateStatus ? openConfirmModal(params.data) : ''" [ngClass]="{'pe-none' : !canUpdateStatus}">
      <label for="chkToggle" class="switch-label" [ngClass]="{'pe-none' : !canUpdateStatus}"></label>
    </div>
  </ng-container>