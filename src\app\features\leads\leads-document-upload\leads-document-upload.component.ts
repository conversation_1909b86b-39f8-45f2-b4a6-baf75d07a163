import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import {
  DeleteLeadDocument,
  FetchLeadDocuments,
  UploadLeadDocument,
} from 'src/app/reducers/lead/lead.actions';
import { getLeadDocuments, getLeadDocumentsIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'leads-document-upload',
  templateUrl: './leads-document-upload.component.html',
})
export class LeadsDocumentUploadComponent
  implements OnInit, OnDestroy, OnChanges {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('deleteDocumentModal') deleteDocumentModal: TemplateRef<any>;
  selectedFile: Array<string>;
  s3BucketPath: string = environment.s3ImageBucketURL;
  leadDocName: FormControl = new FormControl('', Validators.required);
  validationPerformed: boolean = false;
  isUpload: boolean = false;
  fileFormatToBeUploaded: string =
    'application/pdf,image/x-png,image/gif,image/jpeg,image/tiff';
  noDocument: AnimationOptions = { path: 'assets/animations/no-document.json' };
  leadDocList: Array<any> = [];
  visitDocList: Array<any> = [];
  meetingDocList: Array<any> = [];
  currentDelete: string;
  activeLeadIsLoading: boolean = true;
  @Input() leadData: any;
  canEditDoc: boolean = false;
  @Input() whatsAppComp: boolean = false;
  moment = moment;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;

  constructor(
    private s3UploadService: BlobStorageService,
    private store: Store<AppState>,
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private _notificationsService: NotificationsService,
    private _translateService: TranslateService,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Leads.UpdateDocuments')) {
          this.canEditDoc = true;
        }
      });

    if (this?.leadData?.id && !this.whatsAppComp) {
      this.store.dispatch(new FetchLeadDocuments(this.leadData?.id));
    }
    this.store
      .select(getLeadDocumentsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: any) => {
        this.activeLeadIsLoading = loading;
      });
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.fetchLeadData();
  }

  ngOnChanges(changes: SimpleChanges) {
    if ('leadData' in changes) {
      this.fetchLeadData();
    }
  }

  fetchLeadData() {
    if (this?.leadData?.id && !this.whatsAppComp) {
      this.store.dispatch(new FetchLeadDocuments(this.leadData?.id));
      this.store
        .select(getLeadDocuments)
        .pipe(
          takeUntil(this.stopper)
        )
        .subscribe((documents: any[]) => {
          this.leadDocList = [];
          this.meetingDocList = [];
          this.visitDocList = [];

          documents?.forEach((doc: any) => {
            if (doc?.leadDocumentType === 1) {
              this.leadDocList?.push(doc);
            } else if (doc?.leadDocumentType === 2) {
              this.meetingDocList?.push(doc);
            } else {
              this.visitDocList?.push(doc);
            }
          });
        });
    }
  }

  fileUploadToS3() {
    if (!this.leadData?.name?.trim()) {
      this._notificationsService.warn(
        'Lead Name is invalid, Please Rename to continue'
      );
      return;
    }
    if (this.selectedFile?.[0]?.includes('data:') && this.leadDocName.valid) {
      this.activeLeadIsLoading = true;
      this.isUpload = false;
      this.s3UploadService
        .uploadImageBase64(this.selectedFile, FolderNamesS3.LeadDocument)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            this.addDocument(response.data?.[0]);
          }
        });
    } else {
      this.validationPerformed = true;
      if (this.leadDocName.invalid || !this.selectedFile) {
        this.leadDocName.markAsTouched({ onlySelf: true });
        return;
      }
      this.addDocument();
    }
  }

  isPdf(filePath: string) {
    if (filePath && filePath.substring(filePath.length - 3) === 'pdf')
      return true;
    return false;
  }

  addDocument(filePath?: string) {
    let payload: any = {
      leadId: this.leadData.id,
      documents: [
        {
          documentName: this.leadDocName.value,
          filePath: filePath,
        },
      ],
    };
    this.store.dispatch(new UploadLeadDocument(payload.id, payload));
    this.leadDocName.reset();
    this.selectedFile = null;
    this.validationPerformed = false;
    this.isUpload = false;
  }

  cancelUpload() {
    this.isUpload = false;
    this.leadDocName.reset();
    this.selectedFile = null;
    this.validationPerformed = false;
  }

  initDeleteDocument(id: string) {
    this.modalRef = this.modalService.show(
      this.deleteDocumentModal,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
        }
      )
    );
    this.currentDelete = id;
  }

  removeDocument(id: string) {
    this.activeLeadIsLoading = true;
    let payload: any = {
      leadId: this.leadData.id,
      documentIds: [id],
    };
    this.store.dispatch(new DeleteLeadDocument(payload));
    this.modalRef.hide();
  }

  copyUrl(url: string): void {
    navigator.clipboard?.writeText(url);
    this._notificationsService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
