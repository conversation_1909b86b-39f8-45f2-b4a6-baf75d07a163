import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';


@Injectable({
  providedIn: 'root'
})
export class AmenitiesAttributesService {
  serviceBaseUrl: string;
  iconServiceBaseUrl: string;
  getResourceUrl(): string {
    return 'customamenityandattribute';
  }


  constructor(private http: HttpClient) {
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getCategories(){
    return this.http.get(`${this.serviceBaseUrl}/amenitycategory`);
  }

  createAmenities(payload:any){
    return this.http.post(`${this.serviceBaseUrl}/new/amenities`,payload)
  }
  
  createAttributes(payload:any){
    return this.http.post(`${this.serviceBaseUrl}/attribute`,payload)
  }

  getAllAmenities(){
    return this.http.get(`${this.serviceBaseUrl}/get/all/categories/with/amenities`)
  }

  getAllAttributes(){
    return this.http.get(`${this.serviceBaseUrl}/get/all/attributes`)
  }

  updateAmenity(payload:any){
    return this.http.put(`${this.serviceBaseUrl}/updateamenities`,payload)
  }

  updateAttribute(payload:any){
    return this.http.put(`${this.serviceBaseUrl}/updateattribute`,payload)
  }

  deleteAmenity(id:string){
    return this.http.delete(`${this.serviceBaseUrl}/${id}/amenity`)
  }

  deleteAttribute(id:string){
    return this.http.delete(`${this.serviceBaseUrl}/${id}/atttibute`) //need to fix the spelling of attribute
  }

  addCategory(category: string) {
    return this.http.post(`${this.serviceBaseUrl}/catgory`, {
      category: category,
    });
  }

  doesAmenityNameExists(name: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/custom/amenity-exist?name=${name}`
    );
  }

  doesAttrNameExists(name: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/custom/attribute-exist?name=${name}`
    );
  }

  getAmenityById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/${id}/amenityid`);
  }

  getAttributeById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/${id}/atttibuteid`);
  }
}
