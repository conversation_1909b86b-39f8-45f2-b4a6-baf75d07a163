<div *ngIf="loggedInUser" class="justify-between">
    <h4 class="fw-700 mr-10 text-truncate-2 break-all">
        <span>{{ greeting }}</span>
        <span *ngIf="userData?.[0].userName">, {{userData?.[0].userName}}!</span>
    </h4>
    <div>
        <div *ngIf="!isClockIn" (click)="clockIn()"
            class="bg-green-900 w-100px py-8 text-center text-white fw-600 br-20"
            [ngClass]="{'cursor-pointer': (!geoFencingData || !isGeoFenceEnabled || !userBasicDetails?.isGeoFenceActive || (isInGeoFence && !isAccuracyLow)), 'opacity-50 pe-none': (geoFencingData && isGeoFenceEnabled && userBasicDetails?.isGeoFenceActive && (!isInGeoFence || isAccuracyLow) && !isCheckingGeoFence)}">
            Clock IN
        </div>
        <div *ngIf="isClockIn" class="flex-center-col">
            <div (click)="clockOut()" class="bg-red-350 w-100px py-8 text-center text-white fw-600 br-20"
                [ngClass]="{'cursor-pointer': (!geoFencingData || !isGeoFenceEnabled || !userBasicDetails?.isGeoFenceActive || (isInGeoFence && !isAccuracyLow)), 'opacity-50 pe-none': (geoFencingData && isGeoFenceEnabled && userBasicDetails?.isGeoFenceActive && (!isInGeoFence || isAccuracyLow) && !isCheckingGeoFence)}">
                Clock OUT
            </div>
            <!-- <div class="fw-600 mt-4">{{ hours }}h: {{ minutes }}m: {{ seconds }}s</div>
            <div class="text-xs text-dark-gray text-decoration-underline">since last login</div> -->
        </div>
    </div>
    <h5 *ngIf="isLocationDisabled"
        class="position-absolute left-0 top-45 nz-index-1 text-white bg-danger text-nowrap w-100 flex-center pt-2 z-index-1021">
        <span class="icon ic-location-solid  ic-xs mr-8"></span>Location permission is mandatory to use
        <span class="fw-600 mx-2">“Clock IN / OUT”</span> feature.
    </h5>
</div>
<div class="flex-between tb-flex-col mqd-flex-col tb-align-center-unset mqd-align-center-unset">
    <div *ngIf="!loggedInUser">
        <div class="text-sm">{{'USER.user-progress-message' | translate }}</div>
        <div class="text-large fw-700">{{'USER.user' | translate }} {{'SIDEBAR.attendance' | translate }}</div>
    </div>
    <ul class="d-flex text-large flex-wrap filters-grid p-0 tb-pt-10 mqd-pt-10">
        <button *ngFor="let option of dateFilterOptions" class="mr-6 br-4 py-6 px-10 ip-mb-10"
            (click)="filterByDate(option.type)" [(ngModel)]="appliedFilter.date" ngDefaultControl name="dateFilter"
            [ngClass]="isDateFilter === option.type ? 'bg-black-200 text-white border-0' : 'bg-white border'">
            {{ option.translationKey | translate }}
        </button>
        <div *ngIf="isDateFilter == 'custom'" [owlDateTimeTrigger]="dt1"
            class="date-picker border br-6 align-center mr-8 ip-mb-10">
            <span class="ic-appointment icon ic-xxs ic-black"></span>
            <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                [max]="currentDate" (ngModelChange)="appliedFilter.date = $event;filterFunction()"
                [ngModel]="appliedFilter.date" placeholder="ex. 19-06-2025 - 29-06-2025" class="pl-20 text-large" />
            <owl-date-time [pickerType]="'calendar'" #dt1
                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
        </div>
        <span *ngIf="appliedFilter?.date?.[0] || isDateFilter == 'custom'"
            class="ic-refresh ic-xxs w-30px h-30px flex-center bg-coal br-4 cursor-pointer ic-white"
            (click)="resetDate()"></span>
    </ul>
</div>
<div class="mt-20 user-details">
    <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
        [rowData]="rowData" [pagination]="true" [paginationPageSize]="rowData?.length"
        [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" [suppressPaginationPanel]="true">
    </ag-grid-angular>
</div>