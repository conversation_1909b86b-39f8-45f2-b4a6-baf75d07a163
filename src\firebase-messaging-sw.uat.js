importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: "AIzaSyAX3q9mzzCJ8bl-ciWmcjsq_PrEFrdRbaE",
    authDomain: "leadrat-black-web-uat.firebaseapp.com",
    projectId: "leadrat-black-web-uat",
    storageBucket: "leadrat-black-web-uat.firebasestorage.app",
    messagingSenderId: "892771106611",
    appId: "1:892771106611:web:629bc2c0fbb4bd53ab8846",
    measurementId: "G-VLZGF1HYYQ",
    vapidKey: 'BMYoZ1lmdnQH-MIDTGNi3Mv0gyvmtZ-0jgSzJw4PAusengdr2eJKxcy8ue1ABmPIcSDQn0DQt8Ju7LDkIteFpmI'
});

const messaging = firebase.messaging();
self.addEventListener('push', function (event) {
    const payload = event.data ? event.data.json() : {};
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png',
        // data: { url: payload.data.click_action},
        // actions: [
        //     {
        //         action: 'reply', // Identifier for the action
        //         title: 'Reply', // Button text
        //         icon: payload.notification.icon // Optional icon for the button,
        //     },
        //     {
        //         action: 'dismiss', // Identifier for the action
        //         title: 'Dismiss', // Button text
        //         icon: payload.notification.icon // Optional icon for the button
        //     }
        // ],
    };

    event.waitUntil(
        // Retrieve all existing notifications and close them
        self.registration.getNotifications().then(function (notifications) {
            return Promise.all(notifications.map(function (notification) {
                return notification.close();
            }));
        })
            .then(function () {
                return self.registration.showNotification(notificationTitle, notificationOptions);
            })
    );
});

// self.addEventListener('notificationclick', function(event) {
//     event.notification.close();
//     console.log(event);

//     // Determine action based on notification data
//     const actionUrl =  event.notification.data.url;

//     event.waitUntil(
//         clients.openWindow(actionUrl)
//     );
// });


messaging.onBackgroundMessage(function (payload) {
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
    };

    self.registration.showNotification(notificationTitle, notificationOptions);
});
