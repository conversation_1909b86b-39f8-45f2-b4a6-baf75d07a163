import { ChangeDetectorRef, Component, EventEmitter, HostListener, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import {
  CellClickedEvent,
  ColDef,
  GridApi,
  GridOptions,
} from 'ag-grid-community';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { debounceTime, filter, firstValueFrom, skipWhile, take, takeUntil, tap } from 'rxjs';
import {
  DATE_FILTERS,
  DATE_TYPE,
  EMPTY_GUID,
  LEAD_FILTERS_KEY_LABEL,
  LEAD_STATUS,
  LEAD_VISIBILITY_IMAGE,
  LEADS_FILTERS_V2,
  PAGE_SIZE,
  PAYMENT_TYPE,
  POSSESSION_DATE_FILTER_LIST,
  SCHEDULED_TODAY_LIST,
  SHOW_ENTRIES,
} from 'src/app/app.constants';
import {
  BHKType,
  BookingPaymentMode,
  CallDirections,
  CallStatuses,
  EnquiryType,
  FurnishStatus,
  Gender,
  LeadDateType,
  LeadScheduledType,
  LeadSource,
  LeadVisibility,
  MaritalStatusType,
  OfferType,
  OwnerSelectionType,
  PaymentType,
  PossessionType,
  Profession,
  PurposeType,
  SecondLevelFilter,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  formatBudget,
  getAssignedToDetails,
  getBedsDisplay,
  getBHKDisplayString,
  getBRDisplayString,
  getIndexes,
  getLocationDetailsByObj,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  isEmptyObject,
  onFilterChanged,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { FetchTagsList } from 'src/app/reducers/custom-tags/custom-tags.actions';
import { getTagsList } from 'src/app/reducers/custom-tags/custom-tags.reducer';
import { FetchEmailSMTPListByUserId } from 'src/app/reducers/email/email-settings.action';
import { FetchFilter, FilterExist, SaveFilter, UpdateSavedFilter } from 'src/app/reducers/filter/filter.action';
import { getFilter, getFilterExist, getSavedFilter } from 'src/app/reducers/filter/filter.reducer';
import {
  addDuplicate,
  cancelAddLead,
  FetchActiveCount,
  FetchAllParentLeadById,
  FetchBulkOperation,
  FetchExcelUploadedList,
  FetchExportStatus,
  FetchLeadBaseFilterCount,
  FetchLeadByIdWithArchive,
  FetchLeadByIdWithArchiveSuccess,
  FetchLeadCustomTopFilters,
  FetchLeadCustomTopFiltersSuccess,
  FetchLeadExportSuccess,
  FetchLeadsCommunicationByIds,
  FetchLeadStatusCount,
  FetchModuleWiseSearchProperties,
  UpdateFilter,
  UpdateFilterPayload,
  UpdateInvoiceFilter,
  UpdateIsCardView,
  UpdateIsLoadMore
} from 'src/app/reducers/lead/lead.actions';
import {
  getActiveLeadsCounts,
  getActiveLeadsCountsIsLoading,
  getAllParentLeadData,
  getBaseFilterCountIsLoading,
  getDuplicate,
  getFiltersPayload,
  getFlagsCounts,
  getInvoiceFiltersPayload,
  getIsLeadCustomStatusEnabled,
  getLeadBaseFilterCount,
  getLeadByIdWithArchiveData,
  getLeadCardData,
  getLeadCustomTopFilters,
  getLeadCustomTopFiltersIsLoading,
  getLeadListIsLoading,
  getLeads,
  getLeadsCount,
  getLeadStatusCounts,
  getModuleWiseSearchProperties,
  getModuleWiseSearchPropertiesLoading
} from 'src/app/reducers/lead/lead.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchModifiedDatesList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits, getFetchModifiedDatesList } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getCustomStatusList } from 'src/app/reducers/status/status.reducer';
import {
  FetchIVRSettingListById,
  FetchRecentSearch,
  FetchUsersListForReassignment,
  UpdateUserSearch
} from 'src/app/reducers/teams/teams.actions';
import {
  getRecentSearch,
  getUserBasicDetails,
  getUsersByDesignation,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { ExportLeadsTrackerComponent } from 'src/app/shared/components/export-leads-tracker/export-leads-tracker.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { QrGeneratorComponent } from 'src/app/shared/components/qr-code/qr-generator.component';
import { environment as env } from 'src/environments/environment';
import { ExcelUploadedStatusComponent } from './excel-uploaded-status/excel-uploaded-status.component';
import { LeadNameSectionComponent } from './lead-name-section/lead-name-section.component';
import { LeadsActionsComponent } from './leads-actions/leads-actions.component';
import { LeadsAdvanceFilterComponent } from './leads-advance-filter/leads-advance-filter.component';
import { MatchingPropertiesComponent } from './matching-properties/matching-properties.component';

@Component({
  selector: 'manage-leads',
  templateUrl: './manage-leads.component.html',
})
export class ManageLeadsComponent extends BaseGridComponent implements OnInit {
  @ViewChild('parentLeadDeleted') parentLeadDeleted: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  visibilityList: Array<Object> = LEAD_VISIBILITY_IMAGE.slice(0, 3);
  _viewStates: Array<string> = Object.values(LEAD_STATUS).slice(1, 6);
  dateTypeList: Array<string> = DATE_TYPE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  visibility = LEADS_FILTERS_V2;
  dateFilters = DATE_FILTERS;
  scheduledTodayList: any = SCHEDULED_TODAY_LIST;
  permissions: any;
  showLeftNav: boolean = true;
  currentPath: string;
  s3BucketUrl: string = env.s3ImageBucketURL;
  filtersPayload: any = {};
  forceApiCall = false;
  filtersForm: FormGroup;
  formFields: any = {
    pageNumber: [1],
    pageSize: [10],
    leadVisibility: [0],
    SearchByNameOrNumber: [null],
    dateType: [null],
    fromDate: [null],
    toDate: [null],
    duplicateLeadIds: [null],
    shouldShowParentLead: [false],
    CustomFlags: [null],
    IsDualOwnershipEnabled: [false],
    baseUTcOffset: [null],
    timeZoneId: [null],
  };
  pageSize: number = PAGE_SIZE;

  gridOptions: GridOptions;
  gridApi: GridApi;
  gridColumnApi: any;
  isCustomStatusEnabled: boolean;
  customStatusList: any;
  isCustomStatusListLoading: any;
  LeadVisibilityEnum = LeadVisibility;
  selectedTrackerOption: string;
  selectedOption: string;
  secondLevelFilterList: any = [];
  masterLeadStatus: any;
  SecondLevelFilterEnum = SecondLevelFilter;
  LeadScheduledTypeEnum: any = LeadScheduledType;
  showWebUI: boolean = true;
  searchTerm: any;
  columns: any[];
  defaultColumns: any[];
  showSearchDropdown: boolean = false;
  searchFilters: any[] = [];
  allSearchFilters: any[] = [];
  selectedSearchFilters: any[] = [];
  defaultSearchFilterNames: string[] = ['Lead Name', 'Serial Number', 'Contact No'];
  selectedAdditionalFilters: string[] = [];
  recentSearches: string[] = [];
  getAssignedToDetails = getAssignedToDetails;
  getPages = getPages;
  formatBudget = formatBudget;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  getBedsDisplay = getBedsDisplay;
  getTimeZoneDate = getTimeZoneDate;
  globalSettingsData: any;
  customTopLevelFilters: any = [];
  isCustomTopFiltersLoading: boolean = true;
  isLeadDataLoading: boolean = true;
  isPrevFetched: boolean = false;
  customFiltersCount: any = {};
  customSecondLevelFilterList: any = [];
  customThirdLevelFilterList: any = [];
  customFourthLevelFilterList: any = [];
  baseFilterCount: any = [];
  baseFilterCountIsLoading: boolean = true;
  dateType: string = 'All';
  filterDate: any = [];
  userData: any;
  currentDate: Date;
  rowData: any = [];
  allUsers: any = [];
  defaultCurrency: string = 'INR';
  currentSelectionForLeadPreview: string = 'Overview';
  cardData: Array<any> = [];
  leadTags: Array<any> = [];
  onFilterChanged = onFilterChanged;
  usersListForReassignmentIsLoading: any;
  leadsTotalCount: any;
  selectedNodes: any = [];
  cardDataPageNumber: number = 1;
  activeLeadsCount: any = [];
  activeLeadsCountIsLoading: boolean = true;
  statusCounts: { [key: string]: number };
  isStatusCountsLoading: any = true;
  isShowParentLead: boolean = false;
  showParentLeadColumn: boolean = true;
  isParentLeadColumnVisible: boolean = false;
  flagsCount: any = [];
  leadsFiltersKeyLabel = LEAD_FILTERS_KEY_LABEL;
  propertyType: Array<any> = JSON.parse(localStorage.getItem('propertyType'));
  areaSizeUnits: any;
  designationList: { id: any; name: string }[];
  onPickerOpened: any = onPickerOpened;
  LeadSource: any = LeadSource;
  currentVisibilityName: string = 'All';
  parentLeadDataMap: Map<string, any> = new Map();
  canViewAllLeads: boolean = false;
  isParentLeadDeleted: boolean = false;
  isSavedFilterOpen: boolean = false;
  selectedFilter: string;
  selectedFilterId: any;
  FileForm: FormGroup;
  filters: any;
  columnMovedListener: any;
  isModuleWiseSearchPropertiesLoading: boolean;
  doesFileNameExist: boolean = false;
  selectedFilterName: any;
  Gender = Gender;
  MaritalStatusType = MaritalStatusType;
  showFilterCount: boolean = false;
  showCommunicationCount: boolean = false;
  leadLevelFilter: any;
  leadCustomStatusLastModifiedDate: any;

  get sortOrder(): 'asc' | 'desc' {
    return this.filtersPayload?.['SortingCriteria.IsAscending'] == true
      ? 'asc'
      : this.filtersPayload?.['SortingCriteria.IsAscending'] == false
        ? 'desc'
        : undefined;
  }

  get showFilters(): boolean {
    const filters = this.getFilteredPayload();
    const booleanFilters = ['ShowOnlyParentLeads', 'ShowPrimeLeads'];
    for (const filterName of booleanFilters) {
      if (filters?.[filterName] === true) return true;
      if (filters?.[filterName] === false) return true;
      if (filters?.[filterName] === null) return false;
    }
    return Object.values(filters).some((value) =>
      Array.isArray(value) ? value.length > 0 : Boolean(value) || value === 0
    ) || Boolean(filters?.landLine);
  }

  get filteredFilters(): any {
    let filters = this.getFilteredPayload()
    filters = {
      ...filters,
      ...(this.globalSettingsData?.isCustomLeadFormEnabled
        ? { BR: filters.NoOfBHKs, NoOfBHKs: null }
        : {}),
      ...(filters.ShowOnlyParentLeads !== undefined
        ? { ShowOnlyParentLeads: filters.ShowOnlyParentLeads ? "Yes" : "No" }
        : {}),
      ...(filters.ShowPrimeLeads !== undefined
        ? { ShowPrimeLeads: filters.ShowPrimeLeads ? "Yes" : "No" }
        : {}),
    };
    return filters;
  }
  get isFilterSelected(): boolean {
    let filters = {
      ...this.filtersPayload
    }
    Object.entries(filters).forEach(([key, value]) => {
      if (value || value === 0) {
        if (Array.isArray(value)) {
          filters[key] = getIndexes(key, value);
        } else {
          filters[key] = value;
        }
      }
    });
    return (
      this.selectedFilter &&
      this.selectedFilter === JSON.stringify(filters || {})
    );
  }
  get isFilterUpdated(): boolean {
    let filters = {
      ...this.filtersPayload
    }
    Object.entries(filters).forEach(([key, value]) => {
      if (value || value === 0) {
        if (Array.isArray(value)) {
          filters[key] = getIndexes(key, value);
        } else {
          filters[key] = value;
        }
      }
    });
    return (
      this.selectedFilter &&
      this.selectedFilter !== JSON.stringify(filters || {})
    );
  }

  private getFilteredPayload(): any {
    const ignoredKeys = new Set([
      'SearchByNameOrNumber',
      'PropertyToSearch',
      'IsWithTeam',
      'CustomFilterIds',
      'IsWithHistory',
      'pageSize',
      'pageNumber',
      'leadVisibility',
      'FilterTypes',
      'fromDate',
      'toDate',
      'dateType',
      'ScheduledType',
      'SecondLevelFilter',
      'ToDateForMeetingOrVisit',
      'ScheduledDateTypeFilter',
      'FirstLevelFilter',
      'CustomFirstLevelFilter',
      'CustomSecondLevelFilter',
      'CustomThirdLevelFilter',
      'CustomFourthLevelFilter',
      'UpperAgreementLimit',
      'UpperDiscountLimit',
      'IsDualOwnershipEnabled',
      'CustomFilterId',
      'shouldShowBookedDetails',
      'shouldShowBrokerageInfo',
      'shouldShowParentLead',
      'path',
      'CanAccessAllLeads',
      'Currency',
      'SaleableAreaUnitId',
      'CarpetAreaUnitId',
      'BuiltUpAreaUnitId',
      'PropertyAreaUnitId',
      'NetAreaUnitId',
      'SecondLevelFilterId',
      'duplicateLeadIds',
      'SortingCriteria.ColumnName',
      'SortingCriteria.IsAscending',
      'CallDateType',
      'CallLogToDate',
      'showCommunicationCount',
      'showFilterCount',
      'baseUTcOffset',
      'timeZoneId',
      'dontCallApi'
    ]);

    return Object?.fromEntries(
      Object?.entries(this.filtersPayload).filter(
        ([key, value]) => !ignoredKeys.has(key) && value != null
      )
    );
  }

  get isMobileView(): boolean {
    const width = window.innerWidth;
    if (width > 480) this.showWebUI = true;
    return width <= 480;
  }
  constructor(
    private gridOptionsService: GridOptionsService,
    private modalService: BsModalService,
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    public router: Router,
    public activatedRoute: ActivatedRoute,
    public metaTitle: Title,
    private shareDataService: ShareDataService,
    private fb: FormBuilder,
    public modalRef: BsModalRef,
    public trackingService: TrackingService,
    private changeDetectorRef: ChangeDetectorRef,
  ) {
    super();
  }

  async ngOnInit() {
    // Always reset these flags on component initialization
    this.showFilterCount = false;
    this.showCommunicationCount = false;
    if (this.filtersPayload) {
      this.filtersPayload.showFilterCount = false;
      this.filtersPayload.showCommunicationCount = false;
    }
    if (this.filtersForm) {
      this.filtersForm.patchValue({
        showFilterCount: false,
        showCommunicationCount: false
      });
    }
    this.trackingService.trackFeature(`Web.Leads.Page.Leads.Visit`);
    this.initializeComponent();

    await this.initializeSubscriptions();
    let isFirstChange = true;
    this.FileForm
      .get('name')
      .valueChanges.pipe(debounceTime(300))
      .subscribe((value: any) => {
        if (isFirstChange) {
          isFirstChange = false;
          return;
        }

        if (
          value &&
          this.FileForm.controls.name.status === 'VALID'
        ) {
          this.doesFileNameExists();
        }
      });
    this._store.select(getLeadCustomTopFilters)
      .pipe(
        filter((data: any) => !!data && (Array.isArray(data) ? data.length > 0 : Object.keys(data).length > 0)),
        take(1)
      )
      .subscribe((data: any) => {
        localStorage.setItem('leadLevelFilter', JSON.stringify(data));
      });
    if (!localStorage.getItem('leadCustomStatusLastModifiedDate')) {
      this._store.select(getFetchModifiedDatesList)
        .pipe(takeUntil(this.stopper))
        .subscribe((result: any) => {
          const data = result.data;
          if (!isEmptyObject(data)) {
            localStorage.setItem('leadCustomStatusLastModifiedDate', JSON.stringify(data?.CustomMasterLeadStatus));
          }
        });
    }
  }

  initializeComponent(): void {
    this._store.dispatch(new FetchModifiedDatesList());
    this._store.dispatch(
      new UpdateIsCardView(window.innerWidth <= 480 && this.showWebUI)
    );
    this.FileForm = this.fb.group({
      name: [null, [Validators.required]],
    });
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this._store.dispatch(new FetchIVRSettingListById(userId));
    this._store.dispatch(new FetchEmailSMTPListByUserId(userId));
    this._store.dispatch(new FetchFilter());
    this.filtersForm = this.fb.group(this.formFields);

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.shareDataService.apiTrigger$.subscribe((value: boolean) => {
      if (value === true) {
        this.forceApiCall = true;
        this.filterFunction()

      }
    });
    this.headerTitle.setLangTitle('Manage Leads');
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this.currentPath === '/invoice'
      ? this.metaTitle.setTitle('CRM | Invoice')
      : this.metaTitle.setTitle('CRM | Leads');
    this._store.dispatch(new FetchUsersListForReassignment());
  }

  async initializeSubscriptions() {
    this.customTopLevelFilters = await firstValueFrom(
      this._store.select(getLeadCustomTopFilters)
    );

    this.globalSettingsData = await firstValueFrom(
      this._store
        .select((state) => state.globalSettings)
        .pipe(
          skipWhile((state) => state.globalAnonymousIsLoading),
          take(1)
        )
    );

    this.dateTypeList = this.dateTypeList?.filter((item: any) => item !== 'Possession Date');
    this.globalSettingsData = this.globalSettingsData.globalSettingsAnonymous;
    this.filtersForm.patchValue({
      IsDualOwnershipEnabled: this.globalSettingsData?.isDualOwnershipEnabled,
    });
    this.defaultCurrency =
      this.globalSettingsData.countries &&
        this.globalSettingsData.countries.length
        ? this.globalSettingsData.countries[0].defaultCurrency
        : null;

    this.isCustomStatusEnabled = await firstValueFrom(
      this._store.select(getIsLeadCustomStatusEnabled)
    );

    const permission = await firstValueFrom(
      this._store.select(getPermissions).pipe(
        takeUntil(this.stopper),
        skipWhile((data) => !data?.length)
      )
    );

    this.permissions = new Set(permission);

    const visibilityChecks = [
      { permission: 'Permissions.Leads.ViewUnAssignedLead', index: 3 },
      { permission: 'Permissions.Leads.Delete', index: 4 },
      { permission: 'Permissions.Leads.ViewDuplicateTag', index: 5 },
    ];

    visibilityChecks.forEach(({ permission, index }) => {
      if (
        this.permissions.has(permission) &&
        !this.visibilityList?.some(
          (item: any) => item?.name === LEAD_VISIBILITY_IMAGE[index].name
        ) &&
        (index === 3 ? this.currentPath !== '/invoice' : true)
      ) {
        this.visibilityList = [
          ...this.visibilityList,
          LEAD_VISIBILITY_IMAGE[index],
        ];
      }
    });

    if (this.permissions.has('Permissions.Leads.ViewAllLeads')) {
      this.canViewAllLeads = true
      this.filtersForm.addControl('CanAccessAllLeads', this.fb.control(true));
    }

    this.userData = await firstValueFrom(
      this._store.select(getUserBasicDetails)
    );

    this.currentDate = changeCalendar(
      this.userData?.timeZoneInfo?.baseUTcOffset
    );

    this.filtersForm.patchValue({
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
    });

    this.masterLeadStatus = JSON.parse(
      localStorage.getItem('masterleadstatus') || '[]'
    );

    this.leadLevelFilter = JSON.parse(
      localStorage.getItem('leadLevelFilter') || '[]'
    );

    if (!(this.currentPath === '/invoice')) {
      const additionalFormFields: any = !this.isCustomStatusEnabled
        ? {
          FirstLevelFilter: [1],
          SecondLevelFilter: [null],
          ScheduledDateTypeFilter: [null],
          ScheduledType: [null],
          SecondLevelFilterId: [null],
        }
        : {
          CustomFirstLevelFilter: [null],
          CustomSecondLevelFilter: [null],
          CustomThirdLevelFilter: [null],
          CustomFourthLevelFilter: [null],
          CustomFilterId: [null],
        };

      Object.keys(additionalFormFields).forEach((key) => {
        this.filtersForm.addControl(
          key,
          this.fb.control(additionalFormFields[key][0])
        );
      });

      this._store
        .select(getDuplicate)
        .pipe(
          filter(
            (duplicate: any) => duplicate.isDuplicate && duplicate.duplicateId
          ),
          take(1),
          tap((duplicate: any) => {
            this.filtersForm.patchValue({
              duplicateLeadIds: duplicate.duplicateId,
              leadVisibility: 5,
              pageNumber: 1,
            });
            this.isShowParentLead = true;
            this.filterFunction();
          })
        )
        .subscribe();

      this.shareDataService
        .getDuplicateMethodTrigger()
        .pipe(takeUntil(this.stopper))
        .subscribe(({ selectedLeadId }: { selectedLeadId: string }) => {
          this.filtersForm.patchValue({
            duplicateLeadIds: selectedLeadId,
            leadVisibility: 5,
            pageNumber: 1,
          });
          this.isShowParentLead = true;
          this.filterFunction();
        });

      this.shareDataService.fetchNextLeads
        .pipe(takeUntil(this.stopper))
        .subscribe((isMobileView: boolean) => {
          if (isMobileView) {
            return this.loadMore();
          }
          const totalPages = Math.ceil(
            this.leadsTotalCount / this.filtersPayload?.pageSize
          );
          if (this.filtersPayload?.pageNumber === totalPages) {
            return;
          }
          this.filtersForm.patchValue({
            pageNumber: this.filtersPayload?.pageNumber + 1,
          });
          this._store.dispatch(new UpdateIsLoadMore(true));
          this.filterFunction(true);
        });
      this.shareDataService.fetchPreviousLeads
        .pipe(takeUntil(this.stopper))
        .subscribe(({ isMobileView, currentPageNumber }) => {
          if (isMobileView || this.filtersPayload?.pageNumber == 1) {
            return;
          }
          this.isPrevFetched = true;
          this.filtersForm.patchValue({
            pageNumber: this.filtersPayload?.pageNumber - 1,
          });
          this.filterFunction();
        });

      this.filtersForm
        .get('CustomFirstLevelFilter')
        ?.valueChanges.subscribe(() => {
          this.onCustomFirstLevelFilterChange();
        });
      this.filtersForm
        .get('CustomSecondLevelFilter')
        ?.valueChanges.subscribe((value: any) => {
          if (value) {
            this.onCustomSecondLevelFilterChange(value);
          }
        });
      this.filtersForm
        .get('CustomThirdLevelFilter')
        ?.valueChanges.subscribe(() => {
          this.onCustomThirdLevelFilterChange();
        });
      this.filtersForm.get('FirstLevelFilter')?.valueChanges.subscribe(() => {
        this.onFirstLevelChange();
      });
      this.filtersForm.get('shouldShowParentLead')
        .valueChanges.subscribe((value: boolean) => {
          this.showParentLeadColumn = value;
          this.changeDetectorRef.detectChanges();
        });
    }
    this._store
      .select(
        this.currentPath === '/invoice'
          ? getInvoiceFiltersPayload
          : getFiltersPayload
      )
      .pipe(
        takeUntil(this.stopper),
        skipWhile((data: any) => !Object.keys(data)?.length)
      )
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, dontCallApi: false };
        this.searchTerm = data?.SearchByNameOrNumber;
        this.filtersForm.patchValue({
          ...this.filtersPayload,
        });
        this.dateType = LeadDateType[data?.dateType] ?? 'All';
        this.filterDate = data?.fromDate
          ? [
            patchTimeZoneDate(
              data?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              data?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ]
          : [];
        this.filtersPayload.leadVisibility
          ? (this.currentVisibilityName =
            this.LeadVisibilityEnum[this.filtersPayload.leadVisibility])
          : null;
      });
    const params: any = await firstValueFrom(this.activatedRoute.queryParams);
    if (params?.Source && params?.isNavigatedFromSource) {
      try {
        const sourcesValue = typeof params.Source === 'string' ? JSON.parse(params.Source) : params.Source;
        this.filtersPayload.Source = sourcesValue;
      } catch (error) {
        console.error('Error parsing Source parameter:', error);
      }
    }
    if (!this.isCustomStatusEnabled && !(this.currentPath === '/invoice')) {
      Object.keys(params)?.length
        ? this.dashboardNavigation(params)
        : this.gridOptionsService?.data &&
          Object.values({ ...this.gridOptionsService?.data }).some((value) =>
            Boolean(value)
          )
          ? this.reportsNavigation()
          : this.filterFunction();
    } else if (
      this.isCustomStatusEnabled &&
      !(this.currentPath === '/invoice')
    ) {
      const storedleadLevelFilter = localStorage.getItem('leadLevelFilter');
      const storedLastModified = localStorage.getItem('leadCustomStatusLastModifiedDate');

      if (storedleadLevelFilter && storedLastModified) {
        try {

          this._store
            .select(getFetchModifiedDatesList)
            .pipe(
              takeUntil(this.stopper),
              skipWhile((result: any) => !result || !result.data || Object.keys(result.data).length === 0),
              take(1)
            )
            .subscribe((result: any) => {
              const data = result.data;
              const apiLastModified = data?.CustomMasterLeadStatus;

              let parsedStoredLastModified;
              try {
                parsedStoredLastModified = JSON.parse(storedLastModified);
              } catch (error) {
                parsedStoredLastModified = storedLastModified;
              }
              const storedDate = new Date(parsedStoredLastModified);
              const apiDate = new Date(apiLastModified);

              if (apiLastModified && apiDate.getTime() > storedDate.getTime()) {
                this._store.dispatch(new FetchLeadCustomTopFilters());
                localStorage.setItem('leadCustomStatusLastModifiedDate', JSON.stringify(apiLastModified));
              } else {
                const parsedData = JSON.parse(storedleadLevelFilter);
                this._store.dispatch(new FetchLeadCustomTopFiltersSuccess(parsedData));
              }
            });
        } catch (error) {
          this._store.dispatch(new FetchLeadCustomTopFilters());
        }
      } else {
        this._store.dispatch(new FetchLeadCustomTopFilters());
      }
    }
    if (this.currentPath === '/invoice') {
      Object.keys(params)?.length
        ? this.dashboardNavigation(params)
        : this.gridOptionsService?.data &&
          Object.values({ ...this.gridOptionsService?.data }).some((value) =>
            Boolean(value)
          )
          ? this.reportsNavigation()
          : this.filterFunction();
    }

    this._store.dispatch(new FetchTagsList());

    this._store
      .select(getTagsList)
      .pipe(
        takeUntil(this.stopper),
        skipWhile((data: any) => !data?.length)
      )
      .subscribe((data: any) => {
        this.leadTags = data
          ?.filter((flag: any) => flag?.isActive)
          .sort((a: any, b: any) =>
            a?.name.toLowerCase().localeCompare(b?.name.toLowerCase())
          );
      });

    this._store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus
          ?.slice()
          .sort((a: any, b: any) =>
            a?.displayName.localeCompare(b?.displayName)
          );
      });

    this._store
      .select(getModuleWiseSearchProperties)
      .pipe(
        takeUntil(this.stopper),
        skipWhile((data: any) => !data?.length)
      )
      .subscribe((data: any) => {
        this.searchFilters = data;
        this.allSearchFilters = [...data];
        if (this.allSearchFilters.length) {
          const defaultFilters = this.allSearchFilters.filter(f =>
            this.defaultSearchFilterNames.includes(f.displayName)
          );
          const combinedFilters = [...this.selectedSearchFilters, ...defaultFilters];
          const uniqueFilters = Array.from(new Map(combinedFilters.map(item => [item.propertyName, item])).values());
          this.selectedSearchFilters = uniqueFilters;
        }
      });
    this._store
      .select(getModuleWiseSearchPropertiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading) => (this.isModuleWiseSearchPropertiesLoading = isLoading));

    this._store
      .select(getFlagsCounts)
      .pipe(
        takeUntil(this.stopper),
        skipWhile((data: any) => !data?.length)
      )
      .subscribe((data: any) => {
        this.flagsCount = data;
      });

    if (this.isCustomStatusEnabled) {
      if (
        this.getFormValue('CustomFirstLevelFilter') &&
        this.customTopLevelFilters.length
      ) {
        Object.keys(params)?.length
          ? this.dashboardNavigation(params)
          : this.gridOptionsService?.data &&
            Object.values({ ...this.gridOptionsService?.data }).some((value) =>
              Boolean(value)
            )
            ? this.reportsNavigation()
            : this.filterFunction();
      }
      this._store
        .select(getLeadCustomTopFiltersIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading) => (this.isCustomTopFiltersLoading = isLoading));
      this._store
        .select(getLeadCustomTopFilters)
        .pipe(
          takeUntil(this.stopper),
          skipWhile(
            () =>
              this.isCustomTopFiltersLoading || this.currentPath === '/invoice'
          )
        )
        .subscribe((data) => {
          this.customTopLevelFilters = [...data];
          const defaultFilter =
            this.customTopLevelFilters?.find(
              (filter: any) => filter?.isDefault
            ) ||
            this.customTopLevelFilters?.find(
              (filter: any) => filter?.name === 'Active Leads'
            ) ||
            this.customTopLevelFilters?.[0];

          if (
            this.filtersForm.get('CustomFirstLevelFilter') &&
            !this.getFormValue('CustomFirstLevelFilter')
          ) {
            this.filtersForm
              .get('CustomFirstLevelFilter')
              .setValue(defaultFilter?.id ?? EMPTY_GUID);
            this.customSecondLevelFilterList = this.customTopLevelFilters?.find(
              (filter: any) => filter?.id === defaultFilter?.id
            )?.childType;
            Object.keys(params)?.length
              ? this.dashboardNavigation(params)
              : this.gridOptionsService?.data &&
                Object.values({ ...this.gridOptionsService?.data }).some(
                  (value) => Boolean(value)
                )
                ? this.reportsNavigation()
                : this.filterFunction();
          }
          this.customFiltersCount = this.extractFilterCounts(
            this.customTopLevelFilters
          );
        });
    }

    this._store
      .select(getBaseFilterCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading) => (this.baseFilterCountIsLoading = loading));

    this._store
      .select(getLeadBaseFilterCount)
      .pipe(
        takeUntil(this.stopper),
        skipWhile(() => this.baseFilterCountIsLoading)
      )
      .subscribe((data) => (this.baseFilterCount = { ...data }));

    this._store
      .select(getLeadListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading) => {
        this.isLeadDataLoading = isLoading;
      });

    this._store
      .select(getLeads)
      .pipe(
        takeUntil(this.stopper),
        skipWhile(() => this.isMobileView)
      )
      .subscribe((data) => {

        if (JSON.stringify(data) === JSON.stringify(this.rowData)) return;
        this.rowData = [...data];
        this.cardData = this.rowData;
        if (!this.isLeadDataLoading) {
          this.shareDataService.emitDataUpdated({
            cardData: this.rowData,
            isPrevFetched: this.isPrevFetched,
          });
        }
        this.shareDataService.currentPageNumber =
          this.filtersPayload?.pageNumber;
        this.shareDataService.totalPages = Math.ceil(
          this.leadsTotalCount / this.filtersPayload?.pageSize
        );
        if (this.currentPath !== '/invoice' && this.isParentLeadColumnVisible) {
          this.fetchAllParentLeadData(this.rowData);
        }
        this.initializeGridSettings();
      });

    this._store
      .select(getLeadCardData)
      .pipe(
        takeUntil(this.stopper),
        skipWhile(() => !this.isMobileView)
      )
      .subscribe(async (data: any) => {
        this.rowData = await firstValueFrom(this._store.select(getLeads));
        this.initializeGridSettings();

        this.cardData = data;
        if (!this.isLeadDataLoading) {
          this.shareDataService.emitDataUpdated({
            cardData: this.cardData,
            isPrevFetched: this.isPrevFetched,
          });
        }
      });

    this._store
      .select(getLeads)
      .pipe(takeUntil(this.stopper))
      .subscribe((leads) => {
        if (
          JSON.stringify(leads) === JSON.stringify(this.rowData) &&
          !this.isMobileView
        )
          return;
        this.rowData = [...leads];
        this.cardData = this.isMobileView
          ? [...this.cardData, ...this.rowData]
          : this.rowData;
        this.shareDataService.currentPageNumber =
          this.filtersPayload?.pageNumber;
        this.shareDataService.totalPages = Math.ceil(
          this.leadsTotalCount / this.filtersPayload?.pageSize
        );
      });

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (isLoading) => (this.usersListForReassignmentIsLoading = isLoading)
      );

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        skipWhile(() => this.usersListForReassignmentIsLoading)
      )
      .subscribe((data: any) => {
        const sortedUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
      });

    this._store
      .select(getLeadsCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => (this.leadsTotalCount = data?.totalCount));

    this._store
      .select(getActiveLeadsCountsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading) => (this.activeLeadsCountIsLoading = loading));

    this._store
      .select(getActiveLeadsCounts)
      .pipe(
        takeUntil(this.stopper),
        skipWhile(() => this.activeLeadsCountIsLoading)
      )
      .subscribe((data) => {
        this.activeLeadsCount = {
          ...data,
          All: data.scheduledLeadsCount,
          'Scheduled Today': data.scheduledTodayLeadsCount,
          'Scheduled Tomorrow': data.scheduledTomorrowLeadsCount,
          'Scheduled next 2 days': data.scheduledNextTwoDaysLeadsCount,
          'Upcoming Schedules': data.upcomingScheduledLeadsCount,
          'Site visits': data.siteVisitsCount ?? data?.overdueSiteVisitCount,
          Meetings: data.meetingsCount ?? data?.overdueMeetingCount,
          Callbacks: data.callbackCount ?? data?.overdueCallbackCount,
        };
      });

    this._store
      .select(getLeadStatusCounts)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.isStatusCountsLoading = data?.isLoading;
        let counts = this.extractStatusCounts(data?.counts);
        this.statusCounts = {};
        this.masterLeadStatus.forEach((status: any) => {
          this.statusCounts[status.displayName] = counts[status.id] || 0;
          status.childTypes.forEach((child: any) => {
            this.statusCounts[child.displayName] = counts[child.id] || 0;
          });
        });
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store
      .select(getUsersByDesignation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.designationList = Object.entries(data)
          .map(([key, value]: [string, any]) => ({
            id: value[0]?.designation?.id,
            name: key,
          }))
          .filter((item) => item.name !== 'No Designation');
      });
    this._store
      .select(getFilter)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filters = data;
      });
    this._store
      .select(getSavedFilter)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.selectedFilterName = data?.data?.name
        this.selectedFilterId = data?.data?.id;
        this.FileForm.patchValue({
          name: data?.data?.name
        });
      }
      );
    if (
      this.gridOptionsService?.data &&
      Object.values({ ...this.gridOptionsService?.data }).some((value) =>
        Boolean(value)
      )
    ) {
      this.reportsNavigation();
    }
  }

  openLeadsTracker() {
    const modalConfig: any = {
      bulkUpload: {
        component: ExcelUploadedStatusComponent,
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState: { fieldType: 'leads' },
        action: () => this._store.dispatch(new FetchExcelUploadedList(1, 10)),
      },
      export: {
        component: ExportLeadsTrackerComponent,
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
        action: () => this._store.dispatch(new FetchExportStatus(1, 10)),
      },
      bulk: {
        component: BulkOperationTrackerComponent,
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        action: () =>
          this._store.dispatch(new FetchBulkOperation(1, 10, 'lead')),
        initialState: {
          moduleType: 'lead',
        },
      },
    };
    const selectedOption = modalConfig[this.selectedTrackerOption];
    if (selectedOption) {
      selectedOption.action();
      this.modalService.show(selectedOption.component, {
        class: selectedOption?.class,
        initialState: selectedOption?.initialState,
      });
    }
    this.selectedTrackerOption = '';
  }

  navigateToAddLead() {
    this.router.navigate(['leads/add-lead']);
    // /need to check with varun
    this.trackingService.trackFeature(`Web.Leads.Button.AddLead.Click`);
  }

  getFormValue(controlName: string) {
    return this.filtersForm?.get(controlName)?.value;
  }

  currentVisibility(visibility: any) {
    const currentVisibilityValue = this.getFormValue('leadVisibility');
    const newVisibilityValue = LeadVisibility[visibility];

    if (currentVisibilityValue === newVisibilityValue) {
      return;
    }

    this.filtersForm.patchValue({
      pageNumber: 1,
      leadVisibility: newVisibilityValue,
      ...(visibility !== 'Duplicate'
        ? {
          shouldShowParentLead: false,
          duplicateLeadIds: null,
          pageNumber: 1,
        }
        : {}),
    });

    this.isShowParentLead =
      this.filtersForm.value.duplicateLeadIds && visibility === 'Duplicate';

    if (visibility === 'My Leads') {
      this.filtersPayload = {
        ...this.filtersPayload,
        assignTo: [],
        SecondaryUsers: [],
      };
    }

    this.trackingService.trackFeature(
      `Web.Leads.Menu.${visibility.replace(/\s+/g, '')}.Click`
    );

    this.filterFunction();
  }

  openBulkUploadQR() {
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['leads/bulk-upload']);
      this.trackingService.trackFeature('Web.Leads.Button.BulkUpload.Click');
    } else if (this.selectedOption === 'QRCode') {
      this.modalService.show(QrGeneratorComponent, {
        class: 'modal-450 right-modal ip-modal-unset',
      });
      this.trackingService.trackFeature('Web.Leads.Options.QRGenerator.Click');
    }
    this.selectedOption = '';
  }

  onFirstLevelChange(): void {
    const firstLevelFilter = this.getFormValue('FirstLevelFilter');
    if ([0, 1].includes(firstLevelFilter)) {
      this.secondLevelFilterList = [];
      return;
    }
    const selectedStatus = this.visibility?.find(
      (item: any) => item.value === firstLevelFilter
    );
    const statusName = this.masterLeadStatus?.find(
      (status: any) => status.status === selectedStatus?.status
    )?.status;

    this.secondLevelFilterList =
      this.masterLeadStatus
        ?.find((status: any) => status.status === statusName)
        ?.childTypes?.map(({ displayName, id }: any) => ({
          displayName,
          id,
        })) || [];

    this.filtersForm.patchValue({
      SecondLevelFilter: null,
      SecondLevelFilterId: null,
      ScheduledDateTypeFilter: null,
      ScheduledType: null,
      pageNumber: 1,
    });

  }

  onSearch() {
    if (this.searchTerm) {
      if (this.recentSearches?.length > 0 && this.selectedSearchFilters?.length === 0) {
        this.selectedSearchFilters = [...this.recentSearches];
        this.recentSearches = [];
      }

      const searchPayload: any = {
        SearchByNameOrNumber: this.searchTerm,
        ...(!this.isCustomStatusEnabled ? { FirstLevelFilter: 0 } : {}),
        pageNumber: 1,
      };

      if (this.selectedSearchFilters?.length > 0) {
        searchPayload.SearchFilters = this.selectedSearchFilters;
      }

      if (this.selectedAdditionalFilters?.length > 0) {
        searchPayload.AdditionalFilters = this.selectedAdditionalFilters;
      }
      const displayedFilters = this.getSelectedFilters();
      if (displayedFilters && displayedFilters?.length > 0) {
        const propertyNames = displayedFilters.map(filter => filter.propertyName);
        searchPayload.PropertyToSearch = propertyNames;
        this.filtersPayload = {
          ...this.filtersPayload,
          PropertyToSearch: propertyNames
        };
        const recentSearched = this.recentSearches?.map((item: any) => item?.propertyName);
        if (!propertyNames?.includes(recentSearched)) {
          this._store.dispatch(new UpdateUserSearch({
            module: 0,
            searchResults: propertyNames
          }));
        }
      }

      if (this.getSelectedFilters()?.length === 0) {
        this._store.dispatch(new UpdateUserSearch({
          module: 0,
          searchResults: []
        }));
      }

      this.filtersForm.patchValue(searchPayload);
      this.showSearchDropdown = false;
      this.filterFunction();
    }
    this.trackingService.trackFeature('Web.Leads.Dataentry.Search.Data Entry');
  }

  isEmptyInput() {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.filtersForm.patchValue({
        SearchByNameOrNumber: null,
        PropertyToSearch: null,
        ...(!this.isCustomStatusEnabled ? { FirstLevelFilter: 0 } : {}),
        pageNumber: 1,
      });
      this.filterFunction();
    }
  }

  toggleSearchDropdown() {
    if (!this.showSearchDropdown) {
      this._store.dispatch(new FetchModuleWiseSearchProperties(0));
      this._store.dispatch(new FetchRecentSearch('0'));
    }
    this.showSearchDropdown = !this.showSearchDropdown;
    if (this.showSearchDropdown) {
      this._store.select(getRecentSearch)
        .pipe(
          takeUntil(this.stopper)
        )
        .subscribe((recentSearches: any[]) => {
          const recentFilterObjects = (recentSearches && recentSearches.length > 0) ? recentSearches : [];
          const combinedFilters = [...this.selectedSearchFilters, ...recentFilterObjects];
          const uniqueFilters = Array.from(new Map(combinedFilters.map(item => [item.propertyName, item])).values());
          this.selectedSearchFilters = uniqueFilters;
        });
    }
  }

  isSelectedFilter(filter: any): boolean {
    return this.selectedSearchFilters?.some((f: any) => {
      return (typeof f === 'string' ? f : f?.propertyName) === filter?.propertyName;
    });
  }

  getDisplayedFilters() {
    const selectedProps = [
      ...(this.selectedSearchFilters || []),
      ...(this.recentSearches || [])
    ].map((f: any) => typeof f === 'string' ? f : f?.propertyName);

    return this.allSearchFilters?.filter(
      (filter: any) => !selectedProps.includes(filter?.propertyName)
    )?.slice(0, 15);
  }


  toggleSearchFilter(filter: any) {
    if (this.isDefaultFilter(filter)) {
      return;
    }

    const filterKey = typeof filter === 'string' ? filter : filter?.propertyName;

    const selectedIndex = this.selectedSearchFilters?.findIndex(
      (item: any) => (typeof item === 'string' ? item : item?.propertyName) === filterKey
    );
    const recentIndex = this.recentSearches?.findIndex(
      (item: any) => item?.propertyName === filterKey
    );


    if (selectedIndex > -1) {
      this.selectedSearchFilters.splice(selectedIndex, 1);
      return;
    }

    if (recentIndex > -1) {
      const updatedRecentSearches = [...this.recentSearches];
      updatedRecentSearches.splice(recentIndex, 1);
      this.selectedSearchFilters = [...updatedRecentSearches];
      this.recentSearches = [];
      return;
    }

    if (this.recentSearches?.length > 0 && this.selectedSearchFilters?.length === 0) {
      this.selectedSearchFilters = [...this.recentSearches];
      this.recentSearches = [];
    }

    const totalFilters = this.selectedSearchFilters?.length;
    if (totalFilters < 7) {
      const matchingFilter = this.allSearchFilters?.find(f => f.propertyName === filterKey);
      if (matchingFilter) {
        const updatedSelectedFilters = [...this.selectedSearchFilters, matchingFilter];
        this.selectedSearchFilters = updatedSelectedFilters;
      }
    }
  }

  isDefaultFilter(filter: any): boolean {
    return this.defaultSearchFilterNames.includes(filter.displayName);
  }

  getSelectedFilters() {
    if (this.selectedSearchFilters?.length > 0) {
      const selectedSearches = this.selectedSearchFilters?.map((item: any) => item?.propertyName);
      return this.allSearchFilters?.filter(filter =>
        selectedSearches?.includes(filter?.propertyName)
      ) || [];
    }
    if (this.recentSearches?.length > 0) {
      const recentSearches = this.recentSearches?.map((item: any) => item?.propertyName);
      return this.allSearchFilters?.filter(filter =>
        recentSearches.includes(filter?.propertyName)
      ) || [];
    }
    return [];
  }

  openModal(component: any, data: any) {
    this.modalRef = this.modalService.show(component, {
      ...data,
    });
  }

  openAdvFiltersModal(updatedFilter: any) {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
      initialState: {
        filterData: updatedFilter,
        initialFiltersPayload: {
          ...this.filtersPayload,
          showFilterCount: this.showFilterCount,
          showCommunicationCount: this.showCommunicationCount
        }
      },
    };
    this.modalRef = this.modalService.show(LeadsAdvanceFilterComponent, {
      ...initialState,
    });
    this.modalRef.content?.filterChanged.subscribe((data: boolean) => {
      if (data) {
        this.cardData = [];
      }
    });
    this.trackingService.trackFeature('Web.Leads.Page.AdvanceFilters.Click');
  }

  onResetFilter(event: Event, control: string) {
    event.stopPropagation();
    this.filtersForm
      .get(control)
      ?.setValue(null, { emitEvent: control !== 'CustomSecondLevelFilter' });
    this.filtersForm.patchValue({
      pageNumber: 1,
    });
    if (control === 'CustomSecondLevelFilter') {
      this.onCustomSecondLevelFilterChange();
    } else {
      this.filterFunction();
    }
  }

  extractFilterCounts(data: any[]): { [key: string]: number } {
    const counts: any = {};

    function recurse(item: any): void {
      if (item.id && typeof item.count === 'number') {
        counts[item.id] = item.count;
      }

      if (Array.isArray(item.childType)) {
        item.childType.forEach(recurse);
      }
    }
    data.forEach(recurse);

    return counts;
  }

  onCustomSecondLevelFilterClick(filterId: string) {
    if (this.getFormValue('CustomSecondLevelFilter') !== filterId) {
      this.onCustomSecondLevelFilterChange(filterId);
    }
  }

  dateChange() {
    if (this.dateType && this.filterDate?.[0]) {
      this.filtersForm.patchValue({
        dateType: LeadDateType[this.dateType as keyof typeof LeadDateType],
        fromDate: setTimeZoneDate(
          this.filterDate?.[0],
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        toDate: setTimeZoneDate(
          this.filterDate?.[1],
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        pageNumber: 1,
      });
      if (this.dateType) {
        this.trackingService.trackFeature(
          `Web.Leads.Options.${this.dateType.replace(/\s+/g, '')}.Click`
        );
      } else if (this.filterDate?.[0]) {
        this.trackingService.trackFeature(
          `Web.Leads.Range.CalenderRange.Click`
        );
      }
      this.filterFunction();
    }
  }

  onCustomFirstLevelFilterChange() {
    this.customSecondLevelFilterList = this.customTopLevelFilters?.find(
      (filter: any) =>
        filter?.id === this.getFormValue('CustomFirstLevelFilter')
    )?.childType;
    this.customThirdLevelFilterList = [];
    this.customFourthLevelFilterList = [];
    this.filtersForm.patchValue(
      {
        CustomSecondLevelFilter: null,
        CustomThirdLevelFilter: null,
        CustomFourthLevelFilter: null,
        CustomFilterId: null,
        pageNumber: 1,
      },
      { emitEvent: false }
    );

  }

  onCustomSecondLevelFilterChange(filterId?: string) {
    this.customThirdLevelFilterList = [];
    this.customFourthLevelFilterList = [];
    this.customThirdLevelFilterList = this.customSecondLevelFilterList?.find(
      (filter: any) => filter?.id === filterId
    )?.childType;
    this.filtersForm.patchValue(
      {
        CustomSecondLevelFilter: filterId ?? null,
        CustomThirdLevelFilter: filterId
          ? this.customThirdLevelFilterList?.[0]?.id
          : null,
        CustomFourthLevelFilter: null,
        CustomFilterId: null,
        pageNumber: 1,
      },
      { emitEvent: false }
    );
    if (!filterId) {
      this.filterFunction();
    }

  }

  onCustomThirdLevelFilterChange() {
    this.customFourthLevelFilterList = [];
    this.customFourthLevelFilterList = this.customThirdLevelFilterList?.find(
      (filter: any) =>
        filter?.id === this.getFormValue('CustomThirdLevelFilter')
    )?.childType;
    this.filtersForm.patchValue(
      {
        CustomFourthLevelFilter: null,
        CustomFilterId: null,
        pageNumber: 1,
      },
      { emitEvent: false }
    );

  }

  check() {
    if (this.forceApiCall) {
      this.forceApiCall = false;
      return false;
    }
    if (this.isCustomStatusEnabled) {
      if (this.filtersPayload.CustomFirstLevelFilter !== this.getFormValue('CustomFirstLevelFilter')) {
        return true
      }
      if (this.filtersPayload.CustomSecondLevelFilter !== this.getFormValue('CustomSecondLevelFilter')) {
        return this.customThirdLevelFilterList?.length > 0 ? false : true;
      }
      if (this.filtersPayload.CustomThirdLevelFilter !== this.getFormValue('CustomThirdLevelFilter')) {
        return true;
      }
      if (this.filtersPayload.CustomFourthLevelFilter !== this.getFormValue('CustomFourthLevelFilter')) {
        return true;
      }

      return false;
    } else {
      if (this.filtersPayload.FirstLevelFilter !== this.getFormValue('FirstLevelFilter')) {
        return true
      }
      if (this.filtersPayload.SecondLevelFilter !== this.getFormValue('SecondLevelFilter')) {
        const isScheduledOrOverdue = (this.getFormValue('SecondLevelFilter') === 3 || this.getFormValue('SecondLevelFilter') === 4);
        return isScheduledOrOverdue ? false : true
      }
      if (this.filtersPayload.ScheduledDateTypeFilter !== this.getFormValue('ScheduledDateTypeFilter')) {
        return true;
      }
      if (this.filtersPayload.ScheduledType !== this.getFormValue('ScheduledType')) {
        return true;
      }
      return false;
    }
  }

  filterFunction(isLoadmore: boolean = false) {
    debugger
    const dontCallApi = this.check()
    let filters = {
      ...this.filtersPayload,
      ...this.filtersForm.value,
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      ...(this.currentPath === '/invoice' ||
        this.getFormValue('FirstLevelFilter') === 6
        ? {
          shouldShowBookedDetails: this.currentPath === '/invoice',
        }
        : {}),
      ...(this.permissions.has('Permissions.Invoice.ViewBrokerageInfo') &&
        this.currentPath === '/invoice'
        ? {
          shouldShowBrokerageInfo: true,
        }
        : {}),
      ...(!this.isCustomStatusEnabled &&
        ![3, 4].includes(this.getFormValue('SecondLevelFilter'))
        ? {
          ScheduledDateTypeFilter: null,
          ScheduledType: null,
        }
        : {}),
      ...(this.isMobileView && this.showWebUI && !isLoadmore
        ? { pageNumber: 1 }
        : {}),
      ...(this.searchTerm && this.getFormValue('PropertyToSearch')
        ? { PropertyToSearch: this.getFormValue('PropertyToSearch') }
        : {}),
      dontCallApi,
    };

    if (filters.hasOwnProperty('CustomFilterIds')) {
      delete filters.CustomFilterIds;
    }

    if (this.isCustomStatusEnabled) {
      filters = {
        ...filters,
        CustomFilterId:
          this.getFormValue('CustomFourthLevelFilter') ??
          this.getFormValue('CustomThirdLevelFilter') ??
          this.getFormValue('CustomSecondLevelFilter') ??
          this.getFormValue('CustomFirstLevelFilter'),
      };
    }
    if (filters?.CustomFirstLevelFilter == EMPTY_GUID) {
      delete filters.CustomFirstLevelFilter;
      delete filters.CustomFilterId;
    }

    this.currentPath === '/invoice'
      ? this._store.dispatch(new UpdateInvoiceFilter(filters))
      : this._store.dispatch(new UpdateFilterPayload(filters));

  }

  statusCellRenderer(params: any) {
    const status = params.data.status?.displayName || '';
    const childStatus = params.data.status?.childType?.displayName || '';
    let isNegStatus: boolean;
    isNegStatus = [
      'Overdue',
      'Not Interested',
      'Dropped',
      'Booking Cancel',
      'Pending',
    ].includes(status);
    const statusClass = this.isCustomStatusEnabled
      ? 'text-black'
      : isNegStatus
        ? 'text-danger'
        : 'text-accent-green';

    return `
      <p class="text-truncate fw-600 ${statusClass}">${status === 'Site Visit Scheduled' && this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : status}</p>
      <p class="text-truncate mt-4">${childStatus}</p>
    `;
  }

  onCellClicked(event: CellClickedEvent) {
    const headerName = event.colDef.headerName;
    if (event.data && event.data.rootId &&
      this.parentLeadDataMap.get(event.data.rootId) &&
      this.parentLeadDataMap.get(event.data.rootId).isDeleted) {
      this.isParentLeadDeleted = true;
      this.modalRef = this.modalService.show(this.parentLeadDeleted, {
        class: 'modal-350 top-modal ip-modal-unset',
        initialState: {
          isPermissionError: false
        }
      });
      return;
    }
    if (headerName !== 'Actions') {
      this.currentSelectionForLeadPreview = 'Overview';
    }
    let initialStateStatus: any;
    let leadPreviewRef: any;
    switch (headerName) {
      case 'Matching Projects':
      case 'Matching Properties':
      case 'Actions':
        break;
      case 'Status':
        if (!this.permissions.has('Permissions.Leads.UpdateLeadStatus')) {
          this.isParentLeadDeleted = false;
          this.modalRef = this.modalService.show(this.parentLeadDeleted, {
            class: 'modal-350 top-modal ip-modal-unset',
            initialState: {
              isPermissionError: true
            }
          });
          return;
        }
        initialStateStatus = {
          data: event.data,
          showCommunicationCount: this.showCommunicationCount,
          selectedSection:
            event?.data?.assignTo == EMPTY_GUID
              ? 'Overview'
              : this.currentPath === '/invoice'
                ? this.permissions.has('Permissions.Leads.UpdateLeadStatus') &&
                  !event?.data?.isArchived
                  ? 'Status'
                  : this.currentSelectionForLeadPreview
                : this.permissions.has('Permissions.Leads.UpdateLeadStatus') &&
                  !event?.data?.isArchived &&
                  event?.data?.status?.status !== 'invoiced'
                  ? 'Status'
                  : this.currentSelectionForLeadPreview,
          cardData: this.isMobileView ? this.cardData : this.rowData,
          closeLeadPreviewModal: () => {
            leadPreviewRef.hide();
          },
        };
        leadPreviewRef = this.modalService.show(
          LeadPreviewComponent,
          Object.assign(
            {},
            {
              class: 'right-modal modal-550 ip-modal-unset',
              initialState: initialStateStatus,
            }
          )
        );
        break;
      case 'Parent Lead':
        if (event.value && (event.value[0] === '--' || event.value === '--')) {
          return;
        }
        if (event.value && (event.value[0] === 'Deleted Lead' || event.value === 'Deleted Lead')) {
          this.isParentLeadDeleted = true;
          this.modalRef = this.modalService.show(this.parentLeadDeleted, {
            class: 'modal-350 top-modal ip-modal-unset',
            initialState: {
              isPermissionError: false
            }
          });
          return;
        }
        if (!this.canViewAllLeads) {
          this.isParentLeadDeleted = false;
          this.modalRef = this.modalService.show(this.parentLeadDeleted, {
            class: 'modal-350 top-modal ip-modal-unset',
            initialState: {
              isPermissionError: true
            }
          });
          return;
        }
        if (event.data.rootId && this.parentLeadDataMap.get(event.data.rootId)) {
          this._store.dispatch(new FetchLeadByIdWithArchive(event.data.rootId));
          this._store.dispatch(new FetchLeadByIdWithArchiveSuccess({}));
          this._store.select(getLeadByIdWithArchiveData).pipe(
            takeUntil(this.stopper),
            filter((data: any) => !isEmptyObject(data)),
            take(1)
          ).subscribe((parentLeadData: any) => {
            if (parentLeadData) {
              initialStateStatus = {
                data: parentLeadData,
                isShowParentLeadColumn: true,
              };
              leadPreviewRef = this.modalService.show(
                LeadPreviewComponent,
                Object.assign(
                  {},
                  {
                    class: 'right-modal modal-550 ip-modal-unset',
                    initialState: initialStateStatus,
                  }
                )
              );
            }
          });
        } else {
          initialStateStatus = {
            data: event.data,
            isShowParentLeadColumn: true,
          };
          leadPreviewRef = this.modalService.show(
            LeadPreviewComponent,
            Object.assign(
              {},
              {
                class: 'right-modal modal-550 ip-modal-unset',
                initialState: initialStateStatus,
              }
            )
          );
        }
        break;
      default:
        if (!this.permissions.has('Permissions.Leads.View')) {
          this.isParentLeadDeleted = false;
          this.modalRef = this.modalService.show(this.parentLeadDeleted, {
            class: 'modal-350 top-modal ip-modal-unset',
            initialState: {
              isPermissionError: true
            }
          });
          return;
        }

        initialStateStatus = {
          data: event.data,
          selectedSection: this.currentSelectionForLeadPreview,
          cardData: this.isMobileView ? this.cardData : this.rowData,
          closeLeadPreviewModal: () => {
            leadPreviewRef.hide();
          },
        };
        leadPreviewRef = this.modalService.show(
          LeadPreviewComponent,
          Object.assign(
            {},
            { class: 'right-modal modal-550 ip-modal-unset', initialState: initialStateStatus }
          )
        );
        break;
    }
  }

  onSortChanged(event: any) {
    if (event.source === 'api') return;
    const sortedColumn = event.columnApi
      .getAllColumns()
      .filter(
        (column: any) =>
          column?.hasOwnProperty('sort') && column?.sort !== undefined
      )?.[0];
    this.filtersPayload = {
      ...this.filtersPayload,
      'SortingCriteria.ColumnName': sortedColumn?.colId,
      'SortingCriteria.IsAscending':
        sortedColumn?.sort == 'asc'
          ? true
          : sortedColumn?.sort == 'desc'
            ? false
            : null,
    };
    this.filtersForm.patchValue({
      pageNumber: 1,
    });
    this.filterFunction();
  }

  commonGridSettings(
    headerName: string,
    hide: boolean,
    sortable: boolean = false
  ) {
    return {
      headerName: headerName,
      hide: hide,
      field: headerName,
      cellClass: 'cursor-pointer',
      comparator: (
        valueA: any,
        valueB: any,
        nodeA: any,
        nodeB: any,
        isInverted: any
      ) => {
        if (isInverted) {
          return nodeB.rowIndex - nodeA.rowIndex;
        }
        return nodeA.rowIndex - nodeB.rowIndex;
      },
      colId: headerName.replace(/\s+/g, ''),
      sortable: sortable,
      unSortIcon: sortable,
    };
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions = {
      ...this.gridOptions,
      getRowId: (params) => params.data.id,
    };
    this.gridOptions.rowHeight = 60;
    const bulkInvoicePermissions = [
      'Permissions.Invoice.BulkUpdateStatus',
      'Permissions.Invoice.BulkReassign',
      'Permissions.Invoice.BulkSecondaryReassign',
      'Permissions.Invoice.BulkUpdateSource',
      'Permissions.Invoice.BulkProjectAssignment',
      'Permissions.Invoice.BulkWhatsApp',
      'Permissions.Invoice.BulkEmail',
      'Permissions.Invoice.BulkDelete',
      'Permissions.Invoice.BulkRestore',
    ];

    const bulkLeadsPermissions = [
      'Permissions.Leads.BulkUpdateStatus',
      'Permissions.Leads.BulkReassign',
      'Permissions.Leads.BulkSecondaryReassign',
      'Permissions.Leads.BulkProjectAssignment',
      'Permissions.Leads.BulkUpdateSource',
      'Permissions.Leads.BulkDelete',
      'Permissions.Leads.BulkRestore',
      'Permissions.Leads.BulkWhatsApp',
      'Permissions.Leads.BulkEmail',
    ];

    const relevantPermissions =
      this.currentPath === '/invoice'
        ? bulkInvoicePermissions
        : bulkLeadsPermissions;
    const bulkPermissions = relevantPermissions.some((permission) =>
      this.permissions.has(permission)
    );

    this.gridOptions.columnDefs = [
      {
        ...this.commonGridSettings('Lead Name', false, true),
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned cursor-pointer',
        // minWidth: 310,
        wrapText: true,
        autoHeight: true,
        suppressMovable: true,
        lockPosition: 'left',
        valueGetter: (params: any) => [params.data.name],
        cellRenderer: LeadNameSectionComponent,
        colId: 'Name',
      },
      ...(this.currentPath !== '/invoice'
        ? [
          {
            ...this.commonGridSettings('Parent Lead', !this.showParentLeadColumn),
            valueGetter: (params: any) => {
              const parentLead = this.parentLeadDataMap?.get(params.data.rootId);
              return [
                params.data.rootId
                  ? parentLead?.name || 'Deleted Lead'
                  : '--'
              ];
            },
            cellRenderer: (params: any) => {
              const isDeletedLead = params.value[0] === 'Deleted Lead';
              return `<p class="text-truncate-1 break-all ${isDeletedLead ? 'text-danger' : ''}">${params.value[0]}</p>`;
            },
          },
        ]
        : []),
      {
        ...this.commonGridSettings('Assigned To', false),
        minWidth: 195,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.assignTo, this.allUsers, true) ||
          '--',
          getAssignedToDetails(
            params.data.secondaryUserId,
            this.allUsers,
            true
          ) || '--',
        ],
        colId: 'AssignTo',
        cellRenderer: (params: any) => {
          if (this.globalSettingsData?.isDualOwnershipEnabled) {
            return `<div class="d-flex">
              <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">P</div>
              <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'
              }</p>
            </div>
            <div class="d-flex mt-6">
              <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
              <p class="text-truncate-1 break-all">${params.value[1] ? params.value[1] : '--'
              }</p>
            </div>`;
          } else {
            return `<div class="d-flex">
              <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
              }</p>
            </div>`;
          }
        },
      },
      {
        ...this.commonGridSettings('Assigned From', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.assignedFrom, this.allUsers, true) ||
          '--',
          getAssignedToDetails(
            params.data.secondaryFromUserId,
            this.allUsers,
            true
          ) || '--',
        ],
        colId: 'AssignedFrom',
        cellRenderer: (params: any) => {
          if (this.globalSettingsData?.isDualOwnershipEnabled) {
            return `<div class="d-flex">
              <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">P</div>
              <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'
              }</p>
            </div>
            <div class="d-flex mt-6">
              <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
              <p class="text-truncate-1 break-all">${params.value[1] ? params.value[1] : '--'
              }</p>
            </div>`;
          } else {
            return `<div class="d-flex">
              <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
              }</p>
            </div>`;
          }
        },
      },
      {
        ...this.commonGridSettings('Original Owner', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.originalOwner, this.allUsers, true) ||
          '--',
        ],
        colId: 'OriginalOwner',
        cellRenderer: (params: any) => {
          return `<div class="d-flex">
            <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'}</p>
          </div>`;
        },
      },
      {
        ...this.commonGridSettings('Status', false, true),
        minWidth: 170,
        cellRenderer: this.statusCellRenderer.bind(this),
        colId: 'CustomLeadStatus',
        valueGetter: (params: any) => {
          return [
            params?.data?.status?.displayName,
            params?.data?.status?.actionName,
            params?.data?.status?.childType?.displayName,
            params?.data?.status?.childType?.actionName,
          ];
        },
      },
      {
        ...this.commonGridSettings('Scheduled Date', true, true),
        minWidth: 170,
        valueGetter: (params: any) => [
          params.data.scheduledDate
            ? getTimeZoneDate(
              params.data.scheduledDate,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Referral Details', true, true),
        valueGetter: (params: any) => [
          params.data.referralName,
          params.data.referralContactNo,
          params.data.referralEmail,
        ],
        colId: 'ReferralName',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-sm">${params.value[0] ? params.value[0] : ''
            }</p>
            <p class="text-truncate-1 break-all text-sm">${params.value[1] ? params.value[1] : ''
            }</p>
            <p class="text-truncate-1 break-all text-sm">${params.value[2] ? params.value[2] : ''
            }</p>
            `;
        },
      },
      {
        ...this.commonGridSettings('Landline Number', true, true),
        valueGetter: (params: any) => [params.data.landLine],
        colId: 'LandLine',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Enquired Location', true, true),
        valueGetter: (params: any) =>
          params?.data?.enquiry?.addresses
            ? params?.data?.enquiry?.addresses
              ?.map((address: any) => getLocationDetailsByObj(address))
              .join('; ')
            : '',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all" title='${params?.value
            }'>${params.value || ''}</p>`;
        },
        colId: 'Address',
      },
      {
        ...this.commonGridSettings('Enquired City', true, true),
        valueGetter: (params: any) => {
          const cityArray = params.data?.enquiry?.addresses
            ?.map((address: any) => address.city)
            .filter((city: string) => city) || [];
          return [cityArray.join(', ')];
        },
        cellRenderer: (params: any) => {
          const fullCity = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCity}">${fullCity}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Enquired State', true, true),
        valueGetter: (params: any) => {
          const stateArray = params.data?.enquiry?.addresses
            ?.map((address: any) => address.state)
            .filter((state: string) => state) || [];
          return [stateArray.join(', ')];
        },
        cellRenderer: (params: any) => {
          const fullState = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullState}">${fullState}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Enquired Country', true, true),
        valueGetter: (params: any) => {
          const countryArray = params.data?.enquiry?.addresses
            ?.map((address: any) => address.country)
            .filter((country: string) => country) || [];
          return [countryArray.join(', ')];
        },
        cellRenderer: (params: any) => {
          const fullCountry = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCountry}">${fullCountry}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Enquired Zone', true, true),
        valueGetter: (params: any) => [params.data?.enquiry?.zone],
        colId: 'EnquiryZone',
        cellRenderer: (params: any) => {
          return `<p>${params.value ? params.value : ''}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Min. Budget', true, true),
        minWidth: 200,
        colId: 'LowerBudget',
        valueGetter: (params: any) => [
          params.data.enquiry?.lowerBudget || '',
          params.data.enquiry?.lowerBudget
            ? params.data?.enquiry?.currency || this.defaultCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          const [budget, currency] = params.value;
          const formattedBudget = formatBudget(budget, currency);
          const budgetString = budget
            ? `${params.value[1]}${params.value[0]} (${formattedBudget})`
            : '';
          return `<p>${budgetString}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Date of Birth', true, true),
        minWidth: 200,
        valueGetter: (params: any) => [
          params.data.dateOfBirth || ''
        ],
        colId: 'DateOfBirth',
        cellRenderer: (params: any) => {
          const dateOfBirth = params.value[0];
          const formattedDateOfBirth = dateOfBirth ? this.getTimeZoneDate(dateOfBirth, '00:00:00', 'dayMonthYear') : '';
          return `<p>${formattedDateOfBirth}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Gender', true, true),
        minWidth: 200,
        valueGetter: (params: any) => [
          params.data.gender || ''
        ],
        colId: 'Gender',
        cellRenderer: (params: any) => {
          const gender = params.value[0];
          const formattedGender = this.Gender[gender] ?? '';
          return `<p>${formattedGender}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Marital Status', true, true),
        minWidth: 200,
        valueGetter: (params: any) => [
          params.data.maritalStatus || ''
        ],
        colId: 'MaritalStatus',
        cellRenderer: (params: any) => {
          const maritalStatus = params.value[0];
          const formattedMaritalStatus = this.MaritalStatusType[maritalStatus] ?? '';
          return `<p>${formattedMaritalStatus}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Max. Budget', true, true),
        minWidth: 200,
        valueGetter: (params: any) => [
          params.data.enquiry?.upperBudget || '',
          params.data.enquiry?.upperBudget
            ? params.data?.enquiry?.currency || this.defaultCurrency
            : '',
        ],
        colId: 'UpperBudget',
        cellRenderer: (params: any) => {
          const [budget, currency] = params.value;
          const formattedBudget = formatBudget(budget, currency);
          const budgetString = budget
            ? `${params.value[1]}${params.value[0]} (${formattedBudget})`
            : '';
          return `<p>${budgetString}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Carpet Area', true, true),
        valueGetter: (params: any) => [
          params.data?.enquiry?.carpetArea,
          params.data?.enquiry?.carpetAreaUnit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Built Up Area', true, true),
        valueGetter: (params: any) => [
          params.data?.enquiry?.builtUpArea,
          params.data?.enquiry?.builtUpAreaUnit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Saleable Area', true, true),
        valueGetter: (params: any) => [
          params.data?.enquiry?.saleableArea,
          params.data?.enquiry?.saleableAreaUnit,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Enquired For', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params.data.enquiry?.enquiryTypes?.map((enquiry: any) => {
            return EnquiryType[enquiry] == 'None' ? '' : EnquiryType[enquiry];
          }) || '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Property Details', true),
        valueGetter: (params: any) => {
          const displayName =
            params.data?.enquiry?.propertyTypes?.[0]?.displayName || '';
          const childType =
            params.data?.enquiry?.propertyTypes?.map((item: any) => item?.childType?.displayName) || '';
          return [displayName, childType];
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}${params.value[0] && params.value[1] ? ',' : ''
            } ${params.value[1]} </p>`;
        },
      },
      ...(this.globalSettingsData?.isCustomLeadFormEnabled === false ? [{
        ...this.commonGridSettings('BHK', true, false),
        minWidth: 180,
        valueGetter: (params: any) => [
          params?.data?.enquiry?.bhKs
            ?.map((bhk: any) => getBHKDisplayString(bhk))
            ?.join(', '),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${Array.isArray(params.value) &&
            params.value[0] &&
            params.value[0]?.length > 0
            ? params.value[0]
            : '--'
            } </p>`;
        },
      }] : []),
      {
        ...this.commonGridSettings('Project(s)', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params.data.projects.map((project: any) => project.name),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
            }</p>`;
        },
        colId: 'Projects',
      },
      {
        ...this.commonGridSettings('Property(s)', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params.data.properties.map((property: any) => property.title),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
            }</p>`;
        },
        colId: 'Properties',
      },

      {
        ...this.commonGridSettings('Facebook Page ID', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.additionalProperties?.PageId,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Facebook Ad', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.additionalProperties?.AdId,
          params?.data?.additionalProperties?.AdName,
        ],
        cellRenderer: (params: any) => {
          const [adId, adName] = params.value || '--';

          const renderField = (label: string, value: string | undefined) =>
            value
              ? `<p class="text-truncate-1 break-all">${label}: ${value}</p>`
              : '-';

          return `
            <div class="facebook-ad-container">
              ${renderField('Ad ID', adId)}
              ${renderField('Ad Name', adName)}
            </div>
          `;
        },
      },
      {
        ...this.commonGridSettings('Facebook Ad Set', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.additionalProperties?.AdSetId,
          params?.data?.additionalProperties?.AdSetName,
        ],
        cellRenderer: (params: any) => {
          const [AdSetId, adSetName] = params.value || '--';

          const renderField = (label: string, value: string | undefined) =>
            value
              ? `<p class="text-truncate-1 break-all">${label}: ${value}</p>`
              : '-';

          return `
            <div class="facebook-ad-container">
              ${renderField('Ad Set ID', AdSetId)}
              ${renderField('Ad Set Name', adSetName)}
            </div>
          `;
        },
      },
      {
        ...this.commonGridSettings('Facebook Ad Account', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.additionalProperties?.AdAccountId,
          params?.data?.additionalProperties?.AdAccountName,
        ],
        cellRenderer: (params: any) => {
          const [adAccountId, adAccountName] = params.value || '--';

          const renderField = (label: string, value: string | undefined) =>
            value
              ? `<p class="text-truncate-1 break-all">${label}: ${value}</p>`
              : '-';

          return `
            <div class="container">
              ${renderField('Account ID', adAccountId)}
              ${renderField('Account Name', adAccountName)}
            </div>
          `;
        },
      },
      {
        ...this.commonGridSettings('Facebook Campaign', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.additionalProperties?.CampaignId,
          params?.data?.additionalProperties?.CampaignName,
        ],
        cellRenderer: (params: any) => {
          const [compaignId, compaignName] = params.value || '--';

          const renderField = (label: string, value: string | undefined) =>
            value
              ? `<p class="text-truncate-1 break-all">${label}:${value}</p>`
              : '-';

          return `
            <div class="facebook-ad-container">
              ${renderField('Compaign ID', compaignId)}
              ${renderField('Compaign Name', compaignName)}
            </div>
          `;
        },
      },
      {
        ...this.commonGridSettings('Facebook Form', true),
        minWidth: 150,
        valueGetter: (params: any) => [
          params?.data?.additionalProperties?.Name,
          params?.data?.additionalProperties?.FacebookId,
        ],
        cellRenderer: (params: any) => {
          const [Name, FacebookId] = params.value || '--';

          const renderField = (label: string, value: string | undefined) =>
            value
              ? `<p class="text-truncate-1 break-all">${label}:${value}</p>`
              : '-';

          return `
            <div class="facebook-ad-container">
              ${renderField('Name', Name)}
              ${renderField('Facebook Id', FacebookId)}
            </div>
          `;
        },
      },
      {
        ...this.commonGridSettings('Agency Name(s)', true, true),
        valueGetter: (params: any) => [
          params.data?.agencies?.map((agency: any) => agency?.name),
        ],
        colId: 'AgencyName',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Campaign Name', true, true),
        valueGetter: (params: any) => [
          params.data?.campaigns?.map((campaigns: any) => campaigns?.name),
        ],
        colId: 'CampaignName',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Company Name', true, true),
        minWidth: 150,
        valueGetter: (params: any) => [params.data.companyName],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">
            ${params.value[0] ? params.value[0] : ''}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Notes', true, true),
        minWidth: 150,
        valueGetter: (params: any) => [params.data.notes],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">
            ${params.value ? params.value : ''}</p>`;
        },
      },

      {
        ...this.commonGridSettings('Serial Number', true, true),
        filter: false,
        minWidth: 125,
        valueGetter: (params: any) => [params.data.serialNumber],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">
            ${params.value[0] ? params.value[0] : ''}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Possession Needed By', true, true),
        colId: 'PossessionDate',
        valueGetter: (params: any) => [
          params.data.enquiry?.possessionDate
            ? getTimeZoneDate(
              params.data.enquiry?.possessionDate,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Possession Type', true, true),
        colId: 'PossessionType',
        valueGetter: (params: any) => [
          params.data?.possesionType
            ? PossessionType[params.data?.possesionType]
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Picked Date', true, true),
        minWidth: 160,
        valueGetter: (params: any) => [
          params.data.pickedDate
            ? getTimeZoneDate(
              params.data.pickedDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]} </p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Deleted Date', true, true),
        colId: 'ArchivedOn',
        valueGetter: (params: any) => [
          params.data.archivedOn
            ? getTimeZoneDate(
              params.data.archivedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Created', true, true),
        colId: 'CreatedOn',
        minWidth: 200,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.allUsers, true) ||
          '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Modified', true, true),
        colId: 'LastModifiedOn',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.allUsers,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
          <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Booked', true, true),
        colId: 'BookedDate',
        minWidth: 200,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.bookedBy, this.allUsers, true) || '',
          params.data?.bookedDate
            ? 'At ' +
            getTimeZoneDate(
              params.data?.bookedDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
        <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        ...this.commonGridSettings('Channel Partner Name(s)', true, true),
        minWidth: 195,
        valueGetter: (params: any) => [
          params.data.channelPartners?.map(
            (channelPartner: any) => channelPartner.firmName
          ),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value[0] ? params.value[0] : ''}</p>`;
        },
        colId: 'ChannelPartnerName',
      },
      {
        ...this.commonGridSettings('Sourcing Manager', true),
        minWidth: 195,
        valueGetter: (params: any) =>
          getAssignedToDetails(
            params.data.sourcingManager,
            this.allUsers,
            true
          ) || '',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value}</p>`;
        },
        colId: 'SourcingManager',
      },
      {
        ...this.commonGridSettings('Closing Manager', true),
        minWidth: 195,
        valueGetter: (params: any) =>
          getAssignedToDetails(
            params.data.closingManager,
            this.allUsers,
            true
          ) || '',
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value}</p>`;
        },
        colId: 'ClosingManager',
      },

      {
        ...this.commonGridSettings('Profession', true),
        minWidth: 100,
        valueGetter: (params: any) => [params.data.profession],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">
        ${params.value[0] ? Profession[params.value[0]] : ''}</p>`;
        },
      },
      {
        ...this.commonGridSettings('Customer Address', true, true),
        minWidth: 195,
        valueGetter: (params: any) => [
          getLocationDetailsByObj(params.data?.address),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate">${params.value}</p>`;
        },
      },
    ];
    if (bulkPermissions) {
      this.gridOptions.columnDefs = [
        {
          showRowGroup: true,
          cellRenderer: 'agGroupCellRenderer',
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          filter: false,
          pinned: window.innerWidth > 768 ? 'left' : null,
          lockPinned: true,
          cellClass: 'lock-pinned',
          maxWidth: 50,
          suppressMovable: true,
          lockPosition: 'left',
        },
        ...this.gridOptions.columnDefs,
      ];
    }
    const isInvoicedFilter =
      this.filtersPayload?.FirstLevelFilter === 6 ||
      (this.isCustomStatusEnabled &&
        this.customTopLevelFilters?.some(
          (filter: any) =>
            filter.filterName === 'Invoiced' &&
            filter.id === this.getFormValue('CustomFirstLevelFilter')
        ));

    if (this.currentPath !== '/invoice' && isInvoicedFilter) {
      this.gridOptions.columnDefs = this.gridOptions?.columnDefs?.filter(
        (col): col is ColDef<any> => !(col as ColDef<any>).checkboxSelection
      );
    }

    if (parseInt(this.filtersPayload?.FirstLevelFilter) !== 4) {
      const matchingPropertiesColumn = {
        ...this.commonGridSettings('Matching Properties', true),
        filter: false,
        minWidth: 150,
        valueGetter: (params: any) => 'MatchingProperties',
        cellRenderer: MatchingPropertiesComponent,
      };

      const matchingProjectsColumn = {
        ...this.commonGridSettings('Matching Projects', true),
        filter: false,
        minWidth: 150,
        valueGetter: (params: any) => 'MatchingProjects',
        cellRenderer: MatchingPropertiesComponent,
      };

      this.gridOptions.columnDefs = [
        ...this.gridOptions?.columnDefs,
        matchingPropertiesColumn,
        matchingProjectsColumn,
      ];
    }

    const createColumn = (
      headerName: any,
      field: any,
      valueGetter: any,
      additionalProps = {}
    ) => ({
      headerName,
      field,
      hide: true,
      minWidth: 150,
      valueGetter,
      cellRenderer: (params: any) =>
        `<p class="text-truncate">${params.value[0] || ''}</p>`,
      cellClass: 'cursor-pointer',
      ...additionalProps,
    });

    const createBookingFormColumn = (
      headerName: any,
      field: any,
      property: any
    ) =>
      createColumn(headerName, field, (params: any) => {
        const bookedDetails = params.data?.bookedDetails;
        if (!bookedDetails || bookedDetails.length === 0) {
          return [];
        }
        const value = bookedDetails[0]?.[property];
        const discountUnit = bookedDetails[0]?.discountUnit;
        const currency = bookedDetails[0]?.currency;
        if (headerName === 'Discount' && value) {
          return [
            discountUnit === '%'
              ? `<div class="flex-center gap-1"><span>${value}</span><span>${'%'}</span></div>`
              : `<div class="flex-center gap-1"><span>${currency}</span><span>${value}</span></div>`,
          ];
        } else if (headerName === 'Book Under Name') {
          return [value];
        }
        return [
          value &&
          `<div class="flex-center gap-1"><span>${currency}</span><span>${value}</span></div>`,
        ];
      });

    const createBrokerageColumn = (headerName: any, field: any) =>
      createColumn(headerName, field, (params: any) => {
        const bookedDetails = params.data?.bookedDetails;
        if (
          !bookedDetails ||
          bookedDetails.length === 0 ||
          !bookedDetails[0]?.brokerageInfo
        ) {
          return '';
        }
        const value = bookedDetails[0]?.brokerageInfo[field];
        const commissionUnit = bookedDetails[0]?.brokerageInfo.commissionUnit;
        const currency = bookedDetails[0]?.currency;
        if (headerName === 'Referral Commission') {
          if (commissionUnit === '%') {
            return [
              `<div class="flex-center">${value} <div>${'%'}</div></div>`,
            ];
          } else {
            return [
              `<div class="flex-center"><div>${currency}</div> ${value}</div>`,
            ];
          }
        } else if (headerName === 'Brokerage Charges' || headerName === 'GST') {
          const brokerageUnit = bookedDetails[0]?.brokerageInfo.brokerageUnit;
          if (headerName === 'Brokerage Charges') {
            if (brokerageUnit === '%') {
              return [
                `<div class="flex-center ml-10">${value} <div>${'%'}</div></div>`,
              ];
            } else {
              return [
                `<div class="flex-center ml-10"><div>${currency}</div> ${value}</div>`,
              ];
            }
          } else if (value) {
            return [
              `<div class="flex-center ml-10">${value} <div>${'%'}</div></div>`,
            ];
          }
        } else if (['Referral Name', 'Referral Number'].includes(headerName)) {
          return [value && value];
        }
        return [
          value &&
          `<div class="d-flex gap-1"><span>${currency}</span><span>${value}</span></div>`,
        ];
      });

    let bookingFormColumns = [
      createBookingFormColumn(
        'Book Under Name',
        'Book Under Name',
        'bookedUnderName'
      ),
      createBookingFormColumn(
        'Agreement Value',
        'Agreement Value',
        'agreementValue'
      ),
      createBookingFormColumn(
        'Car Parking Charges',
        'Car Parking Charges',
        'carParkingCharges'
      ),
      createBookingFormColumn(
        'Add-On Charges',
        'Add-On Charges',
        'additionalCharges'
      ),
      createBookingFormColumn(
        'Token Amount Paid',
        'Token Amount Paid',
        'tokenAmount'
      ),
      createBookingFormColumn('Discount', 'Discount', 'discount'),
      createBookingFormColumn(
        'Balance Amount',
        'Balance Amount',
        'remainingAmount'
      ),
      {
        headerName: 'Payment Mode',
        field: 'Payment Mode',
        hide: true,
        minWidth: 150,
        ...createColumn('Payment Mode', 'Payment Mode', (params: any) => [
          params.data?.bookedDetails[0]?.paymentMode
            ? BookingPaymentMode[params.data?.bookedDetails[0]?.paymentMode]
            : '',
        ]),
      },
      {
        headerName: 'Type Of Payment Selected',
        field: 'Type Of Payment Selected',
        hide: true,
        minWidth: 150,
        ...createColumn(
          'Type Of Payment Selected',
          'Type Of Payment Selected',
          (params: any) => [
            PAYMENT_TYPE[params.data?.bookedDetails[0]?.paymentType],
          ]
        ),
      },
    ];

    if (this.permissions.has('Permissions.Invoice.ViewBrokerageInfo')) {
      bookingFormColumns = [
        ...bookingFormColumns,
        createBrokerageColumn('Brokerage Charges', 'brokerageCharges'),
        createBrokerageColumn('Net Brokerage Amount', 'netBrokerageAmount'),
        createBrokerageColumn('GST', 'gst'),
        createBrokerageColumn('Total Brokerage', 'totalBrokerage'),
        createBrokerageColumn('Referral Name', 'referralName'),
        createBrokerageColumn('Referral Number', 'referralNumber'),
        createBrokerageColumn('Referral Commission', 'commission'),
        createBrokerageColumn('Brokerage Earned', 'earnedBrokerage'),
        createBrokerageColumn('Total Sold Price', 'soldPrice'),
      ];
    }

    if (this.currentPath === '/invoice') {
      this.gridOptions.columnDefs = [
        ...this.gridOptions.columnDefs,
        ...bookingFormColumns,
      ];
    }

    if (this.permissions.has('Permissions.Leads.ViewLeadSource')) {
      this.gridOptions.columnDefs = [
        ...this.gridOptions.columnDefs,
        {
          ...this.commonGridSettings('Source', false),
          valueGetter: (params: any) => [
            LeadSource[params.data.enquiry?.leadSource],
            params.data.enquiry?.subSource,
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate fw-600">
              ${params.value[0] ? params.value[0] : ''}</p>
              <p class="text-truncate mt-4">
              ${params.value[1] ? params.value[1] : ''}</p>`;
          },
          colId: 'LeadSource',
        },
      ];
    }
    if (!this.globalSettingsData?.isMaskedLeadContactNo) {
      this.gridOptions.columnDefs = [
        ...this.gridOptions.columnDefs,
        {
          ...this.commonGridSettings('Primary/Alt No', true, true),
          colId: 'ContactNo',
          valueGetter: (params: any) => [
            params.data.contactNo,
            params.data?.alternateContactNo,
          ],
          cellRenderer: (params: any) => {
            return `<p class="gap-2 align-center">
              ${params?.value[0]
                ? `<span class="icon ic-call-ring ic-dark ic-xxs"></span> ${params?.value[0]}`
                : ''
              }</p>
              <p class="gap-2 align-center">${params?.value[1]
                ? `<span class="icon ic-Call ic-dark ic-xxs"></span> ${params?.value[1]}`
                : ''
              }</p>`;
          },
          width: 200,
        },
        {
          ...this.commonGridSettings('Email', true, true),
          valueGetter: (params: any) => [params.data.email],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Purpose', true, true),
          valueGetter: (params: any) => [params.data?.enquiry?.purpose ? PurposeType[params.data?.enquiry?.purpose] : '--'],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : '--'}</p>`;
          },
        },
      ];
    }
    if (!this.globalSettingsData?.isCustomLeadFormEnabled) {
      this.gridOptions.columnDefs = [
        ...this.gridOptions.columnDefs,
        {
          ...this.commonGridSettings('BHK Types', true),
          minWidth: 180,
          valueGetter: (params: any) => [
            params.data.enquiry?.bhkTypes
              ?.map((type: any) => BHKType[type])
              ?.join(', ') || '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
          },
        },
      ];
    }
    if (this.globalSettingsData?.isCustomLeadFormEnabled) {
      this.gridOptions.columnDefs = [
        ...this.gridOptions.columnDefs,
        {
          ...this.commonGridSettings('Enquired Sub-Community', true),
          valueGetter: (params: any) => [
            params.data?.enquiry?.addresses
              ?.map((address: any) => address.subCommunity)
              .join(', '),
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Enquired Community', true),
          valueGetter: (params: any) => [
            params.data?.enquiry?.addresses
              ?.map((address: any) => address.community)
              .join(', '),
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Enquired Tower Name', true),
          valueGetter: (params: any) => [
            params.data?.enquiry?.addresses
              ?.map((address: any) => address.towerName)
              .join(', '),
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value ? params.value : ''
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Furnish Status', true),
          valueGetter: (params: any) => [
            params.data.enquiry?.furnished
              ? FurnishStatus[params.data.enquiry?.furnished]
              : '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
          },
        },
        {
          ...this.commonGridSettings('Preferred Floors', true),
          valueGetter: (params: any) => [params.data.enquiry?.floors],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${Array.isArray(params.value) &&
              params.value[0] &&
              params.value[0].length > 0
              ? params.value[0]
              : '--'
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Beds', true),
          valueGetter: (params: any) => params.data.enquiry?.beds || [],
          cellRenderer: (params: any) => {
            const value = Array.isArray(params.value)
              ? params.value.map((bed: any) =>
                bed === 0 || bed === '0' ? 'Studio' : bed
              )
              : [];

            return `<p class="text-truncate-1 break-all">${value.length > 0 ? value.join(', ') : '--'
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Baths', true),
          valueGetter: (params: any) => [params.data.enquiry?.baths],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${Array.isArray(params.value) &&
              params.value[0] &&
              params.value[0].length > 0
              ? params.value[0]
              : '--'
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Offering Type', true),
          valueGetter: (params: any) => [
            params.data.enquiry?.offerType
              ? OfferType[params.data.enquiry?.offerType]
              : '--',
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
          },
        },
        {
          ...this.commonGridSettings('Property Area', true, true),
          valueGetter: (params: any) => [
            params.data?.enquiry?.propertyArea,
            params.data?.enquiry?.propertyAreaUnit,
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Net Area', true, true),
          valueGetter: (params: any) => [
            params.data?.enquiry?.netArea,
            params.data?.enquiry?.netAreaUnit,
          ],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''} ${params.value[1] ? params.value[1] : ''
              }</p>`;
          },
        },
        {
          ...this.commonGridSettings('Unit Number/Name', true, true),
          valueGetter: (params: any) => [params.data?.enquiry?.unitName],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : ''}</p>`;
          },
        },
        {
          ...this.commonGridSettings('Cluster Name', true, true),
          valueGetter: (params: any) => [params.data?.enquiry?.clusterName || '--'],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
            ${params.value[0] ? params.value[0] : '--'}</p>`;
          },
        },
        {
          ...this.commonGridSettings('Nationality', true, true),
          valueGetter: (params: any) => [params.data?.nationality || '--'],
          cellRenderer: (params: any) => {
            return `<p class="text-normal">
          ${params.value[0] ? params.value[0] : ''}</p>`;
          },
        },
      ];
    }
    this.gridOptions.columnDefs = [
      ...this.gridOptions.columnDefs,
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 290,
        maxWidth: 290,
        menuTabs: [],
        filter: false,
        suppressMovable: true,
        lockPosition: 'right',
        cellRenderer: LeadsActionsComponent,
        cellRendererParams: () => ({
          showCommunicationCount: this.showCommunicationCount
        })
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  loadMore() {
    this.filtersForm.patchValue({
      pageNumber: this.filtersPayload?.pageNumber + 1,
    });
    this._store.dispatch(new UpdateIsLoadMore(true));
    this.filterFunction(true);
  }

  onInView(isVisible: boolean) {
    this._store.select(getBaseFilterCountIsLoading).pipe(
      skipWhile((isLoading: boolean) => {
        return isLoading;
      }),
      take(1)
    );

    if (
      isVisible &&
      !this.isLeadDataLoading &&
      !this.baseFilterCountIsLoading &&
      this.cardData.length < this.leadsTotalCount
    ) {
      this.loadMore();
    }
  }
  getBHKNo(data: any) {
    return this.globalSettingsData?.isCustomLeadFormEnabled
      ? data
      : data?.map((bhk: any) => getBHKDisplayString(bhk))?.join(', ');
  }
  customizeHeader() {
    const columnDefs = this.gridApi?.getColumnDefs();
    const columnToModify: any = columnDefs.find(
      (column: any) =>
        column.colId === this.filtersPayload?.['SortingCriteria.ColumnName']
    );
    if (columnToModify) {
      columnToModify.sort = this.sortOrder;
      this.gridApi.setColumnDefs(columnDefs);
    }
  }

  onColumnsSelected(columns: any[] = this.defaultColumns) {
    let parentLead = columns.find((col: any) => col?.label === 'Parent Lead') ? true : false;
    const wasParentLeadVisible = this.isParentLeadColumnVisible;
    this.isParentLeadColumnVisible = parentLead;
    if (!wasParentLeadVisible && parentLead && this.rowData?.length) {
      this.fetchAllParentLeadData(this.rowData);
    }

    const colData = columns.map((column: any) => column.label).toString();
    const storageKey = location.href.includes('invoice')
      ? 'invoice-columns'
      : 'manage-lead-columns';
    localStorage.setItem(storageKey, colData);

    const cols = columns.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);

    const nonSelectedCols = this.columns.filter(
      (col: any) => !cols.includes(col.value)
    );
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col: any) => col.value),
      false
    );

    const columnState = this.gridColumnApi.getColumnState();
    columnState?.slice(0, 2).forEach((col: any) => (col.pinned = 'left'));

    localStorage.setItem(
      'myColumnState',
      JSON.stringify(
        columnState.map((column: any) => ({ ...column, sort: null }))
      )
    );
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
    this.gridColumnApi?.setColumnVisible('ParentLead', this.showParentLeadColumn || parentLead);
  }


  onGridReady(params: any) {
    if (this.columnMovedListener) {
      this.gridApi.removeEventListener('columnMoved', this.columnMovedListener);
    }
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.columns = this.gridColumnApi.getColumns().map((column: any) => ({
      label: column.getColDef().headerName,
      value: column,
    }));

    if (parseInt(this.filtersPayload?.FirstLevelFilter) === 4) {
      this.columns = this.columns.filter(
        (column) =>
          column.label !== 'Matching Properties' &&
          column.label !== 'Matching Projects'
      );
    }
    const sliceStartIndex = this.columns?.[0]?.label === undefined ? 2 : 1;

    this.columns = this.columns
      .slice(sliceStartIndex, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns.filter(
      (col) => !col.value.getColDef().hide
    );

    const columnState = JSON.parse(localStorage.getItem('myColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    if (this.onColumnMoved) {
      this.gridOptions = {
        ...this.gridOptions,
        onColumnMoved: this.onColumnMoved,
      };
    }

    const columnData = (
      !location.href.includes('invoice')
        ? localStorage.getItem('manage-lead-columns')
        : localStorage.getItem('invoice-columns')
    )?.split(',');

    if (columnData?.length) {
      this.defaultColumns = this.columns.filter((col) =>
        columnData.includes(col.label)
      );
      this.onColumnsSelected(this.defaultColumns);
    } else {
      this.isParentLeadColumnVisible = this.defaultColumns.some((col: any) => col?.label === 'Parent Lead');
    }
    this.columnMovedListener = () => {
      const updatedColumns = this.gridColumnApi.getAllDisplayedColumns().map((column: any) => ({
        label: column.getColDef().headerName,
        value: column,
      }));

      this.onColumnsSelected(updatedColumns);
    };

    this.gridApi.addEventListener('columnMoved', this.columnMovedListener);
    this.customizeHeader();
    this.changeDetectorRef.detectChanges();
  }

  onColumnMoved(params: any) {
    var columnState = JSON.stringify(
      params?.columnApi?.getColumnState()?.map((column: any) => ({
        ...column,
        sort: null,
      }))
    );
    localStorage.setItem('myColumnState', columnState);
  }

  onSetColumnDefault() {
    this.showParentLeadColumn = false
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
    this.trackingService.trackFeature(`Web.Leads.Button.DefaultClick.Click`);
  }

  onPageSizeChange(event: any) {
    this.filtersForm.patchValue({
      pageSize: event,
      pageNumber: 1,
    });
    this.trackingService.trackFeature(`Web.Leads.Options.${event}.Click`);
    this.filterFunction();
  }

  extractStatusCounts(data: any[]): { [key: string]: number } {
    const result: { [key: string]: number } = {};
    data?.forEach((item) => {
      result[item.statusCount.statusId] = item.statusCount.count;
      item.subStatusCount.forEach((subStatus: any) => {
        result[subStatus.statusId] = subStatus.count;
      });
    });

    return result;
  }

  onSecondLevelFilterClick(filter: any) {
    if (this.getFormValue('SecondLevelFilter') !== filter) {
      this.filtersForm.patchValue({
        SecondLevelFilter: filter,
        ScheduledDateTypeFilter: filter === 3 ? 1 : null,
        ScheduledType: null,
        SecondLevelFilterId: null,
        pageNumber: 1,
      });

      this.filterFunction();
    }
  }

  filterTag(tag: any) {
    const customFlags = this.filtersForm.value.CustomFlags ?? [];
    const updatedFlags = customFlags.includes(tag)
      ? customFlags.filter((flag: any) => flag !== tag)
      : [...customFlags, tag];
    this.filtersForm.patchValue({
      CustomFlags: updatedFlags,
      pageNumber: 1,
    });
    this.trackingService.trackFeature(
      'Web.Leads.Options.TagsBasedShortcuts.Click'
    );
    this.filterFunction();
  }

  getTagsCounts(tags: any) {
    return this.flagsCount?.find((tag: any) => tag.name === tags)?.count ?? 0;
  }

  reportsNavigation() {
    const data = this.gridOptionsService?.data;
    if (
      !(
        data?.userId ||
        data?.projectId ||
        data?.id ||
        data?.source?.toString()?.length ||
        data?.agencyName ||
        data?.subSource ||
        data?.projectTitle ||
        data?.city
      )
    ) {
      return;
    }
    const excludedStatuses = [
      'Active Leads',
      'All Leads',
      'Dropped',
      'Booked',
      'Booking Cancel',
      'Invoiced',
      'Not Interested',
    ];
    const status = excludedStatuses.includes(this.gridOptionsService.status)
      ? this.gridOptionsService.status
      : 'Active Leads';
    this.filtersPayload = Object.fromEntries(
      Object.entries({
        ...this.filtersPayload,
        assignTo:
          data?.userId || this.gridOptionsService?.payload?.UserIds || data?.id,
        ownerSelection: this.gridOptionsService.payload.ownerSelection,
        fromDate: setTimeZoneDate(
          this.gridOptionsService?.payload?.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        toDate: setTimeZoneDate(
          this.gridOptionsService?.payload?.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        pageNumber: 1,
        SearchByNameOrNumber: null,
        MeetingOrVisitStatuses: this.gridOptionsService.meetingStatus,
        FirstLevelFilter: this.visibility?.find(
          (item: any) => item.displayName === status
        )?.value,
        SecondLevelFilter:
          this.SecondLevelFilterEnum[
          this.gridOptionsService.status as keyof typeof SecondLevelFilter
          ] ?? null,
        FromDateForMeetingOrVisit: setTimeZoneDate(
          this.gridOptionsService.payload.fromDateForMeetingOrVisit,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        ToDateForMeetingOrVisit: setTimeZoneDate(
          this.gridOptionsService.payload.toDateForMeetingOrVisit,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        AgencyNames:
          this.gridOptionsService?.payload?.AgencyNames || data?.agencyName,
        CampaignNames:
          this.gridOptionsService?.payload?.CampaignNames ||
          data?.CampaignNames,
        SubSources:
          this.gridOptionsService.payload.SubSources || data.subSource
            ? Array.isArray(
              this.gridOptionsService.payload.SubSources || data.subSource
            )
              ? this.gridOptionsService.payload.SubSources || data.subSource
              : [this.gridOptionsService.payload.SubSources || data.subSource]
            : null,
        IsWithTeam: this.gridOptionsService?.payload?.IsWithTeam,
        designationsId: this.gridOptionsService?.payload?.Designation,
        Projects:
          this.gridOptionsService?.payload?.Projects || data?.projectTitle,
        StatusIds: this.getStatusId(this.gridOptionsService.status)
          ? [this.getStatusId(this.gridOptionsService.status)]
          : null,
        SubStatusIds: this.getSubStatusId(this.gridOptionsService.subStatus)
          ? [this.getSubStatusId(this.gridOptionsService.subStatus)]
          : null,
        leadVisibility: 0,
        dateType:
          LeadDateType[
          (this.gridOptionsService.dateType ||
            'All') as keyof typeof LeadDateType
          ],
        Locations: this.gridOptionsService.payload.Locations,
        Cities: this.gridOptionsService?.payload?.Cities,
        States: this.gridOptionsService?.payload?.States,
        Countries: this.gridOptionsService?.payload?.Countries,
        Source:
          this.gridOptionsService?.payload?.Sources || data?.source
            ? Array.isArray(
              this.gridOptionsService?.payload?.Sources || data?.source
            )
              ? this.gridOptionsService?.payload?.Sources || data?.source
              : [
                LeadSource[
                this.gridOptionsService?.payload?.Sources || data?.source
                ] ?? null,
              ]
            : null,
        CallDirections: this.gridOptionsService?.payload?.callDirection ? [this.gridOptionsService?.payload?.callDirection] : null,
        CallStatuses: this.gridOptionsService?.payload?.callStatus ? [this.gridOptionsService?.payload?.callStatus] : null,
        CallLogFromDate: this.gridOptionsService?.payload?.callLogFromDate ? setTimeZoneDate(this.gridOptionsService?.payload?.callLogFromDate, this.userData?.timeZoneInfo?.baseUTcOffset) : null,
        CallLogToDate: this.gridOptionsService?.payload?.callLogToDate ? setTimeZoneDate(this.gridOptionsService?.payload?.callLogToDate, this.userData?.timeZoneInfo?.baseUTcOffset) : null,
        CallDateType: this.gridOptionsService?.payload?.CallDateType ? this.gridOptionsService?.payload?.CallDateType : null,
      }).filter(([_, value]) => value !== undefined)
    );

    if (this.gridOptionsService?.payload?.Sources === 0 || data?.source === 0) {
      this.filtersPayload = {
        ...this.filtersPayload,
        Source: LeadSource[0] ?? null,
      }
    }

    this.filtersPayload = {
      ...this.filtersPayload,
      ...(data?.userId && (this.filtersPayload.CallDirections || this.filtersPayload.CallStatuses) ? {
        UserIds: [data?.userId],
        assignTo: null,
      } : {}),
    }
    if (this.gridOptionsService?.payload?.Sources === 0 || data?.source === 0) {
      this.filtersPayload = {
        ...this.filtersPayload,
        Source: LeadSource[0] ?? null,
      }
    }

    if (this.gridOptionsService.dateType) {
      this.dateType = this.gridOptionsService.dateType || 'All';
    }

    this.filtersForm.patchValue({
      ...this.filtersPayload,
    });

    this.filterFunction();
    this.gridOptionsService.clearAllFilters();
  }

  getSubStatusId(name: string): any {
    const statusList = this.isCustomStatusEnabled
      ? this.customStatusList
      : this.masterLeadStatus;
    for (const status of statusList) {
      const child = status.childTypes?.find(
        (child: any) => child?.displayName === name
      );
      if (child) {
        return child.id;
      }
    }
    return null;
  }

  getStatusId(name: string): any {
    const statusList = this.isCustomStatusEnabled
      ? this.customStatusList
      : this.masterLeadStatus;
    return statusList?.find((status: any) => status?.displayName === name)?.id;
  }

  dashboardNavigation(params: any) {
    const data = Object.keys(params).reduce((parsedObj: any, key) => {
      const value = params[key];
      try {
        parsedObj[key] = JSON.parse(value);
      } catch {
        parsedObj[key] = value;
      }
      return parsedObj;
    }, {});
    this.filtersPayload = Object.fromEntries(
      Object.entries({
        ...this.filtersForm.value,
        baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        pageNumber: 1,
        SearchByNameOrNumber: null,
        leadVisibility: data?.leadVisibility
          ? this.visibilityList?.findIndex(
            (list: any) => list.name === data?.leadVisibility
          )
          : 0,
        ...(!this.isCustomStatusEnabled
          ? {
            FirstLevelFilter: data?.firstLevelFilter
              ? this.visibility?.findIndex(
                (list: any) => list.displayName === data?.firstLevelFilter
              )
              : 1,
            SecondLevelFilter: data?.secondLevelFilter
              ? this.SecondLevelFilterEnum[data.secondLevelFilter]
              : data?.StatusName
                ? this.SecondLevelFilterEnum[data.StatusName]
                : null,
            ScheduledDateTypeFilter: data?.scheduledDateTypeFilter
              ? this.dateFilters?.findIndex(
                (filter: string) => filter === data?.scheduledDateTypeFilter
              ) ?? 1
              : null,
            ScheduledType:
              this.LeadScheduledTypeEnum[data?.scheduledType] ?? null,
          }
          : {
            CustomFirstLevelFilter:
              (data?.customFirstLevelFilter
                ? data?.customFirstLevelFilter?.includes('Active')
                  ? this.customTopLevelFilters?.find(
                    (filter: any) => filter?.name === 'Active Leads'
                  )?.id
                  : this.customTopLevelFilters[0]?.id
                : this.filtersPayload?.CustomFirstLevelFilter) ??
              this.filtersForm?.value?.CustomFirstLevelFilter,
            CustomSecondLevelFilter: data?.StatusName ? this.customSecondLevelFilterList?.find(
              (filter: any) => filter?.name === data?.StatusName
            )?.id : data?.secondLevelFilter ? this.customSecondLevelFilterList?.find(
              (filter: any) => filter?.name === data?.secondLevelFilter
            )?.id : null,
            CustomThirdLevelFilter: null,
            CustomFourthLevelFilter: null,
          }),
        StatusIds: this.getStatusId(data?.StatusName)
          ? [this.getStatusId(data?.StatusName)]
          : null,
        assignTo:
          (data?.assignTo?.length ? data?.assignTo : null) ??
          (data?.userId ? [data?.userId] : []),
        ...(data.date
          ? {
            fromDate: setTimeZoneDate(
              data?.date[0],
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            toDate: setTimeZoneDate(
              data?.date[1],
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          }
          : {}),
        dateType: LeadDateType[data?.dtype] ?? 0,
        IsWithTeam: data?.withTeam,
        Source: Array.isArray(data?.source || data?.Source)
          ? (data?.source || data?.Source)
          : (data?.source || data?.Source)
            ? [(data?.source || data?.Source)]
            : [],
        MeetingOrVisitStatuses: data?.doneStatus ? [data?.doneStatus]?.flat() : null,
        IsOnlyReportees: data?.IsOnlyReportees,
        Projects: data?.projects ?? data?.Projects,
        designationsId: data?.designation,
        ...(data?.isNavigatedFromMarketing
          ? data?.ChannelPartnerNames
            ? { ChannelPartnerNames: data.ChannelPartnerNames }
            : data?.AgencyNames
              ? { AgencyNames: data.AgencyNames }
              : data?.CampaignNames
                ? { CampaignNames: data.CampaignNames }
                : {}
          : {}),
        ...(data?.Properties ? { Properties: data?.Properties } : {}),
      }).filter(([_, value]) => value !== undefined)
    );
    this.filtersForm.patchValue({
      ...this.filtersPayload,
    });
    if (this.filtersPayload?.CustomSecondLevelFilter) {
      this.onCustomSecondLevelFilterChange(this.filtersPayload?.CustomSecondLevelFilter);
    }
    this.filterFunction();
  }

  isArray(value: any) {
    return Array.isArray(value);
  }

  getFilterValue(key: string, value: any) {

    if (key === 'NoOfBHKs' || key === 'BR') {
      return !this.globalSettingsData?.isCustomLeadFormEnabled
        ? getBHKDisplayString(value)
        : getBRDisplayString(value);
    }
    if (key === 'genderTypes') {
      return this.Gender[value];
    }
    if (key === 'maritalStatuses') {
      return this.MaritalStatusType[value];
    }
    if (key === 'dateOfBirth') {
      return this.getTimeZoneDate(value, '00:00:00', 'dayMonthYear');
    }

    if (
      [
        'assignTo',
        'SecondaryUsers',
        'AssignedFromIds',
        'HistoryAssignedToIds',
        'SecondaryFromIds',
        'DoneBy',
        'createdByIds',
        'bookedByIds',
        'lastModifiedByIds',
        'archivedByIds',
        'restoredByIds',
        'AppointmentDoneByUserIds',
        'sourcingManagers',
        'closingManagers',
        'QualifiedByIds',
        'OriginalOwner',
        'UserIds',
      ].includes(key)
    ) {
      return this.getUserName(value);
    }

    if (key === 'Beds') {
      if (value === 0) {
        return 'Studio';
      } else {
        return value;
      }
    }

    if (key === 'PropertyType') {
      return this.getPropertyTypeName(value);
    }

    if (key === 'PropertySubType') {
      return this.getPropertySubTypeName(value);
    }
    if (key === 'FromDateForMeetingOrVisit') {
      const formattedToDate = getTimeZoneDate(
        this.filtersPayload.FromDateForMeetingOrVisit,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      );
      const formattedFromDate = getTimeZoneDate(
        this.filtersPayload.ToDateForMeetingOrVisit,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      );
      const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
      return [dateRangeString];
    }

    if (key === 'StatusIds') {
      return this.getStatusName(value);
    }

    if (key === 'PaymentMode') {
      return BookingPaymentMode[value];
    }

    if (key === 'PaymentType') {
      return PaymentType[value];
    }

    if (key === 'Profession') {
      return Profession[value];
    }

    if (key === 'ownerSelection') {
      return OwnerSelectionType[value];
    }

    if (key === 'SubStatusIds') {
      return this.getSubStatusName(value);
    }

    if (key === 'PossesionType') {
      const possessionDateFilterList = POSSESSION_DATE_FILTER_LIST;
      const possessionType = Object.values(PossessionType)[value];
      const possessionOption = possessionDateFilterList.find(option => option.value === possessionType);
      return possessionOption ? possessionOption.displayName : value;
    }

    if (key === 'FromPossesionDate' || key === 'ToPossesionDate') {
      return getTimeZoneDate(
        value,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      );
    }

    if (
      [
        'MinCarpetArea',
        'MinBuiltUpArea',
        'MaxBuiltUpArea',
        'MaxSaleableArea',
        'MinSaleableArea',
        'MaxCarpetArea',
        'MinPropertyArea',
        'MaxPropertyArea',
        'MinNetArea',
        'MaxNetArea',
      ].includes(key)
    ) {
      return this.getArea(value, this.filtersPayload?.[`${key}UnitId`]);
    }

    if (key === 'designationsId') {
      return this.getDesignation(value);
    }

    if (['FromMinBudget', 'ToMinBudget', 'FromMaxBudget', 'ToMaxBudget'].includes(key)) {
      return `${value} ${this.filtersPayload?.Currency}`;
    }

    if (['isUntouched', 'DataConverted'].includes(key)) {
      return value;
    }

    if (key === 'RadiusInKm') {
      return value + ' kms';
    }
    if (key === 'Sources') {
      return LeadSource[value];
    }
    if (key === 'MeetingOrVisitStatuses' && (value === 'Site Visit Done' || value === 'Site Visit Not Done') && this.globalSettingsData?.shouldRenameSiteVisitColumn) {
      if (value === 'Site Visit Done') {
        return 'Referral Taken';
      } else {
        return 'Referral Not Taken';
      }
    }

    if (key === 'LeadType') {
      const leadTypes: any = {
        0: 'Unique',
        1: 'Parent',
        2: 'Duplicate'
      };
      return leadTypes[value];
    }

    if (key === 'CallDirections') {
      return CallDirections[value];
    }

    if (key === 'CallStatuses') {
      return CallStatuses[value];
    }

    if (key === 'CallLogFromDate') {
      return `${getTimeZoneDate(
        value,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      )} - ${getTimeZoneDate(
        this.filtersPayload.CallLogToDate,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      )}`;
    }

    return value;
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.firstName} ${user.lastName}`;
    });
    return userName;
  }

  getPropertyTypeName(id: string) {
    let propertyType = '';
    this.propertyType?.forEach((type: any) => {
      if (type.id === id) propertyType = type.displayName;
    });
    return propertyType;
  }

  getPropertySubTypeName(id: string) {
    let propertySubType = '';
    let allPropertySubTypes = this.propertyType
      ?.map((type: any) => type?.childTypes)
      ?.flat();
    propertySubType = allPropertySubTypes?.find(
      (type: any) => type?.id === id
    )?.displayName;
    return propertySubType;
  }

  getStatusName(id: string): string {
    const statusList = this.isCustomStatusEnabled
      ? this.customStatusList
      : this.masterLeadStatus;
    const status = statusList?.find((type: any) => type.id === id)?.displayName;
    return status;
  }

  getSubStatusName(id: string) {
    const statusList = this.isCustomStatusEnabled
      ? this.customStatusList
      : this.masterLeadStatus;
    let subStatusList = statusList
      ?.map((status: any) => status?.childTypes)
      ?.flat();
    return subStatusList?.find((status: any) => status.id === id)?.displayName;
  }
  getArea(area: number, id: string) {
    return area + ' ' + (this.getAreaUnit(id) || '');
  }

  getAreaUnit(id: string) {
    let areaUnit = '';
    this.areaSizeUnits?.forEach((type: any) => {
      if (type.id === id) areaUnit = type.unit;
    });
    return areaUnit;
  }

  getDesignation(id: string) {
    let designation = '';
    this.designationList?.forEach((type: any) => {
      if (type.id === id) designation = type.name;
    });
    return designation;
  }

  onRemoveFilter(key: string, value: string, allValue: any) {
    if (key === 'BR') {
      key = 'NoOfBHKs';
    }

    if (['PossesionType', 'FromPossesionDate', 'ToPossesionDate'].includes(key)) {
      this.filtersPayload.PossesionType = null;
      this.filtersPayload.FromPossesionDate = null;
      this.filtersPayload.ToPossesionDate = null;
      this.filtersForm.patchValue({
        pageNumber: 1,
        PossesionType: null,
        FromPossesionDate: null,
        ToPossesionDate: null
      });
      this.filterFunction();
      return;
    }

    const isArray = Array.isArray(allValue);
    this.filtersPayload[key] = isArray
      ? allValue.filter((item: string) => item !== value)
      : null;
    if ((!this.filtersPayload.FromMinBudget || !this.filtersPayload.ToMinBudget) && (!this.filtersPayload.FromMaxBudget || !this.filtersPayload.ToMaxBudget)) {
      delete this.filtersPayload.Currency;
    }
    if (key === 'LeadType' && value == '1') {
      delete this.filtersPayload.ChildLeadsCount;
    }
    if (key === 'assignTo') {
      delete this.filtersPayload.ownerSelection;
    }
    if (
      [
        'FromMinBudget',
        'ToMinBudget',
        'FromMaxBudget',
        'ToMaxBudget',
      ].includes(key)
    ) {
      const modifiedKey = key.replace(/^(From|To)/, '');
      delete this.filtersPayload[`To${modifiedKey}`];
      delete this.filtersPayload[`From${modifiedKey}`];
    }
    if (
      [
        'MinCarpetArea',
        'MinBuiltUpArea',
        'MaxBuiltUpArea',
        'MaxSaleableArea',
        'MinSaleableArea',
        'MaxCarpetArea',
        'MinPropertyArea',
        'MaxPropertyArea',
        'MinNetArea',
        'MaxNetArea',
      ].includes(key)
    ) {
      const modifiedKey = key.replace(/^(Min|Max)/, '');
      delete this.filtersPayload[`Min${modifiedKey}`];
      delete this.filtersPayload[`Max${modifiedKey}`];
      delete this.filtersPayload[`${modifiedKey}UnitId`];
    }
    this.filtersForm.patchValue({
      ...this.filtersPayload,
      pageNumber: 1,
    });
    this.filterFunction();
  }

  onClearAllFilters() {
    this.selectedFilter = ''
    const preservedShowLeadCountData = this.filtersPayload?.showFilterCount;
    const preservedShowLeadCommunicationCount = this.filtersPayload?.showCommunicationCount;
    this.filtersPayload = Object.keys(this.filtersPayload).reduce(
      (acc: any, key: string) => {
        acc[key] =
          this.filtersForm.controls[key] && key !== 'CustomFlags'
            ? this.filtersPayload[key]
            : null;
        return acc;
      },
      {}
    );

    this.filtersPayload.showFilterCount = preservedShowLeadCountData;
    this.filtersPayload.showCommunicationCount = preservedShowLeadCommunicationCount;

    this.filtersForm.patchValue({
      ...this.filtersPayload,
      pageNumber: 1,
      showCommunicationCount: preservedShowLeadCommunicationCount,
      showFilterCount: preservedShowLeadCountData,
    });
    this.filtersPayload = {
      ...this.filtersPayload,
      dontCallApi: false,
    };
    this.filterFunction();
  }

  exportLeadReport() {
    this._store.dispatch(new FetchLeadExportSuccess(''));

    let payload = {
      ...this.filtersPayload,
      path: 'lead/new/all',
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
    };
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, v]) => v !== null && v !== undefined)
    );
    payload = this.convertFlatToNested(payload);

    let initialState: any = {
      payload,
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };

    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
    this.trackingService.trackFeature(`Web.Leads.Button.Export.Click`);
  }

  convertFlatToNested(obj: any): any {
    const result: any = {};

    for (const key in obj) {
      if (key.includes('.')) {
        const [outer, inner] = key.split('.');
        result[outer] = result[outer] || {};
        result[outer][inner] = obj[key];
      } else {
        result[key] = obj[key];
      }
    }

    return result;
  }

  openLeadPreviewModal(data: any, shouldOpenOverview: boolean = false) {
    const initialState: any = {
      data: data,
      cardData: this.cardData,
      selectedSection: shouldOpenOverview
        ? 'Overview'
        : this.currentSelectionForLeadPreview,
      showCommunicationCount: this.showCommunicationCount,
      closeLeadPreviewModal: () => {
        leadPreview.hide();
      },
    };
    var leadPreview = this.modalService.show(LeadPreviewComponent, {
      initialState: initialState,
      class: 'right-modal modal-550 ip-modal-unset',
    });
  }

  toggleUI() {
    this.showWebUI = !this.showWebUI;
    this._store.dispatch(
      new UpdateIsCardView(window.innerWidth <= 480 && this.showWebUI)
    );
    this.filtersForm.patchValue({
      pageNumber: 1,
    });
    this.filterFunction();
  }

  trackerFirstFilter(FirstLevelFilter: any) {
    this.trackingService.trackFeature(
      `Web.Leads.filter.${FirstLevelFilter?.displayName?.replace(
        /\s+/g,
        ''
      )}.Click`
    );
  }

  trackerSecFilter(SecLevelFilter: any) {
    this.trackingService.trackFeature(
      `Web.Leads.Page.${SecLevelFilter?.replace(/\s+/g, '')}.Click`
    );
  }


  fetchAllParentLeadData(data: any) {
    if (!Array.isArray(data)) {
      return;
    }
    const allRootIds = data
      .filter(item => item?.rootId)
      .map(item => item.rootId);
    if (allRootIds.length) {
      this._store.dispatch(new FetchAllParentLeadById(allRootIds));
    }
    this._store.select(getAllParentLeadData).pipe(takeUntil(this.stopper)).subscribe((parentLeadData: any) => {
      this.parentLeadDataMap.clear();
      parentLeadData.forEach((parent: any) => {
        this.parentLeadDataMap.set(parent.id, parent);
      });
      this.gridApi?.refreshCells({ force: true });
    });
  }

  openSavedFilter(event: any) {
    this.isSavedFilterOpen = !this.isSavedFilterOpen;
  }

  closeSavedFilter() {
    this.isSavedFilterOpen = false;
  }

  handleSelectFilter(updatedFilter: any) {
    let payload: any = {
      ...updatedFilter?.filterCriteria,
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
    }

    // Remove CustomFilterIds if present in the incoming data
    if (payload.hasOwnProperty('CustomFilterIds')) {
      delete payload.CustomFilterIds;
    }

    Object.entries(payload).forEach(([key, value]) => {
      if (value || value === 0) {
        if (Array.isArray(value)) {
          payload[key] = getIndexes(key, value);
        } else {
          payload[key] = value;
        }
      }
    });

    this.selectedFilter = JSON.stringify(updatedFilter?.filterCriteria);
    this.selectedFilterName = updatedFilter?.name
    this.selectedFilterId = updatedFilter?.id;
    this.FileForm.patchValue({
      name: updatedFilter?.name,
    });

    if (!this.isCustomStatusEnabled && payload.hasOwnProperty('FirstLevelFilter')) {
      if (typeof payload.FirstLevelFilter === 'string') {
        payload.FirstLevelFilter = parseInt(payload.FirstLevelFilter, 10);
      }

      if ([0, 1].includes(payload.FirstLevelFilter)) {
        payload.SecondLevelFilter = null;
      } else {
        const selectedStatus = this.visibility?.find(
          (item: any) => item.value === payload.FirstLevelFilter
        );
        const statusName = this.masterLeadStatus?.find(
          (status: any) => status.status === selectedStatus?.status
        )?.status;

        this.secondLevelFilterList =
          this.masterLeadStatus
            ?.find((status: any) => status.status === statusName)
            ?.childTypes?.map(({ displayName, id }: any) => ({
              displayName,
              id,
            })) || [];
      }
    }

    this.filtersForm.patchValue({
      ...payload,
      pageNumber: 1,
    });

    this.filtersPayload = {
      ...payload,
      pageNumber: 1,
    };

    this.filterFunction();
    this.closeSavedFilter();
  }
  handleEditFilter(updatedFilter: any) {
    this.selectedFilter = JSON.stringify(updatedFilter?.filterCriteria)
    this.selectedFilterName = updatedFilter?.name
    this.selectedFilterId = updatedFilter?.id;
    this.FileForm.patchValue({
      name: updatedFilter?.name,
    })

    this.openAdvFiltersModal(updatedFilter)
    this.closeSavedFilter()
  }

  onSaveFilter(addFile: TemplateRef<any>) {
    if (!this.isFilterUpdated) {
      this.FileForm.patchValue({
        name: null,
      });
      // Reset validation state when opening the popup
      // this.FileForm.get('name').markAsUntouched();
      // this.FileForm.get('name').setErrors(null);
    }
    let initialState: any = {
      class: 'modal-350 top-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(addFile, initialState);

    // Add event listener for when modal is hidden
    this.modalRef.onHidden.subscribe(() => {
      // Reset form validation state when modal is closed
      this.FileForm.get('name').markAsUntouched();
      this.FileForm.get('name').setErrors(null);
    });
  }

  onSubmit(): void {
    if (!this.FileForm.valid || this.doesFileNameExist) {
      validateAllFormFields(this.FileForm);
      return;
    }

    let enumPayload: any = {
      ...this.filtersPayload,
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
    }

    if (enumPayload.hasOwnProperty('CustomFilterIds')) {
      delete enumPayload.CustomFilterIds;
    }

    Object.entries(enumPayload).forEach(([key, value]) => {
      if (value || value === 0) {
        if (Array.isArray(value)) {
          enumPayload[key] = getIndexes(key, value);
        } else {
          enumPayload[key] = value;
        }
      }
    });
    const payload = {
      name: this.FileForm.value.name,
      module: 'leads',
      filterCriteria: JSON.stringify(enumPayload),
    };

    if (this.isFilterUpdated) {
      this.updateFilter(payload);
    } else {
      this.saveFilter(payload);
    }

    this.modalService.hide();
  }

  saveFilter(payload: any): void {
    this._store.dispatch(new SaveFilter(payload));
    this.selectedFilter = JSON.stringify(this.filtersPayload || {});
  }

  updateFilter(payload: any): void {
    const updatedPayload = { ...payload, id: this.selectedFilterId };
    this._store.dispatch(new UpdateSavedFilter(updatedPayload));
    this.selectedFilter = JSON.stringify(this.filtersPayload || {});
  }

  doesFileNameExists() {
    let name = this.FileForm.value.name;

    if (this.isFilterUpdated && this.selectedFilterName === name) {
      this.doesFileNameExist = false;
      return;
    }
    if (!name) {
      this.doesFileNameExist = false;
      return;
    }
    this._store.dispatch(new FilterExist(name, 'leads'));
    this._store.dispatch(new LoaderHide());
    this._store
      .select(getFilterExist)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.doesFileNameExist = data;
      });
  }


  @HostListener('document:click', ['$event'])
  handleOutsideClick(event: Event): void {
    const target = event.target as HTMLElement;
    const savedFilterDiv = document.getElementById('saved-filter');
    const searchFilterDiv = document.getElementById('search-dropdown');
    if (savedFilterDiv && savedFilterDiv.contains(target)) {
      return;
    }
    if (searchFilterDiv && searchFilterDiv.contains(target)) {
      return;
    }
    this.isSavedFilterOpen = false;
    this.showSearchDropdown = false;
  }

  onshowLeadCountData() {
    this.filtersPayload = { ...this.filtersPayload, showFilterCount: true };
    this.showFilterCount = true;
    this._store.dispatch(new UpdateFilter(this.filtersPayload));
    if (
      !this.isCustomStatusEnabled ||
      window.location.pathname.includes('/invoice')
    ) {
      this._store.dispatch(new FetchActiveCount());
      this._store.dispatch(new FetchLeadStatusCount());
    } else {
      this._store.dispatch(new FetchLeadCustomTopFilters());
    }

    this._store.dispatch(new FetchLeadBaseFilterCount());
  }

  onshowcommunicationCountData() {
    if (!this.rowData?.length) return;

    const leadIds = this.rowData.map((lead: any) => lead?.id).filter(Boolean);
    const batchSize = 50;

    for (let i = 0; i < leadIds.length; i += batchSize) {
      const payload = { LeadIds: leadIds.slice(i, i + batchSize) };
      this.filtersPayload = { ...this.filtersPayload, showCommunicationCount: true };
      this._store.dispatch(new UpdateFilter(this.filtersPayload));

      this._store.dispatch(
        new FetchLeadsCommunicationByIds({
          ...payload,
          showCommunicationCount: true
        })
      );

      this.showCommunicationCount = true;
      if (this.gridApi && this.gridApi) {
        this.gridApi.refreshCells({ columns: ['Actions'], force: true });
      }
    }
  }

  refreshData() {
    this.filterFunction();
  }

  ngOnDestroy() {
    if (this.router.url.includes('invoice')) {
      this._store.dispatch(new addDuplicate(false, null));
    }
    this._store.dispatch(new cancelAddLead(false));
    this.stopper.next();
    this.stopper.complete();
    if (this.gridApi) {
      // Dispose of the grid API reference
      this.gridApi.destroy();
      this.gridApi = null;
    }
  }
}


