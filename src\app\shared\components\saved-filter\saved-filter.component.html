<div [ngClass]="isMobileView ? 'top-40 nright-0' : (showFilters ? 'top-95 nright-10' : 'top-65 nright-10')"
    class="w-240 bg-white box-shadow-10 br-10 z-index-1021 position-absolute" id="dropdown-element"
    (click)="$event.stopPropagation()">
    <div class="flex-between bg-coal p-12 text-white">
        <h5>Saved Filter</h5>
        <div class="icon ic-close ic-white ic-sm cursor-pointer" (click)="onClose($event)"></div>
    </div>
    <div class="px-12 pb-8">
        <div class="bg-white align-center mt-10 px-20 no-validation py-6 br-20 border">
            <input placeholder="Type to search" class="border-0 outline-0 w-100 bg-white ml-4" [(ngModel)]="searchTerm"
                (input)="onSearch()" />
            <span class="justify-end search icon ic-search-solid ic-xxs ic-slate-90"> </span>
        </div>
        <ng-container *ngIf="filteredFilters?.length; else noFilter">
            <div [ngClass]="showFilters ? 'max-h-100-380' : 'max-h-100-332'"
                class="border br-6 mt-10 scrollbar max-h-100-332">
                <div class="hovered-card" *ngFor="let filter of filteredFilters">
                    <div class="flex-between hover-container cursor-pointer py-12 hover-bg w-100 border-bottom"
                        (click)="onSelect($event,filter)">
                        <h6 class="ml-12 my-4 text-sm">{{filter.name}}</h6>
                        <div [ngClass]="isMobileView  ? '' : 'hover-container'">
                            <div class="d-flex">
                                <div title="Edit" (click)="onEditFilter($event, filter)"
                                    class="ml-4 icon-badge icon-width">
                                    <span class="icon ic-dark ic-pen-solid m-auto ic-xxs" id="clkSavedFilterEdit"
                                        data-automate-id="clkSavedFilterEdit"></span>
                                </div>
                                <div class="border mt-2 h-16 mx-4"></div>
                                <div title="Delete" (click)="onDeleteFilter($event, filter)"
                                    class="ml-4 icon-badge icon-width">
                                    <span class="icon ic-dark ic-trash m-auto ic-xxs" id="clkSavedFilterDelete"
                                        data-automate-id="clkSavedFilterDelete"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>
</div>
<ng-template #noFilter>
    <div class="flex-center min-h-240">
        <h6 class="text-center text-sm">No saved filters found</h6>
    </div>
</ng-template>