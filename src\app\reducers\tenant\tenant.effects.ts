import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { TenantService } from 'src/app/services/controllers/tenant.service';
import { FetchTenantAPIKey, FetchTenantAPIKeySuccess, TenantActionTypes } from './tenant.actions';

@Injectable()
export class TenantEffects {
  getTenantAPIKey$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TenantActionTypes.FETCH_TENANT_API_KEY),
      switchMap((action: FetchTenantAPIKey) => {
        return this.api.getTenantAPIKey().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTenantAPIKeySuccess(resp.data);
            }
            return new FetchTenantAPIKeySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: TenantService,
  ) { }
}
