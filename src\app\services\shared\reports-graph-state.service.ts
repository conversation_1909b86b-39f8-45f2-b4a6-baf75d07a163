import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface GraphSelections {
  selectedParent?: any;
  selectedChildren: any[];
  selectedChartType: string;
  childColumns: any[];
  isInitialized: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ReportsGraphStateService {
  private graphSelections = new Map<string, GraphSelections>();

  private defaultSelections: GraphSelections = {
    selectedParent: null,
    selectedChildren: [],
    selectedChartType: 'line',
    childColumns: [],
    isInitialized: false
  };

  getSelections(reportType: string): GraphSelections {
    if (!this.graphSelections.has(reportType)) {
      return { ...this.defaultSelections };
    }
    return this.graphSelections.get(reportType)!;
  }

  saveSelections(reportType: string, selections: GraphSelections): void {
    this.graphSelections.set(reportType, { ...selections });
  }

  updateParentSelection(reportType: string, selectedParent: any, childColumns: any[]): void {
    const current = this.getSelections(reportType);
    current.selectedParent = selectedParent;
    current.childColumns = childColumns;
    this.saveSelections(reportType, current);
  }

  updateChildrenSelection(reportType: string, selectedChildren: any[]): void {
    const current = this.getSelections(reportType);
    current.selectedChildren = selectedChildren;
    this.saveSelections(reportType, current);
  }

  updateChartType(reportType: string, chartType: string): void {
    const current = this.getSelections(reportType);
    current.selectedChartType = chartType;
    this.saveSelections(reportType, current);
  }

  initializeSelections(reportType: string, parentColumns: any[], regularColumns: any[]): GraphSelections {
    const selections: GraphSelections = {
      selectedParent: null,
      selectedChildren: [],
      selectedChartType: 'line',
      childColumns: parentColumns.length > 0 ? [] : regularColumns,
      isInitialized: true
    };

    this.saveSelections(reportType, selections);
    return selections;
  }

  isInitialized(reportType: string): boolean {
    const selections = this.graphSelections.get(reportType);
    return selections?.isInitialized || false;
  }

  clearSelections(reportType: string): void {
    this.graphSelections.delete(reportType);
  }

  clearAllSelections(): void {
    this.graphSelections.clear();
  }
}
