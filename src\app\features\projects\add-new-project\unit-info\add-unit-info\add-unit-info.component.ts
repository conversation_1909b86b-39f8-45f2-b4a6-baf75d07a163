import {
  Attribute,
  Component,
  <PERSON>E<PERSON>ter,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import {
  BHK_NO_ALL,
  BHK_TYPE,
  FACING,
  FURNISH_STATUS,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import {
  BHKType,
  Facing,
  FurnishStatus,
  PropertyType,
  TaxationMode,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  getAWSImagePath,
  getBHKDisplayString,
  getProjectSubTypeDisplayName,
  onlyNumbers,
  toggleValidation,
  validateAll<PERSON><PERSON>Fields,
} from 'src/app/core/utils/common.util';
import { FetchAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import {
  AddUnitType,
  FetchProjectBasicDetailsById,
  UpdateProjectUnitInfo,
} from 'src/app/reducers/project/project.action';
import { getBasicDetailsById } from 'src/app/reducers/project/project.reducer';
import { AddWaterMark } from 'src/app/reducers/property/property.actions';
import { getWaterMarkImage } from 'src/app/reducers/property/property.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { environment } from 'src/environments/environment';
import { FetchCustomForm } from 'src/app/reducers/custom-form/custom-form.action';
import { getCustomForm } from 'src/app/reducers/custom-form/custom-form.reducer';

@Component({
  selector: 'add-unit-info',
  templateUrl: './add-unit-info.component.html',
})
export class AddUnitInfoComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  unitInfoDetails: FormGroup;
  onlyNumbers = onlyNumbers;
  fileFormat: any = 'Supported format: JPG, PNG, MP4, GIF and PDF';
  fileMessage: any = true;
  hasAllRequiredDataValid: boolean = false;
  bhkNoList: Array<string> = BHK_NO_ALL;
  areaUnit: Array<any>;
  selectEditUnitData: any;
  furnishingStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  selectedImage: any;
  allImagePath: any;
  galleryS3Paths: any = [];
  allUnitTypes: Array<any> = JSON.parse(
    localStorage.getItem('projectType') || '[]'
  );
  selectedDataId: any;
  facingList: Array<{ displayName: string; value: string }> = FACING;
  bhkTypes: Array<string> = BHK_TYPE;

  imgFiles: any = [];
  vidFile: any = [];
  syncingCurrency: boolean = false;
  unitSubTypes: any = [];
  currencyList: any[] = [];
  defaultCurrency: string;
  pricePerUnit: string = '';
  maintenanceCost: string = '';
  totalPrice: string = '';
  getBHKDisplayString = getBHKDisplayString;
  brochures: Array<any>;
  basicAttributes: Array<Attribute>;
  selectedAdditionalAttr: Array<string> = [];
  selectedAttributes: any;
  propType: any;
  fileSize: any;
  facingPayload: any = [];
  facingArray: any = [];
  pageNumber: any;
  pageSize: any;
  basicDetailCurrency: any;
  projectType: any;
  projectSubType: any;
  projectId: any;
  isBasicdetailsLoading: boolean = true;
  filteredAttributes: any;
  attributesLoading: boolean;
  allAttributes: any[] = [];
  projectTypeList: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');

  // Custom form properties
  unitBlockData: any;
  customFormFields: any[] = [];
  currentProjectType: string;
  currentProjectSubType: string;

  taxationModeList = [
    { label: 'GST(incl)', value: TaxationMode.GSTInclusive },
    {
      label: 'GST(excl)',
      value: TaxationMode.GSTExclusive,
    },
  ];
  waterMarkSettingsObj: any = {
    isWaterMarkEnabled: false,
    toAddWaterMark: false,
    watermarkLogo: '',
    watermarkImages: '',
    watermarkOpacity: null,
    watermarkPosition: null,
    watermarkSize: null,
  };

  s3BucketUrl = environment.s3ImageBucketURL;
  globalSettingsDetails: any;
  projectSubTypeData: string;
  getProjectSubTypeDisplayName = getProjectSubTypeDisplayName
  constructor(
    private _notificationService: NotificationsService,
    private modalService: BsModalService,
    private imgService: BlobStorageService,
    private router: Router,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private sharedDataService: ShareDataService
  ) { }

  ngOnInit() {
    this.projectId = this.sharedDataService.getProjectTitleId();

    this.unitInfoDetails = this.fb.group({
      unitName: ['', [Validators.required, Validators.minLength(1)]],
      unitArea: [null],
      unitAreaSize: [null],
      unitType: [null],
      unitSubType: [null],
      noOfBHK: [null],
      bhkType: [null],
      carpetArea: [null],
      carpetAreaUnit: [null],
      builtupArea: [null],
      builtupAreaUnit: [null],
      superBuiltupArea: [null],
      superBuiltupAreaUnit: [null],
      maintenanceCost: [null],
      pricePerUnit: [null],
      totalPrice: [null],
      currency: [this.defaultCurrency],
      facing: [null],
      furnishingStatus: [null],
      maximumOccupants: [0],
      isWaterMark: [false],
      numberOfKitchens: [null],
      numberOfUtilities: [null],
      numberOfBedrooms: [null],
      numberOfBathrooms: [null],
      numberOfBalconies: [null],
      numberOfLivingRooms: [null],
      taxationMode: [0],
    });
    this._store
      .select(getWaterMarkImage)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        let waterMarkUrl = data ? data : [];

        waterMarkUrl?.forEach((data: any, index: any) => {
          let s3bucket = this.s3BucketUrl;
          let startIndex = data.indexOf(s3bucket);
          if (startIndex !== -1) {
            let slicedString = data.slice(startIndex + s3bucket.length);
            this.galleryS3Paths.push({
              name: this.waterMarkSettingsObj.watermarkImages[index].name,
              imageFilePath: slicedString,
              isCoverImage: false,
              galleryType: 1,
            });
          }
        });
      });
    this.sharedDataService.projectSubType$.subscribe((subTypeId: any) => {
      this.projectSubTypeData = getProjectSubTypeDisplayName(subTypeId);
    });
    this._store.dispatch(new FetchProjectBasicDetailsById(this.projectId));
    this._store
      .select(getBasicDetailsById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.projectType = data[0]?.projectType?.displayName;
        this.projectSubType = data[0]?.projectType?.childType;
        if (!this.selectEditUnitData) {
          this.unitInfoDetails?.controls['unitType']?.setValue(
            this.projectType
          );
          this.unitInfoDetails?.controls['unitSubType']?.setValue(
            this.projectSubType
          );
          this.updateUnitSubTypes(this.projectType);
        }
      });

    this._store.dispatch(new FetchAllAttributes());
    this._store
      .select(getAllAttributes)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const activeAttribute = data
          .map((attr: any) => ({
            ...attr,
            selected: this.selectedAdditionalAttr.includes(attr?.id),
          }))
          .filter(
            (attr: any) =>
              attr.isActive &&
              attr?.attributeType === 'Additional' &&
              attr.basePropertyType.includes(
                PropertyType[
                this.unitInfoDetails.value.unitType || this.projectType
                ]
              )
          )
          .sort((a: any, b: any) =>
            a.attributeDisplayName.localeCompare(b.attributeDisplayName)
          );
        const allAttr = data
          .map((attr: any) => ({
            ...attr,
            selected: this.selectedAdditionalAttr.includes(attr?.id),
          }))
          .filter(
            (attr: any) =>
              attr.isActive &&
              attr.attributeName !== 'numberOfFloors' &&
              attr.attributeName !== 'floorNumber'
          );
        this.allAttributes = allAttr;
        this.filteredAttributes = activeAttribute;
        this.allAttributes
          ?.filter((item: any) => item.attributeType.toLowerCase() == 'basic')
          ?.forEach((item: any) => {
            this.unitInfoDetails.addControl(
              item?.attributeName,
              new FormControl(0)
            );
          });
        if (data) {
          this.addProjectAttributes();
        }
        this.emitAttributeSelection();
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        // this.hasInternationalSupport = data?.hasInternationalSupport;
        if (data) this.globalSettingsDetails = data;
        const currentValues = this.unitInfoDetails.value;
        this.unitInfoDetails.patchValue({
          unitAreaSize:
            currentValues.unitAreaSize || data?.defaultValues?.masterAreaUnit,
          carpetAreaUnit:
            currentValues.carpetAreaUnit || data?.defaultValues?.masterAreaUnit,
          builtupAreaUnit:
            currentValues.builtupAreaUnit ||
            data?.defaultValues?.masterAreaUnit,
          superBuiltupAreaUnit:
            currentValues.superBuiltupAreaUnit ||
            data?.defaultValues?.masterAreaUnit,
        });
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.unitInfoDetails?.patchValue({
          currency:
            this.selectEditUnitData?.currency ||
            this.basicDetailCurrency ||
            this.defaultCurrency ||
            'INR',
        });
        this.waterMarkSettingsObj.isWaterMarkEnabled =
          data?.leadProjectSetting?.isWaterMarksOnImagesEnabled;
        this.waterMarkSettingsObj.watermarkLogo = data
          ? data?.leadProjectSetting?.waterMarkUrl
          : null;
        this.waterMarkSettingsObj.watermarkOpacity =
          data?.leadProjectSetting?.opacity;
        this.waterMarkSettingsObj.watermarkPosition =
          data?.leadProjectSetting?.waterMarkPosition;
        this.waterMarkSettingsObj.watermarkSize =
          data?.leadProjectSetting?.imageSize;
        // if (!this.selectEditUnitData) {
        //   this.unitInfoDetails.patchValue({
        //     isWaterMark: data?.leadProjectSetting?.isWaterMarksOnImagesEnabled,
        //   });
        // }
      });

    this.unitInfoDetails.controls['unitType'].valueChanges
      .pipe(takeUntil(this.stopper))
      .subscribe((newUnitType) => {
        this.updateUnitSubTypes(newUnitType);
        this.filteredAttributes = this.allAttributes?.filter(
          (attr: any) =>
            attr.isActive &&
            attr?.attributeType === 'Additional' &&
            attr.basePropertyType.includes(PropertyType[newUnitType])
        );
      });

    this.unitInfoDetails
      .get('unitType')
      .valueChanges.subscribe((pType: string) => {
        this.propType = PropertyType[pType as keyof typeof PropertyType];
      });

    this.unitInfoDetails.get('currency').valueChanges.subscribe((value) => {
      if (!this.syncingCurrency) {
        this.syncingCurrency = true;
        this.unitInfoDetails.get('currency').setValue(value);
        this.syncingCurrency = false;
      }
    });

    this.unitInfoDetails.get('currency').valueChanges.subscribe((val) => {
      this.pricePerUnit = formatBudget(
        this.selectEditUnitData?.pricePerUnit ||
        this.unitInfoDetails.value.pricePerUnit,
        val
      );
      this.maintenanceCost = formatBudget(
        this.selectEditUnitData?.maintenanceCost ||
        this.unitInfoDetails.value.maintenanceCost,
        val
      );
      this.totalPrice = formatBudget(
        this.selectEditUnitData?.totalPrice ||
        this.unitInfoDetails.value.totalPrice,
        val
      );
    });

    this.unitInfoDetails.get('pricePerUnit').valueChanges.subscribe((val) => {
      this.pricePerUnit = formatBudget(
        val,
        this.unitInfoDetails.value.currency ||
        this.selectEditUnitData.currency ||
        this.defaultCurrency
      );
    });

    this.unitInfoDetails
      .get('maintenanceCost')
      .valueChanges.subscribe((val) => {
        this.maintenanceCost = formatBudget(
          val,
          this.unitInfoDetails.value.currency ||
          this.selectEditUnitData.currency ||
          this.defaultCurrency
        );
      });

    this.unitInfoDetails.get('totalPrice').valueChanges.subscribe((val) => {
      this.totalPrice = formatBudget(
        val,
        this.unitInfoDetails.value.currency ||
        this.selectEditUnitData.currency ||
        this.defaultCurrency
      );
    });

    this.unitInfoDetails.controls['pricePerUnit'].valueChanges.subscribe(
      (value) => {
        let totalPrice = this.unitInfoDetails.get('unitArea').value
          ? this.unitInfoDetails.get('unitArea').value
          : 1;
        this.unitInfoDetails.controls['totalPrice'].setValue(
          value * totalPrice
        );
        if (!value) {
          this.unitInfoDetails.controls['totalPrice'].setValue(null);
        }
      }
    );

    this.unitInfoDetails.controls['unitArea'].valueChanges.subscribe(
      (value) => {
        if (this.unitInfoDetails.get('pricePerUnit').value) {
          let totalPrice = this.unitInfoDetails.get('pricePerUnit').value;
          this.unitInfoDetails.controls['totalPrice'].setValue(
            value * totalPrice
          );
        }
        if (!value) {
          this.unitInfoDetails.controls['totalPrice'].setValue(
            this.unitInfoDetails.get('pricePerUnit').value
          );
        }

        if (value) {
          toggleValidation(
            VALIDATION_SET,
            this.unitInfoDetails,
            'unitAreaSize',
            [Validators.required]
          );
        } else {
          this.unitInfoDetails.controls['unitAreaSize'].setValue(null);
          toggleValidation(
            VALIDATION_CLEAR,
            this.unitInfoDetails,
            'unitAreaSize'
          );
        }
        this.unitInfoDetails.patchValue({
          unitAreaSize:
            this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    );

    this.unitInfoDetails.get('noOfBHK').valueChanges.subscribe((val) => {
      if (val) {
        this.unitInfoDetails.controls['numberOfKitchens'].setValue(1);
        this.unitInfoDetails.controls['numberOfBedrooms'].setValue(
          parseInt(val)
        );
      }
    });

    this.unitInfoDetails.get('carpetArea').valueChanges.subscribe((val) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.unitInfoDetails,
          'carpetAreaUnit',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.unitInfoDetails,
          'carpetAreaUnit'
        );
      }
    });

    this.unitInfoDetails.get('builtupArea').valueChanges.subscribe((val) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.unitInfoDetails,
          'builtupAreaUnit',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.unitInfoDetails,
          'builtupAreaUnit'
        );
      }
    });

    this.unitInfoDetails
      .get('superBuiltupArea')
      .valueChanges.subscribe((val) => {
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.unitInfoDetails,
            'superBuiltupAreaUnit',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.unitInfoDetails,
            'superBuiltupAreaUnit'
          );
        }
      });

    this.imgFiles = [];
    this.vidFile = [];
    this.brochures = [];
    this.galleryS3Paths = [];

    this.selectEditUnitData?.images?.map((item: any) => {
      this.galleryS3Paths?.push(item);
    });

    this.selectEditUnitData?.videos?.map((item: any) => {
      this.galleryS3Paths?.push(item);
    });

    this.selectEditUnitData?.unitInfoGalleries?.map((item: any) => {
      this.galleryS3Paths?.push(item);
    });

    const flatObject = this.allUnitTypes?.find((item: any) =>
      item.childTypes.some((child: any) => child.displayName === 'Flat')
    );
    const unitSubtype = flatObject?.childTypes.find(
      (child: any) => child.displayName === 'Flat'
    );

    this.unitSubTypes = [];
    this.allUnitTypes?.map((item: any) => {
      if (item?.displayName === 'Residential') {
        item.childTypes?.map((item: any) => {
          this.unitSubTypes.push(item);
        });
      }
    });
    this.onUnitChange('unitAreaSize');
    this.facingPayload = [];
    this.selectEditUnitData?.facings?.map((item: any) => {
      this.facingArray.push(FACING[item - 1]?.value);
      this.facingPayload.push(item);
    });

    if (this.selectEditUnitData) {
      this.unitInfoDetails.patchValue({
        unitName: this.selectEditUnitData.name,
        unitArea: this.selectEditUnitData.area,
        unitAreaSize:
          this.selectEditUnitData.areaUnitId ||
          this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        carpetArea: this.selectEditUnitData.carpetArea
          ? this.selectEditUnitData.carpetArea
          : null,
        carpetAreaUnit:
          this.selectEditUnitData.carpetAreaUnitId ||
          this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        unitType: this.selectEditUnitData?.unitType?.displayName,
        unitSubType: this.selectEditUnitData?.unitType?.childType,
        maintenanceCost: this.selectEditUnitData.maintenanceCost
          ? this.selectEditUnitData.maintenanceCost
          : null,
        pricePerUnit: this.selectEditUnitData.pricePerUnit
          ? this.selectEditUnitData.pricePerUnit
          : null,
        currency: this.selectEditUnitData?.currency || this.defaultCurrency,
        builtupArea: this.selectEditUnitData.buildUpArea
          ? this.selectEditUnitData.buildUpArea
          : null,
        builtupAreaUnit:
          this.selectEditUnitData.buildUpAreaId ||
          this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        furnishingStatus:
          FurnishStatus[this.selectEditUnitData.furnishingStatus],
        totalPrice: this.selectEditUnitData.price
          ? this.selectEditUnitData.price
          : null,
        superBuiltupArea: this.selectEditUnitData.superBuildUpArea
          ? this.selectEditUnitData.superBuildUpArea
          : null,
        superBuiltupAreaUnit:
          this.selectEditUnitData.superBuildUpAreaUnit ||
          this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        bhkType: this.selectEditUnitData?.bhkType
          ? BHKType[this.selectEditUnitData?.bhkType]
          : null,
        noOfBHK: this.selectEditUnitData.noOfBHK
          ? this.selectEditUnitData.noOfBHK?.toString()
          : null,
        facing: Facing[this.selectEditUnitData.facing],
        isWaterMark: this.selectEditUnitData?.isWaterMarkEnabled,
        taxationMode: this.selectEditUnitData?.taxationMode,
      });

      if (
        parseFloat(
          BHK_NO_ALL[
          BHK_NO_ALL.indexOf(this.selectEditUnitData.noOfBHK?.toString())
          ]
        ) > 5
      ) {
        this.bhkNoList = BHK_NO_ALL;
      }

      this.unitSubTypes = [];
      this.allUnitTypes?.map((item: any) => {
        if (
          item?.displayName === this.selectEditUnitData?.unitType?.displayName
        ) {
          item.childTypes?.map((item: any) => {
            this.unitSubTypes.push(item);
          });
        }
      });
      this.waterMarkSettingsObj.toAddWaterMark =
        this.selectEditUnitData?.isWaterMarkEnabled;
    }

    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaUnit = units || [];
      });
    this.sharedDataService.updateSharedTabData(1);

    this.initializeCustomFormFields();
  }

  initializeCustomFormFields() {
    this._store.dispatch(new FetchCustomForm());
    this._store
      .select(getCustomForm)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitBlockData = data;
        this.updateCustomFormFields();
      });

    this.sharedDataService.projectType$.subscribe((projectType: any) => {
      if (projectType && projectType !== this.currentProjectType) {
        this.currentProjectType = projectType;
        this.updateCustomFormFields();
      }
    });

    this.sharedDataService.projectSubType$.subscribe((subTypeId: any) => {
      if (subTypeId && subTypeId !== this.currentProjectSubType) {
        this.currentProjectSubType = subTypeId;
        this.updateCustomFormFields();
      }
    });
  }

  updateCustomFormFields() {
    const items = Array.isArray(this.unitBlockData) ? this.unitBlockData : this.unitBlockData?.items || [];

    const selectedProjectType = this.currentProjectType;
    const selectedProjectSubType = this.currentProjectSubType;

    if (!selectedProjectType || !selectedProjectSubType) {
      this.customFormFields = [];
      return;
    }

    const unitItems = items.filter((item: any) =>
      item.module?.toLowerCase() === 'unit' &&
      item.entityId === selectedProjectType &&
      item.entityChildId === selectedProjectSubType
    );

    this.customFormFields = unitItems.map((item: any) => ({
      id: item.id,
      displayName: item.fieldDisplayName,
      controlName: `customField_${item.id}` // Unique control name
    }));

    this.customFormFields.forEach(field => {
      if (!this.unitInfoDetails.get(field.controlName)) {
        this.unitInfoDetails.addControl(field.controlName, new FormControl(null));
      }
    });
  }

  buildCustomFieldsPayload(formData: any): any[] {
    const items = Array.isArray(this.unitBlockData) ? this.unitBlockData : this.unitBlockData?.items || [];
    const customFields: any[] = [];

    const unitCustomItems = items.filter((item: any) =>
      item.module?.toLowerCase() === 'unit' &&
      item.entityId === this.currentProjectType &&
      item.entityChildId === this.currentProjectSubType
    );

    unitCustomItems.forEach((item: any) => {
      const controlName = `customField_${item.id}`;
      const fieldValue = formData[controlName];

      if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '')  {
        customFields.push({
          formFieldId: item.id,
          // entityId: item.entityId,
          value: fieldValue.toString()
        });
      }
    });

    return customFields;
  }

  updateUnitSubTypes(unitType: string) {
    this.unitSubTypes = [];
    this.allUnitTypes?.forEach((item: any) => {
      if (item?.displayName === unitType) {
        item.childTypes?.forEach((subType: any) => {
          this.unitSubTypes.push(subType);
        });
      }
    });
  }

  getAttributesByType(attrType: string) {
    return this.allAttributes
      ?.filter((item: any) => item.attributeType.toLowerCase() == attrType)
      ?.filter((attr: any) => attr.basePropertyType.includes(this.propType));
  }

  getPropertyAttributes(): any[] {
    const basicAttrs = this.getAttributesByType('basic');
    let { ...basicAttributeForm } = this.unitInfoDetails.value;
    this.basicAttributes = basicAttrs?.reduce((acc: any, cur: any) => {
      acc = [
        ...acc,
        {
          attributeName: cur.attributeName,
          attributeDisplayName: cur.attributeDisplayName,
          masterProjectUnitAttributeId: cur.id,
          value: basicAttributeForm[cur.attributeName]?.toString(),
        },
      ];
      return acc;
    }, []);
    const addAttributes = this.selectedAdditionalAttr.map((attrId: string) => {
      return {
        masterProjectUnitAttributeId: attrId,
      };
    });
    return [...this.basicAttributes, ...addAttributes];
  }

  onMinus(attributeName: string) {
    let value = this.unitInfoDetails.controls[attributeName].value;
    if (value === undefined) {
      value = null;
      this.unitInfoDetails.controls[attributeName].setValue(value);
    } else if (value > 0) {
      value--;
      this.unitInfoDetails.controls[attributeName].setValue(value);
    }
  }

  onPlus(attributeName: string) {
    let value = this.unitInfoDetails.controls[attributeName].value;
    if (value === undefined) {
      value = null;
      value++;
      this.unitInfoDetails.controls[attributeName].setValue(value);
    } else if (value < 99 || value === undefined) {
      value++;
      this.unitInfoDetails.controls[attributeName].setValue(value);
    }
  }

  onAttributeSelectionChange(
    featureId: string,
    isChecked: boolean,
    displayName: string
  ) {
    this.filteredAttributes.selected = isChecked;
    const findAttribute = (attrDisplayName: string) =>
      this.filteredAttributes.find(
        (attr: any) => attr.attributeDisplayName === attrDisplayName
      );

    const removeAttribute = (attrId: string) => {
      this.selectedAdditionalAttr = this.selectedAdditionalAttr.filter(
        (attr) => attr !== attrId
      );
    };

    const addAttribute = (attrId: string) => {
      this.selectedAdditionalAttr.push(attrId);
    };

    switch (displayName) {
      case 'Pet Friendly':
        const petsNotAllowedAttr = findAttribute('Pets Not Allowed');
        if (petsNotAllowedAttr) {
          if (isChecked) {
            removeAttribute(petsNotAllowedAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      case 'Pets Not Allowed':
        const petFriendlyAttr = findAttribute('Pet Friendly');
        if (petFriendlyAttr) {
          if (isChecked) {
            removeAttribute(petFriendlyAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      case 'Family Only':
        const bachelorFriendlyAttr = findAttribute(
          'Bachelor And Couple Friendly'
        );
        if (bachelorFriendlyAttr) {
          if (isChecked) {
            removeAttribute(bachelorFriendlyAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;

      case 'Bachelor And Couple Friendly':
        const familyOnlyAttr = findAttribute('Family Only');
        if (familyOnlyAttr) {
          if (isChecked) {
            removeAttribute(familyOnlyAttr.id);
          }
        }
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;
      default:
        isChecked ? addAttribute(featureId) : removeAttribute(featureId);
        break;
    }
    this.emitAttributeSelection();
  }

  allAttributeSelectionCheck(): boolean {
    return (
      this.filteredAttributes?.length > 0 &&
      this.filteredAttributes.every((attr: any) =>
        this.selectedAdditionalAttr.includes(attr.id)
      )
    );
  }

  changeAllAttributeSelection(isChecked: boolean): void {
    if (isChecked) {
      this.selectedAdditionalAttr = this.filteredAttributes?.map(
        (attr: any) => attr.id
      );
    } else {
      this.selectedAdditionalAttr = [];
    }
    this.emitAttributeSelection();
  }

  emitAttributeSelection() {
    this.selectedAttributes?.emit(this.selectedAdditionalAttr);
  }

  decrement(property: string) {
    if (this.unitInfoDetails.get(property).value > 0) {
      let propName = this.unitInfoDetails.get(property).value;
      this.unitInfoDetails.get(property)?.setValue(--propName);
    }
  }

  increment(property: string) {
    let propName = this.unitInfoDetails.get(property).value;
    this.unitInfoDetails.get(property)?.setValue(++propName);
  }

  isAllFieldsValid($event: any) {
    this.unitInfoDetails.get('unitSubType')?.setValue(null);
    this.unitSubTypes = [];
    this.unitSubTypes = $event?.childTypes;
  }

  onSubmit() {
    this.imgFiles = [];
    this.vidFile = [];
    this.brochures = [];
    this.galleryS3Paths?.map((item: any) => {
      if (item.galleryType === 1) {
        this.imgFiles.push(item);
      } else if (item.galleryType === 2) {
        this.vidFile.push(item);
      } else if (item.galleryType === 3) {
        this.brochures.push(item);
      }
    });

    if (
      !this.unitInfoDetails.valid ||
      this.unitInfoDetails.controls['totalPrice'].value <
      this.unitInfoDetails.controls['pricePerUnit'].value
    ) {
      validateAllFormFields(this.unitInfoDetails);
      return;
    }

    // Build custom fields payload
    const customFields = this.buildCustomFieldsPayload(this.unitInfoDetails.value);

    let unitInfoData: any = {
      id: this.selectEditUnitData?.id,
      name: this.unitInfoDetails.value.unitName,
      furnishingStatus: this.unitInfoDetails.value.furnishingStatus
        ? FurnishStatus[this.unitInfoDetails.value.furnishingStatus]
        : 0,
      noOfBHK: this.unitInfoDetails.value.noOfBHK
        ? this.unitInfoDetails.value.noOfBHK
        : 0,
      bhkType: BHKType[this.unitInfoDetails.value.bhkType],
      area: this.unitInfoDetails.value.unitArea,
      areaUnitId: this.unitInfoDetails.value.unitArea
        ? this.unitInfoDetails.value.unitAreaSize
        : null,
      carpetArea: this.unitInfoDetails.value.carpetArea,
      carpetAreaUnitId: this.unitInfoDetails.value.carpetArea
        ? this.unitInfoDetails.value.carpetAreaUnit
        : null,
      buildUpArea: this.unitInfoDetails.value.builtupArea,
      buildUpAreaId: this.unitInfoDetails.value.builtupArea
        ? this.unitInfoDetails.value.builtupAreaUnit
        : null,
      superBuildUpArea: this.unitInfoDetails.value.superBuiltupArea,
      superBuildUpAreaUnit: this.unitInfoDetails.value.superBuiltupArea
        ? this.unitInfoDetails.value.superBuiltupAreaUnit
        : null,
      maintenanceCost: parseInt(this.unitInfoDetails.value.maintenanceCost),
      pricePerUnit: this.unitInfoDetails.value.pricePerUnit
        ? parseInt(this.unitInfoDetails.value.pricePerUnit)
        : null,
      currency: this.unitInfoDetails.value.currency || this.defaultCurrency,
      price: this.unitInfoDetails.value.totalPrice
        ? parseInt(this.unitInfoDetails.value.totalPrice)
        : null,
      facings: this.facingPayload,
      projectId: this.selectedDataId,
      unitTypeId: this.unitInfoDetails.value.unitSubType?.id,
      imageUrls: {
        additionalProp1: this.imgFiles,
        additionalProp2: this.vidFile,
        additionalProp3: this.brochures,
      },
      unitAttributes: this.getPropertyAttributes(),
      pageNumber: this.pageNumber,
      pageSize: this.pageSize,
      isWaterMarkEnabled: this.unitInfoDetails.value.isWaterMark,
      taxationMode: this.unitInfoDetails.value.taxationMode,
      customFields: customFields
    };
    if (this.selectEditUnitData) {
      unitInfoData = {
        projectId: this.selectedDataId,
        unitTypes: [
          {
            ...unitInfoData,
          },
        ],
      };
      this._store.dispatch(new UpdateProjectUnitInfo(unitInfoData));
    } else {
      this._store.dispatch(new AddUnitType(unitInfoData));
    }

    this.unitInfoDetails.reset();
    this.galleryS3Paths = [];
    this.imgFiles = [];
    this.vidFile = [];
    this.brochures = [];
    this.hasAllRequiredDataValid = false;
    this.selectEditUnitData = false;
    this.closeForm();
  }

  onFileSelection(file: File) {
    let fileSize = file;
    if (fileSize && Array.isArray(fileSize)) {
      if (fileSize.some((size) => size[2] > 15728640)) {
        this._notificationService.warn(`File size should be less than 15 MB.`);
        return;
      }
    }
    this.selectedImage = file;
    if (this.selectedImage?.length) {
      let imagesToBeUploadToS3Bucket = this.selectedImage
        .filter((imagePath: string) => {
          return imagePath[0].startsWith('data:');
        })
        .map((imagePath: string) => imagePath[0]);
      if (imagesToBeUploadToS3Bucket?.length) {
        this.imgService
          .uploadPropertyGallery(imagesToBeUploadToS3Bucket)
          .pipe(takeUntil(this.stopper))
          .subscribe((res: any) => {
            if (res?.data) {
              let pathArr = res?.data;
              pathArr?.map((path: string, index: number) => {
                this.allImagePath = path;
                if (
                  (this.selectedImage[index][1].includes('jpeg') ||
                    this.selectedImage[index][1].includes('png') ||
                    this.selectedImage[index][1].includes('gif') ||
                    this.selectedImage[index][1].includes('jpg')) &&
                  !this.waterMarkSettingsObj.toAddWaterMark
                ) {
                  this.galleryS3Paths.push({
                    name: this.selectedImage[index][1],
                    imageFilePath: this.allImagePath,
                    isCoverImage: false,
                    galleryType: 1,
                  });
                } else if (this.selectedImage[index][1]?.includes('mp4')) {
                  this.galleryS3Paths.push({
                    name: this.selectedImage[index][1],
                    imageFilePath: this.allImagePath,
                    isCoverImage: false,
                    galleryType: 2,
                  });
                } else if (this.selectedImage[index][1]?.includes('pdf')) {
                  this.galleryS3Paths.push({
                    name: this.selectedImage[index][1],
                    imageFilePath: this.allImagePath,
                    isCoverImage: false,
                    galleryType: 3,
                  });
                }
              });
            }
          });
      }
    }
  }

  closeForm() {
    this.galleryS3Paths = [];
    this.imgFiles = [];
    this.vidFile = [];
    this.brochures = [];
    this.selectEditUnitData = false;
    this.unitInfoDetails.reset();
    this.modalService.hide();
  }

  appendToBHKList(bhkNo: string) {
    if (bhkNo != '5+') return;
    this.bhkNoList = BHK_NO_ALL;
  }

  manageProject() {
    this.router.navigate(['/projects/manage-projects']);
  }

  deleteFile(index: any) {
    this.galleryS3Paths.splice(index, 1);
  }

  errMessage() {
    this._notificationService.warn(`Please select any unit type.`);
  }

  onUnitChange(unit: any) {
    const areaUnit = this.unitInfoDetails.get(unit).value;
    this.unitInfoDetails.controls[unit]?.setValue(null);
    if (
      !this.unitInfoDetails.get('unitAreaSize').value &&
      !this.unitInfoDetails.get('carpetAreaUnit').value &&
      !this.unitInfoDetails.get('builtupAreaUnit').value &&
      !this.unitInfoDetails.get('superBuiltupAreaUnit').value
    ) {
      this.unitInfoDetails.controls['unitAreaSize'].setValue(areaUnit);
      this.unitInfoDetails.controls['carpetAreaUnit'].setValue(areaUnit);
      this.unitInfoDetails.controls['builtupAreaUnit'].setValue(areaUnit);
      this.unitInfoDetails.controls['superBuiltupAreaUnit'].setValue(areaUnit);
    } else {
      this.unitInfoDetails.controls[unit].setValue(areaUnit);
    }
  }

  addProjectAttributes() {
    const propertyAttributes = this.selectEditUnitData?.attributes;
    this.selectedAdditionalAttr = [];
    const savedAttributesSelection = propertyAttributes?.reduce(
      (acc: any, cur: any) => {
        const [attr]: any =
          this.allAttributes?.filter(
            (item: any) => item.id === cur.masterProjectUnitAttributeId
          ) || [];
        const isAdditional = attr?.attributeType.toLowerCase() === 'additional';
        if (isAdditional) {
          this.selectedAdditionalAttr.push(cur.masterProjectUnitAttributeId);
        } else {
          acc = {
            ...acc,
            [attr?.attributeName]: cur.value,
          };
        }
        return acc;
      },
      {}
    );

    if (savedAttributesSelection) {
      let controls: any = {};
      this.allAttributes
        ?.filter((item: any) => item.attributeType.toLowerCase() == 'basic')
        ?.forEach((item: any) => {
          controls[item?.attributeName] =
            savedAttributesSelection[item?.attributeName];
        });

      this.unitInfoDetails.patchValue(controls);
    }
  }

  selectFacing(data: any, facing: any) {
    const index = this.facingArray.indexOf(facing.value);
    if (data && index === -1) {
      this.facingArray.push(facing.value);
      this.facingPayload.push(
        FACING.findIndex((item) => item.value === facing.value) + 1
      );
    } else if (!data && index !== -1) {
      this.facingArray.splice(index, 1);
      this.facingPayload.splice(index, 1);
    }
  }

  addWaterMarkImages(data: any) {
    if (data && Array.isArray(data)) {
      if (data.some((size) => size.size > 15728640)) {
        return;
      }
    }
    this.waterMarkSettingsObj.watermarkImages = data.filter((item: any) =>
      item.type.includes('image')
    );
    if (
      this.waterMarkSettingsObj.toAddWaterMark &&
      this.waterMarkSettingsObj.watermarkImages.length
    ) {
      let imgURL = this.waterMarkSettingsObj.watermarkLogo;
      let wtaerMarkPayload = {
        Url: imgURL,
        files: this.waterMarkSettingsObj.watermarkImages,
        WaterMarkPosition:
          this.waterMarkSettingsObj.watermarkPosition === 0
            ? '0'
            : this.waterMarkSettingsObj.watermarkPosition || 0,
        Opacity: this.waterMarkSettingsObj.watermarkOpacity || 100,
        Background: false,
        ImageSize: this.waterMarkSettingsObj.watermarkSize || 7,
      };
      this._store.dispatch(new AddWaterMark(wtaerMarkPayload));
    }
    if (
      !this.waterMarkSettingsObj.watermarkImages.length &&
      this.waterMarkSettingsObj.toAddWaterMark
    ) {
      this._notificationService.warn(`Watermark: JPG, PNG, GIF only.`);
    }
  }

  isAddWaterMark(data: any) {
    this.waterMarkSettingsObj.toAddWaterMark = data?.target?.checked;

    this.unitInfoDetails.patchValue({
      isWaterMark: this.waterMarkSettingsObj.toAddWaterMark,
    });
  }

  getAWSImagePath(imagePath: string) {
    return getAWSImagePath(imagePath);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
