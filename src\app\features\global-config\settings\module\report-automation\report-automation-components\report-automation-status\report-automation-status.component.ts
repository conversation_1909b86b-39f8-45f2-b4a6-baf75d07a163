import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { AppState } from 'src/app/app.reducer';
import { DisableReportAutomation, UpdateReportAutomation } from 'src/app/reducers/reports/reports.actions';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'report-automation-status',
  templateUrl: './report-automation-status.component.html',
})
export class ReportAutomationStatusComponent implements OnInit {
  status: boolean;
  params: any;
  canUpdateStatus: boolean = true;

  constructor(
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>
  ) { }

  agInit(params: any): void {
    this.params = params;
  }

  ngOnInit(): void {
    this.status = this.params.data.isEnabled === true ? true : false;
  }

  openConfirmModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'change the status ',
      title: data?.title,
      fieldType: `${data?.isEnabled === true ? 'Inactive' : 'Active'}`,
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: any) => {
        if (reason == 'confirmed') {
          if (this.status) {
            this.store.dispatch(new UpdateReportAutomation({ ...this.params?.data, isEnabled: true }));
          } else {
            this.store.dispatch(new DisableReportAutomation({ id: this.params?.data?.id, isEnabled: true }));
          }
        } else {
          this.status = this.params.data.isEnabled === true ? true : false;
        }
      });
    }
  }
}