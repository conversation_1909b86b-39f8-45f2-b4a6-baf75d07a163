<div class="lead-adv-filter bg-white brbl-15 brbr-15 overflow-hidden">
    <h3 class="bg-coal w-100 px-20 py-12 text-white flex-between fw-semi-bold">
        {{selectedBlockData ? 'Edit ' + (projectSubType == 'Plot' ? 'Phase' : 'Block') : 'Add New ' + (projectSubType == 'Plot' ? 'Phase' : 'Block')}}
    </h3>
    <form [formGroup]="basicInfoForm" class="px-20" autocomplete="off">
        <div class="d-flex flex-wrap w-100">
            <div class="w-50 ph-w-100">
                <div class="mr-10 ph-mr-0">
                    <div class="field-label-req">{{ 'GLOBAL.name' | translate }}</div>
                    <form-errors-wrapper [control]="basicInfoForm.controls['title']" label="Name">
                        <input type="text" required id="inpProjName" data-automate-id="inpProjName"
                            placeholder="ex. {{ projectSubType == 'Plot' ? 'Phase' : 'Block' }} A" formControlName="title">
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-50 ph-w-100">
                <div class="mr-10 ph-mr-0">
                    <div [ngClass]="basicInfoForm.controls['area'].value ? 'field-label-req' : 'field-label'">
                        {{'PROJECTS.area' | translate}}</div>
                    <div class="align-center">
                        <form-errors-wrapper [control]="basicInfoForm.controls['area']" label="Area"
                            class="w-60pr mr-10">
                            <input type="number" id="inpProjArea" data-automate-id="inpProjArea" placeholder="ex. 1400"
                                formControlName="area" [min]="0">
                        </form-errors-wrapper>
                        <div class="w-40pr">
                            <form-errors-wrapper label="Unit" [control]="basicInfoForm.controls['areaUnit']">
                                <ng-select [virtualScroll]="true" formControlName="areaUnit" [items]="areaSizeUnits"
                                    placeholder="ex. Sq. Mt." bindValue="id" bindLabel="unit"
                                    [readonly]="basicInfoForm.controls['area']?.value ? false : true"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-50 ph-w-100 field-rupees-tag">
                <div class="field-label">{{'PROJECTS.start-date' | translate}}</div>
                <form-errors-wrapper>
                    <div class="form-group mr-10">
                        <span class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                            [owlDateTimeTrigger]="dt1"></span>
                        <input type="text" formControlName="startDate" [owlDateTime]="dt1" readonly
                            [max]="basicInfoForm.value.endDate" [owlDateTimeTrigger]="dt1" bsDatepicker
                            placeholder="ex. 05/03/2025">
                        <owl-date-time [pickerType]="'calendar'" #dt1
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                    </div>
                </form-errors-wrapper>
            </div>
            <div class="w-50 ph-w-100 field-rupees-tag">
                <div class="field-label">{{'PROJECTS.end-date' | translate}}</div>
                <form-errors-wrapper>
                    <div class="form-group mr-10">
                        <span
                            class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                            [owlDateTimeTrigger]="dt2"></span>
                        <input type="text" formControlName="endDate" [owlDateTime]="dt2" [owlDateTimeTrigger]="dt2"
                            readonly bsDatepicker placeholder="ex. 19/06/2025" [min]="basicInfoForm.value.startDate">
                        <owl-date-time [pickerType]="'calendar'" #dt2
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                    </div>
                </form-errors-wrapper>
            </div>
            <div class="w-50 ph-w-100">
                <div class="field-label">{{'PROJECTS.possession-date' | translate}}</div>
                <form-errors-wrapper>
                    <div class="form-group mr-10 field-rupees-tag">
                        <span
                            class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                            (click)="isOpenPossessionModal = !isOpenPossessionModal"></span>
                        <input type="text"
                            [value]="selectedPossession? selectedPossession : basicInfoForm.value.globalRange"
                            id="inpPossessionDate" placeholder="ex. 29/06/2025" readonly
                            (click)="isOpenPossessionModal = !isOpenPossessionModal">
                        <owl-date-time [pickerType]="'calendar'" #dt2
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                    </div>
                </form-errors-wrapper>
                <div class="position-relative top-0 bg-white box-shadow-10 mr-10" *ngIf="isOpenPossessionModal">
                    <div class="d-flex">
                        <div class="w-100 bg-white">
                            <ng-container *ngFor="let type of dateFilterList">
                                <div class="form-check form-check-inline w-fit-content">
                                    <input type="radio" id="inpShowData{{type.value}}" name="globalRange"
                                        formControlName="globalRange" [value]="type.value"
                                         class="radio-check-input w-10 h-10">
                                    <label class="text-dark-gray text-large ml-8"
                                        for="inpShowData{{type.value}}">{{type.displayName}}</label>
                                </div>
                            </ng-container>
                            <div class="position-relative dashboard-filter form-group m-6 mb-16 border py-6" [ngClass]="{'pe-none disabled' : basicInfoForm.controls['globalRange'].value !== 'Custom Date', 
                                    'border-red-30': isValidPossessonDate,
                                    'border': !isValidPossessonDate}">
                                <form-errors-wrapper [control]="basicInfoForm.controls['globalDate']" label="Date">
                                    <span *ngIf="selectedMonth"
                                        class="fw-700 text-large text-black-200 px-12 w-90pr cursor-pointer"
                                        [owlDateTimeTrigger]="dt5">
                                        {{selectedMonth}} {{selectedYear}}
                                    </span>
                                    <span *ngIf="!selectedMonth" class="text-dark-gray px-12 w-90pr cursor-pointer"
                                        [owlDateTimeTrigger]="dt5">
                                        select month and year
                                    </span>
                                    <input type="text" [value]="selectedMonthAndYear" [owlDateTimeTrigger]="dt5"
                                        [owlDateTime]="dt5" placeholder="Select date" class="p-0 border-0 border-remove"
                                        (click)="isClosePossessionModal = false"
                                        style="height: 0 !important; opacity: 0; position: absolute; top: 0; left: 0; pointer-events: none;" />
                                    <owl-date-time #dt5 startView="year" [yearOnly]="true" [pickerType]="'calendar'"
                                        (monthSelected)="monthChanged($event)"></owl-date-time>
                                    <div *ngIf="isValidPossessonDate"
                                        class="mt-8 text-xs text-red position-absolute right-16 fw-semi-bold">
                                        Please select possession date
                                    </div>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                    <div class="flex-end p-6 border-top">
                        <div class="btn-coal" (click)="closePossessionModal()">Close</div>
                    </div>
                </div>
            </div>
            <div>
                <form-errors-wrapper [control]="basicInfoForm.controls['noOfFloors']" [label]="'Number of floors'"
                    class="position-relative mr-20 error">
                    <div class="field-label">Number of Floors</div>
                    <div class="spin-btn-container-gray">
                        <div class="spin-btn" (click)="decreaseNum()"><span class="spin-ic ic-minus"></span>
                        </div>
                        <div class="no-validation px-4">
                            <input type="number" placeholder="ex. 3" [min]="0" [max]="99" formControlName="noOfFloors"
                                autocomplete="off" />
                        </div>
                        <div class="spin-btn" (click)="increaseNum()"><span class="spin-ic ic-plus"></span>
                        </div>
                    </div>
                </form-errors-wrapper>
            </div>

            <!-- Custom Block Fields Section -->
            <ng-container *ngIf="customBlockFields?.length">
                <div class="w-100 mt-4">
                    <div class="align-center">
                        <h4 class="text-accent-green fw-600">Custom Block Fields</h4>
                        <hr class="flex-grow-1 mx-3">
                    </div>
                    <div class="d-flex flex-wrap">
                        <ng-container *ngFor="let field of customBlockFields">
                            <div class="w-50 ph-w-100">
                                <div class="mr-10 ph-mr-0">
                                    <div class="field-label">{{field.displayName}}</div>
                                    <form-errors-wrapper [control]="basicInfoForm.controls[field.controlName]" [label]="field.displayName">
                                        <input type="text" [placeholder]="'Enter ' + field.displayName" [formControlName]="field.controlName">
                                    </form-errors-wrapper>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
            </ng-container>
        </div>
    </form>
    <div class="flex-end p-12 border-top mt-12">
        <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="hideModalAndResetForm();">Cancel</u>
        <div class="btn-coal" (click)="addBlock();">Save {{ projectSubType == 'Plot' ? 'Phase' : 'Block' }}</div>
    </div>
</div>