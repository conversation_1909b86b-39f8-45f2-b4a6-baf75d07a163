import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchTenantAPIKeySuccess, TenantActionTypes } from './tenant.actions';

export type TenantState = {
  tenantAPIKey?: any;
  isTenantAPIKeyLoading: boolean;
};
const initialState: TenantState = {
  tenantAPIKey: [],
  isTenantAPIKeyLoading: true,
};
export function tenantReducer(
  state: TenantState = initialState,
  action: Action
): TenantState {
  switch (action.type) {
    case TenantActionTypes.FETCH_TENANT_API_KEY:
      return {
        ...state,
        isTenantAPIKeyLoading: true,
      };
    case TenantActionTypes.FETCH_TENANT_API_KEY_SUCCESS:
      return {
        ...state,
        tenantAPIKey: (action as FetchTenantAPIKeySuccess).resp,
        isTenantAPIKeyLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.tenant;

export const getTenantAPIKey = createSelector(
  selectFeature,
  (state: TenantState) => state.tenantAPIKey
);

export const getTenantAPIKeyIsLoading = createSelector(
  selectFeature,
  (state: TenantState) => state.isTenantAPIKeyLoading
);