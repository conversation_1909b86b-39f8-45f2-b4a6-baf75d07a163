import {
  Component,
  EventE<PERSON>ter,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, Subscription, firstValueFrom, skipWhile, switchMap, takeUntil } from 'rxjs';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';

import { AnimationOptions } from 'ngx-lottie';
import {
  PAGE_SIZE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchActivityExportSuccess,
  UpdateActivityFilterPayload,
  UpdateAllActivityFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getActivityReportsTotalCount,
  getAllActivityFilterPayload,
  getLevel10IsLoading,
  getLevel11IsLoading,
  getLevel12IsLoading,
  getLevel9IsLoading,
  getReportsActivity10List,
  getReportsActivity11List,
  getReportsActivity12List,
  getReportsActivity9List,
} from 'src/app/reducers/reports/reports.reducer';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';

import { FetchTagsList } from 'src/app/reducers/custom-tags/custom-tags.actions';
import {
  FetchDataReportsActivityCommunication,
  UpdateDataActivityFilterPayload,
} from 'src/app/reducers/data-reports/data-reports.action';
import {
  getActivityCommunicationIsLoading,
  getActivityIsLoading,
  getDataReportsActivityList,
  getReportsActivityCommunicationList,
} from 'src/app/reducers/data-reports/data-reports.reducers';
import { environment } from 'src/environments/environment';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';

@Component({
  selector: 'combined-activity-report',
  templateUrl: './combined-activity-report.component.html',
})
export class CombinedActivityReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: Array<any> = [];
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  getPages = getPages;
  appliedFilter: any;
  filtersPayload: any;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  isDateFilter: string = 'today';
  activityTotalCount: number;
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  isAllUsersLoading: boolean = true;

  getAllUsersFlag: boolean;
  isActivityCommunicationLoading: boolean = true;
  isActivityLoading: boolean = true;
  dataActivityTotalCount: number;
  // isCustomTagsLoading: boolean = true;
  customFlags: Array<any> = [];
  paginationCount: number;
  showFilters: boolean = false;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  getFlagsCount$: Subscription;
  userData: any;
  isLevel9Loading: boolean = true;
  isLevel10Loading: boolean = true;
  isLevel11Loading: boolean = true;
  isLevel12Loading: boolean = true;
  onPickerOpened = onPickerOpened;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  s3BucketUrl: string = environment.s3ImageBucketURL;
  filteredColumnDefsCache: any[] = [];

  @ViewChild('reportsGraph') reportsGraph: any;
  globalSettingsData: any;

  get isGraphLoading(): boolean {
    return (
      this.isLevel9Loading ||
      this.isLevel10Loading ||
      this.isLevel11Loading ||
      this.isLevel12Loading ||
      this.isActivityCommunicationLoading ||
      this.isActivityLoading
    );
  }

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef
  ) {
    this._store.dispatch(new FetchTagsList());

    // this._store
    //   .select(getCustomTagsIsLoading)
    //   .pipe(takeUntil(this.stopper))
    //   .subscribe(
    //     (isLoading: boolean) =>
    //       (this.isCustomTagsLoading = isLoading)
    //   );

    this._store
      .select(getLevel9IsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLevel9Loading = isLoading;
      });

    this._store
      .select(getLevel10IsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLevel10Loading = isLoading;
      });

    this._store
      .select(getLevel11IsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLevel11Loading = isLoading;
      });

    this._store
      .select(getLevel12IsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLevel12Loading = isLoading;
      });

    this._store
      .select(getActivityCommunicationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isActivityCommunicationLoading = isLoading;
      });

    this._store
      .select(getActivityIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isActivityLoading = isLoading;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.filtersPayload = {
          fromDate: setTimeZoneDate(
            new Date(this.currentDate),
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          toDate: setTimeZoneDate(
            new Date(this.currentDate),
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
        };
      });

    this.headerTitle.setTitle('Activity Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);

    this._store
      .select(getAllActivityFilterPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = {
          ...this.filtersPayload,
          ...data,
          isNavigatedFromReports: true,
        };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          userStatus: userStatus,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          visibility: this.filtersPayload?.userStatus,
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
        };
        this.activeDate();
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        this.isOnlyReporteesLoading = false;
        if (!isLoading && this.getAllUsersFlag) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');

          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        this.isAllUsersLoading = false;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getReportsActivity9List)
      .pipe(
        skipWhile(() => this.isLevel9Loading),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        if (this.rowData?.length) {
          this.rowData = this.rowData?.map?.((row: any) => {
            const matchingData = data.find(
              (item: any) => item.userId === row.userId
            );
            return { ...row, ...matchingData };
          });
          return;
        }
        this.rowData = data;
        this.paginationCount = this.rowData?.length;
        this.canCalcTotal();
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
          this.getAllUsersFlag = true;
        } else if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });

    this._store
      .select(getActivityReportsTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: number) => {
        this.activityTotalCount = count;
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    // this._store
    //   .select(getTagsList)
    //   .pipe(
    //     skipWhile(() => this.isCustomTagsLoading),
    //     take(1)
    //   )
    //   .subscribe((data: any) => {
    //     let flagData = data || [];
    //     this.customFlags = flagData?.filter((flag: any) => flag?.isActive);
    //   });
  }

  async ngOnInit() {
    this.globalSettingsData = await firstValueFrom(
      this._store
        .select(getGlobalSettingsAnonymous)
        .pipe(skipWhile((data) => !Object.keys(data).length))
    );
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });
    this.initializeGridSettings();
    this.initializeGraphData();
  }

  canCalcTotal() {
    if (
      !(
        this.isLevel9Loading ||
        this.isLevel10Loading ||
        this.isLevel11Loading ||
        this.isLevel12Loading ||
        this.isActivityCommunicationLoading ||
        this.isActivityLoading
      )
    ) {
      this.rowData = getTotalCountForReports(
        this.rowData.filter((data: any) => data?.projectTitle !== 'Total')
      );
    }
  }

  // getTotal() {
  //   if (this.getFlagsCount$)
  //     this.getFlagsCount$?.unsubscribe();
  //   this.getFlagsCount$ = this._store
  //     .select(getFlagCounts)
  //     .pipe(
  //       takeUntil(this.stopper)
  //     )
  //     .subscribe((data: any) => {
  //       if (data && data.items) {
  //         this.rowData = this.rowData.map((row: any) => {
  //           const matchingData = data.items.find(
  //             (item: any) => item.userId === row.userId
  //           );
  //           matchingData?.flags?.forEach((flagData: any) => {
  //             row = { ...row, [flagData.name + 'Count']: flagData?.count || 0 };
  //             row[flagData.name + 'UniqueLeadsCount'] =
  //               flagData?.uniqueLeadsCount || 0;
  //           });
  //           return { ...row };
  //         });
  //         this.rowData = getTotalCountForReports(this.rowData.filter((data: any) => data?.projectTitle !== "Total"));
  //       }
  //     });
  // }


  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'User Name' && col.field !== 'Total'
    ).map((col: any) => {
      if (col.children && col.children.length > 0) {
        const filteredChildren = col.children.filter((child: any) => child.field !== 'Working Hours');
        return {
          ...col,
          isParent: true,
          children: filteredChildren
        };
      }
      return col;
    });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [params.data?.userName],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p class="py-16 text-truncate">${params.value[0]}</p>`;
        },
      },
    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.columnDefs.push(
      {
        headerName: 'Leads',
        field: 'Leads',
        children: [
          {
            headerName: 'Working Hours',
            field: 'Working Hours',
            valueGetter: (params: any) => {
              const averageWorkingHours = params.data?.averageWorkingHours;
              return averageWorkingHours !== '00:00:00' && averageWorkingHours
                ? [moment(averageWorkingHours, 'HH:mm:ss').format('HH:mm:ss')]
                : ['--'];
            },
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel12Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0]}</p>`;
              }
            },
          },
          {
            headerName: 'Calls',
            field: 'Calls(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.callsInitiatedCount,
              params.data?.callsInitiatedLeadsCount,
            ],
            valueLabels: ['Calls', 'Calls (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel11Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Whatsapp',
            field: 'Whatsapp(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.whatsAppInitiatedCount,
              params.data?.whatsAppInitiatedLeadsCount,
            ],
            valueLabels: ['Whatsapp', 'Whatsapp (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel11Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Email',
            field: 'Email(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.emailsInitiatedCount,
              params.data?.emailsInitiatedLeadsCount,
            ],
            valueLabels: ['Email', 'Email (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel11Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'SMS',
            field: 'SMS(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.smsInitiatedCount,
              params.data?.smsInitiatedLeadsCount,
            ],
            valueLabels: ['SMS', 'SMS (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel11Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Status Edits',
            field: 'Status Edits(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.statusEditsCount,
              params.data?.statusEditsLeadsCount,
            ],
            valueLabels: ['Status Edits', 'Status Edits (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel10Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Form Edits',
            field: 'Form Edits(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.formEditsCount,
              params.data?.formEditsLeadsCount,
            ],
            valueLabels: ['Form Edits', 'Form Edits (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel10Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Notes Added',
            field: 'Notes Added(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.notesAddedCount,
              params.data?.notesAddedLeadsCount,
            ],
            valueLabels: ['Notes Added', 'Notes Added (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel10Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Meeting Scheduled',
            field: 'Meeting Scheduled(leads)',
            filter: false,
            valueGetter: (params: any) => [params.data?.meetingScheduledCount],
            minWidth: 150,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p class="py-16 text-truncate">${params.value[0] ? params.value[0] : '--'
                  }</p>`;
              }
            },
          },
          {
            headerName: 'Meeting Done',
            field: 'Meeting Done(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.meetingDoneCount,
              params.data?.meetingDoneUniqueCount,
            ],
            valueLabels: ['Meeting Done', 'Meeting Done (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel12Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
            field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.siteVisitScheduledCount,
            ],
            minWidth: 150,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p class="py-16 text-truncate">${params.value[0] ? params.value[0] : '--'
                  }</p>`;
              }
            },
          },
          {
            headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
            field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.siteVisitDoneCount,
              params.data?.siteVisitDoneUniqueCount,
            ],
            valueLabels: [
              !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
              (!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken') + ' (unique count)'
            ],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isLevel12Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
                <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Callback',
            field: 'Callback(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.callbackScheduledLeadsCount,
            ],
            minWidth: 90,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Booked',
            field: 'Booked(leads)',
            filter: false,
            valueGetter: (params: any) => [params.data?.bookedLeadsCount],
            minWidth: 75,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Invoiced',
            field: 'Invoiced(leads)',
            filter: false,
            valueGetter: (params: any) => [params.data?.invoicedLeadsCount],
            minWidth: 75,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Booking Cancel',
            field: 'Booking Cancel(leads)',
            filter: false,
            valueGetter: (params: any) => [params.data?.bookingCancelCount],
            minWidth: 120,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Not interested',
            field: 'Not interested(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.notInterestedLeadsCount,
            ],
            minWidth: 115,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Dropped',
            field: 'Dropped(leads)',
            filter: false,
            valueGetter: (params: any) => [params.data?.droppedLeadsCount],
            minWidth: 80,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Expression Of Interest',
            field: 'Expression Of Interest(leads)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.expressionOfInterestLeadCount,
            ],
            minWidth: 160,
            cellRenderer: (params: any) => {
              if (this.isLevel9Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-elastic"></div>
              </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          // remove because backend not handled

          // ...this.customFlags?.map((flags: any) => ({
          //   headerName: flags.name,
          //   field: flags.name,
          //   filter: false,
          //   valueGetter: (params: any) => {
          //     return [
          //       params?.data[flags?.name + 'Count'] || 0,
          //       params?.data[flags?.name + 'UniqueLeadsCount'] || 0,
          //     ];
          //   },
          //   minWidth: 115,
          //   cellRenderer: (params: any) => {
          //     if (this.isCustomTagsLoading) {
          //       return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          //       <div class="dot-elastic"></div>
          //     </ng-container></div>`;
          //     } else {
          //       return `<p>${params.value[0] ? params.value[0] : '--'}</p>
          //       <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
          //         }<span></p>`;
          //     }
          //   },
          // })),
        ],
      },
      {
        headerName: 'Data',
        field: 'Data',
        children: [
          {
            headerName: 'Calls',
            field: 'Calls(Data)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.callsInitiatedAllCount,
              params.data?.callsInitiatedDataUniqueCount,
            ],
            valueLabels: ['Calls', 'Calls (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isActivityCommunicationLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Whatsapp',
            field: 'Whatsapp(Data)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.whatsAppInitiatedAllCount,
              params.data?.whatsAppInitiatedDataUniqueCount,
            ],
            valueLabels: ['Whatsapp', 'Whatsapp (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isActivityCommunicationLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Status Edits',
            field: 'Status Edits(Data)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.statusEditsAllCount,
              params.data?.statusEditsDataUniqueCount,
            ],
            valueLabels: ['Status Edits', 'Status Edits (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isActivityLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Form Edits',
            field: 'Form Edits(Data)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.formEditsAllCount,
              params.data?.formEditsDataUniqueCount,
            ],
            valueLabels: ['Form Edits', 'Form Edits (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isActivityLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Notes Added',
            field: 'Notes Added(Data)',
            filter: false,
            valueGetter: (params: any) => [
              params.data?.notesAddedAllCount,
              params.data?.notesAddedDataUniqueCount,
            ],
            valueLabels: ['Notes Added', 'Notes Added (unique count)'],
            minWidth: 130,
            cellRenderer: (params: any) => {
              if (this.isActivityLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
                  }<span></p>`;
              }
            },
          },
          {
            headerName: 'Not interested',
            field: 'Not interested(Data)',
            filter: false,
            valueGetter: (params: any) => [params.data?.notInterestedDataCount],
            minWidth: 115,
            cellRenderer: (params: any) => {
              if (this.isActivityLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
          {
            headerName: 'Not Reachable',
            field: 'Not Reachable(Data)',
            filter: false,
            valueGetter: (params: any) => [params.data?.notReachableDataCount],
            minWidth: 115,
            cellRenderer: (params: any) => {
              if (this.isActivityLoading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
              }
            },
          },
        ],
      },
      {
        headerName: 'Total',
        field: 'Total',
        children: [
          {
            headerName: 'Call Total',
            field: 'Call Total',
            filter: false,
            minWidth: 130,
            valueGetter: (params: any) => {
              const leadCall = params.data?.callsInitiatedCount || 0;
              const leadCallUnique = params.data?.callsInitiatedLeadsCount || 0;
              const dataCall = params.data?.callsInitiatedAllCount || 0;
              const dataCallUnique = params.data?.callsInitiatedDataUniqueCount || 0;

              return [
                leadCall + dataCall,
                leadCallUnique + dataCallUnique,
              ];
            },
            valueLabels: ['Call Total', 'Call Total (unique count)'],
            cellRenderer: (params: any) => {
              if (this.isActivityCommunicationLoading || this.isLevel11Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `
            <p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate-1 break-all">
              <span class="text-dark-gray">unique count: </span>
              <span class="fw-600">${params.value[1] ? params.value[1] : '--'}</span>
            </p>`;
              }
            },
          },
          {
            headerName: 'Whatsapp Total',
            field: 'Whatsapp Total',
            filter: false,
            minWidth: 130,
            valueGetter: (params: any) => {
              const leadWhatsapp = params.data?.whatsAppInitiatedCount || 0;
              const leadWhatsappUnique = params.data?.whatsAppInitiatedLeadsCount || 0;
              const dataWhatsapp = params.data?.whatsAppInitiatedAllCount || 0;
              const dataWhatsappUnique = params.data?.whatsAppInitiatedDataUniqueCount || 0;

              return [
                leadWhatsapp + dataWhatsapp,
                leadWhatsappUnique + dataWhatsappUnique,
              ];
            },
            valueLabels: ['Whatsapp Total', 'Whatsapp Total (unique count)'],
            cellRenderer: (params: any) => {
              if (this.isActivityCommunicationLoading || this.isLevel11Loading) {
                return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
              } else {
                return `
            <p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate-1 break-all">
              <span class="text-dark-gray">unique count: </span>
              <span class="fw-600">${params.value[1] ? params.value[1] : '--'}</span>
            </p>`;
              }
            }
          }]
      }
    );

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this._store
      .select(getReportsActivity11List)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = this.rowData?.map?.((row: any) => {
          const matchingData = data.find(
            (item: any) => item.userId === row.userId
          );
          return { ...row, ...matchingData };
        });
        this.canCalcTotal();
      });

    this._store
      .select(getReportsActivity10List)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        // this.activityTotalCount = data.totalCount;
        this.rowData = this.rowData?.map?.((row: any) => {
          const matchingData = data.find(
            (item: any) => item.userId === row.userId
          );
          return { ...row, ...matchingData };
        });
        this.canCalcTotal();
      });
    this._store
      .select(getReportsActivity12List)
      .pipe(
        skipWhile(() => this.isLevel12Loading || this.isLevel9Loading),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        this.rowData = this.rowData?.map?.((row: any) => {
          const matchingData = data.find(
            (item: any) => item.userId === row.userId
          );
          return { ...row, ...matchingData };
        });
        this.canCalcTotal();
      });

    this._store
      .select(getReportsActivityCommunicationList)
      .pipe(
        skipWhile(() => this.isActivityCommunicationLoading),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        if (this.rowData?.length) {
          this.rowData = this.rowData?.map?.((row: any) => {
            const matchingData = data.items2.find(
              (item: any) => item.userId === row.userId
            );
            return { ...row, ...matchingData };
          });
        }

        this.canCalcTotal();
      });
    this._store
      .select(getDataReportsActivityList)
      .pipe(
        skipWhile(() => this.isActivityLoading),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        if (this.rowData?.length) {
          this.rowData = this.rowData?.map?.((row: any) => {
            const matchingData = data.items?.find(
              (item: any) => item.userId === row.userId
            );
            return { ...row, ...matchingData };
          });
          this.canCalcTotal();
        }
      });
  }

  getData(operation: string, event: any, leadTags?: string) {
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.date[0]
      ? 'Modified Date'
      : 'All';
    this.gridOptionsService.status = operation;
    this.gridOptionsService.leadTags = leadTags;
    this.gridOptionsService.payload = this.filtersPayload;
  }

  getMeetingCount(operation: string, event: any, meetingStatus: string) {
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = this.filtersPayload;
    this.gridOptionsService.meetingStatus = visitMeeting;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);

    this._store.dispatch(
      new UpdateDataActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new UpdateActivityFilterPayload(this.filtersPayload));
    this._store.dispatch(
      new UpdateAllActivityFilterPayload(this.filtersPayload)
    );
    this.rowData = [];
    this._store.dispatch(new FetchDataReportsActivityCommunication('All'));
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateActivityFilterPayload(this.filtersPayload));
    this._store.dispatch(
      new UpdateDataActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(
      new UpdateAllActivityFilterPayload(this.filtersPayload)
    );
    this.rowData = [];
    this._store.dispatch(new FetchDataReportsActivityCommunication('All'));
    this.currOffset = 0;
  }

  filterFunction() {
    this.rowData = [];
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter?.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
    };
    this._store.dispatch(new UpdateActivityFilterPayload(this.filtersPayload));
    this._store.dispatch(
      new UpdateDataActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(
      new UpdateAllActivityFilterPayload(this.filtersPayload)
    );
    this.rowData = [];
    this._store.dispatch(new FetchDataReportsActivityCommunication('All'));
    this.currOffset = 0;
    if (this.appliedFilter.users?.length || this.appliedFilter.date?.[0]) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (['date'].includes(key)) {
      this.appliedFilter[key] = null;
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  filterByDate(type?: string) {
    // let newDate = new Date();
    let date = new Date(this.currentDate.setHours(0, 0, 0, 0));
    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.date[0] = new Date(date);
        this.appliedFilter.date[1] = new Date(date);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.date[0] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        this.appliedFilter.date[1] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.date[0] = new Date(date).setDate(
          new Date(date).getDate() - 6
        );
        this.appliedFilter.date[1] = new Date(date);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.date[0] = null;
        this.appliedFilter.date[1] = null;
        break;
    }
  }

  activeDate() {
    const fromDate = new Date(this.appliedFilter.date[0]);
    const toDate = new Date(this.appliedFilter.date[1]);

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);

    if (
      fromDate.toDateString() === today.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'today';
    } else if (
      fromDate.toDateString() === yesterday.toDateString() &&
      toDate.toDateString() === yesterday.toDateString()
    ) {
      this.isDateFilter = 'yesterday';
    } else if (
      fromDate.toDateString() === sevenDaysAgo.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'sevenDays';
    } else {
      this.isDateFilter = 'custom';
    }
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      date: [new Date(this.currentDate), new Date(this.currentDate)],
    };
    this.isDateFilter = 'today';
    this.filterFunction();
  }

  resetDate() {
    this.appliedFilter = {
      ...this.appliedFilter,
      date: [new Date(this.currentDate), new Date(this.currentDate)],
    };
    this.isDateFilter = 'today';
    this.filterFunction();
  }

  exportActivityReport() {
    this._store.dispatch(new FetchActivityExportSuccess(''));
    // this.filterFunction();
    this.filtersPayload = {
      ...this.filtersPayload,
      shouldShowDataReport: true,
    };
    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        path: this.filtersPayload?.path || 'report/activity/level9',
        shouldShowDataReport: true,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' &&
      this.reportsGraph?.isChartReady &&
      !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
