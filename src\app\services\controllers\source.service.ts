import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class SourceService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'source';
  }

  updateSourceStatus(id: string, isEnabled: boolean): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/status/${id}`, { isEnabled });
  }

  bulkUpdateSourceStatus(sourceIds: string[], isEnabled: boolean): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/status/multiple`, { sourceIds, isEnabled });
  }

  getLeadAndDataCount(sourceValue: string): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/leadanddata-count?sourceValue=${sourceValue}`);
  }

  convertToDirect(sourceValue: number): Observable<any> {
    return this.http.put(`${env.baseURL}api/v1/source/converttodirect`, { "sourceValue": sourceValue });
  }
}
