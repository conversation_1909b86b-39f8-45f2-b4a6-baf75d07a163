<div class="align-center flex-wrap pt-20 w-100" [ngClass]="{'gray-scale': isPropertySoldOut}">
    <div *ngIf="propertyInfo?.possessionDate" class="align-center mb-10 w-20 ip-w-33">
        <div class="bg-ash dot dot-md mr-8"><span class="icon ic-key ic-xxs ic-black"></span></div>
        <div class="text-nowrap fw-600 name mr-10 text-truncate-1">
            <span
                *ngIf="getTimeZoneDate(propertyInfo?.possessionDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') <= getTimeZoneDate(currentDate,  userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') else date">
                {{ 'PROPERTY.ready-to-move' | translate }}
            </span>
            <ng-template #date>
                <span>{{getTimeZoneDate(propertyInfo.possessionDate , userData?.timeZoneInfo?.baseUTcOffset,
                    'dayMonthYear') }}</span>
            </ng-template>
        </div>
    </div>
    <ng-container *ngIf="propertyInfo?.propertyType?.displayName != 'Agricultural'">
        <div *ngIf="propertyInfo?.furnishStatus != 0" class="align-center mb-10 w-20 ip-w-33">
            <div class="bg-ash dot dot-md mr-8"><span class="icon ic-sofa ic-xxs ic-black"></span></div>
            <div class="fw-600 name text-truncate-1 mr-10">{{FurnishStatus[propertyInfo?.furnishStatus]}}</div>
        </div>
        <div *ngIf="propertyInfo?.facing != 0" class="align-center mb-10 w-20 ip-w-33">
            <div class="bg-ash dot dot-md mr-8"><span class="icon ic-compass ic-xxs ic-black"></span></div>
            <div class="fw-600 name mr-10 text-truncate-1">{{Facing[propertyInfo?.facing]}}</div>
        </div>
        <div *ngIf="(attributesSelection?.numberOfFloors?.value || attributesSelection?.floorNumber?.value) && !isListing"
            class="align-center mb-10 w-20 ip-w-33">
            <div class="bg-ash dot dot-md mr-8"><span class="icon ic-stairs ic-xxs ic-black"></span></div>
            <div class="fw-400 text-sm text-nowrap text-truncate-1">
                <span class="text-normal fw-600" *ngIf="attributesSelection?.floorNumber?.value">
                    {{attributesSelection.floorNumber.value}}
                    <sup>{{nth(attributesSelection.floorNumber.value)}}</sup></span>
                <span *ngIf="attributesSelection?.numberOfFloors?.value && attributesSelection?.floorNumber?.value">
                    {{ 'GLOBAL.of' | translate }}</span> {{attributesSelection.numberOfFloors.value}}
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="propertyInfo?.propertyType?.displayName == 'Residential'">
        <ng-container *ngFor="let attr of attrMap">
            <div *ngIf="attributesSelection?.[attr.name]?.value > 0 &&
                      (!isListing || 
                       (isListing && 
                        ['No. of Bed Rooms', 'Parking', 'No. of Bath Rooms'].includes(attr.displayName)))"
                class="align-center mb-10 text-nowrap w-20 ip-w-33">
                <div class="bg-ash dot dot-md mr-8">
                    <span class="icon ic-xxs ic-black" [ngClass]="attr.icon"></span>
                </div>
                <div class="fw-600 name mr-10 text-truncate-1">
                    <span>{{attributesSelection?.[attr.name]?.value}} </span>
                    <span class="fw-400 text-sm">
                        {{ attributesSelection?.[attr.name]?.value > 1 ? attr.pluralShortName : attr.singularShortName
                        }}
                    </span>
                </div>
            </div>
        </ng-container>
    </ng-container>
</div>
<!-- <ng-container *ngIf="AdditionalAttrData?.length">
    <div class="d-flex">
        <h4 class="text-mud mt-20 fw-semi-bold ph-br-mud">Attributes</h4>
    </div>
    <div class="align-center flex-wrap mt-6">
        <div *ngFor="let attr of AdditionalAttrData, let last = last">
            <div *ngIf="attr?.attributeDisplayName" class="align-center"><span
                    class="dot dot-xxs bg-black-100 mx-8"></span>{{attr?.attributeDisplayName}}
                    
                </div>
        </div>
    </div>
</ng-container> -->