import {
  Http<PERSON><PERSON>,
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import {
  BehaviorSubject,
  catchError,
  filter,
  firstValueFrom,
  Observable,
  skipWhile,
  switchMap,
  take,
  throwError,
} from 'rxjs';

import { ErrorActionCode } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getTenantName } from 'src/app/core/utils/common.util';
import { FetchGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchLeadStatusList,
  FetchModifiedDatesList,
  FetchProjectTypes,
  FetchPropertyTypesList,
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getProjectTypes,
  getPropertyTypes,
  getStatusMasterData,
} from 'src/app/reducers/master-data/master-data.reducer';
import { environment } from 'src/environments/environment';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  identityURL: string = environment.identityURL;
  subDomain: string = getTenantName();
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

  constructor(
    private http: HttpClient,
    private store: Store<AppState>,
    private _notificationsService: NotificationsService
  ) {
  }

  private checkTokenExpiration(token: string): boolean {
    if (!token) return true;
    try {
      const expirationTime = JSON.parse(atob(token.split('.')[1])).exp;
      // console.log('Token expiration time:', new Date(expirationTime * 1000).toLocaleString());
      return Math.floor(new Date().getTime() / 1000) >= expirationTime;
    } catch (e) {
      return true;
    }
  }

  private updateTokenExpiration(idToken: string): void {
    try {
      const expirationTime = JSON.parse(atob(idToken.split('.')[1])).exp;
      localStorage.setItem('tokenExpiration', expirationTime.toString());
      // console.log('Updated token expiration:', new Date(expirationTime * 1000).toLocaleString());
    } catch (e) {
      console.error('Error updating token expiration:', e);
    }
  }

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    let contentType = 'application/json; charset=utf-8';
    let token = localStorage.getItem('idToken') || '';
    let req: any;
    if (request.url.includes('accounts.google')) {
      return next.handle(request);
    }

    if (
      request.url.includes('excel') ||
      request.url.includes('upload-file') ||
      request.url.includes('bulk') ||
      request.url.includes('send') ||
      request.url.includes('send/batch') ||
      request.url.includes('image/upload') ||
      request.url.includes('doc/formfile')
    ) {
      contentType = null;
    }
    const isNoAuthNeeded = (request?.body as any)?.no_auth_needed;
    if (request.url.includes('tokens') || isNoAuthNeeded) {
      req = request.clone({
        setHeaders: {
          ...(contentType && {
            'Content-Type': contentType,
          }),
          Accept: 'application/json',
          tenant: this.subDomain,
        },
      });
    } else {
      // Check token expiration before making the request
      if (this.checkTokenExpiration(token)) {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          if (!this.isRefreshing) {
            this.isRefreshing = true;
            this.refreshTokenSubject.next(null);

            const payload = {
              refreshToken,
              token,
            };
            return this.http
              .post(`${this.identityURL}api/tokens/refresh`, payload)
              .pipe(
                switchMap((res: any) => {
                  const { idToken, refreshToken } = res?.data;
                  if (idToken && refreshToken && res?.data) {
                    localStorage.setItem('idToken', idToken);
                    localStorage.setItem('refreshToken', refreshToken);
                    this.updateTokenExpiration(idToken);
                    this.isRefreshing = false;
                    this.refreshTokenSubject.next(idToken);
                    req = request.clone({
                      setHeaders: {
                        ...(contentType && {
                          'Content-Type': contentType,
                        }),
                        Accept: 'application/json',
                        Authorization: `Bearer ${idToken}`,
                        tenant: this.subDomain,
                      },
                    });
                    return next.handle(req);
                  }
                  return throwError(() => new Error('Invalid refresh token response'));
                }),
                catchError((err) => {
                  this.isRefreshing = false;
                  localStorage.clear();
                  window.location.href = '/login';
                  return throwError(() => err);
                })
              );
          } else {
            return this.refreshTokenSubject.pipe(
              filter((idToken) => idToken != null),
              take(1),
              switchMap((idToken) => {
                req = request.clone({
                  setHeaders: {
                    ...(contentType && {
                      'Content-Type': contentType,
                    }),
                    Accept: 'application/json',
                    Authorization: `Bearer ${idToken}`,
                    tenant: this.subDomain,
                  },
                });
                return next.handle(req);
              })
            );
          }
        }
      }

      let idToken = token.length > 0 ? `Bearer ${token}` : '';
      req = request.clone({
        setHeaders: {
          ...(contentType && {
            'Content-Type': contentType,
          }),
          Accept: 'application/json',
          Authorization: idToken,
          tenant: this.subDomain,
        },
      });
    }

    return next.handle(req).pipe(
      catchError((err: HttpErrorResponse) => {
        if (err?.error?.exception === 'Incorrect Credentials!')
          return throwError(() => err);
        let prevSubDomain = localStorage.getItem('subDomain');
        if (
          this.subDomain &&
          prevSubDomain &&
          prevSubDomain !== this.subDomain
        ) {
          localStorage.clear();
          window.location.href = '/login';
        }
        if (err.status === 401) {
          if (
            err?.error instanceof Object &&
            Object.keys(err?.error)?.length > 0
          ) {
            const body = err?.error;
            if (
              body?.actionCode == ErrorActionCode.Logout &&
              !window.location.pathname.includes('/login') &&
              !window.location.pathname.includes('/no-auth') &&
              !window.location.pathname.includes('/external')
            ) {
              localStorage?.clear();
              window.location.href = '/login';
            }
          }
          if (err.url.includes('refresh')) {
            // localStorage.clear();
            // this.router.navigate(['/login']);
          } else {
            const refreshToken = localStorage.getItem('refreshToken');
            const token = localStorage.getItem('idToken');

            this.store.select(getStatusMasterData).subscribe(async (data: any) => {
              if (data?.length) {
                const globalSettingsData: any = await firstValueFrom(
                  this.store.select(getGlobalSettingsAnonymous).pipe(skipWhile((data) => !Object.keys(data).length))
                );
                let status = [...data]
                if (globalSettingsData?.shouldRenameSiteVisitColumn) {
                  status = status?.map((item: any) => {
                    if (item.displayName === 'Site Visit Scheduled') {
                      return { ...item, displayName: 'Referral Scheduled', actionName: 'Schedule Referral' }
                    }
                    return { ...item }
                  })
                }
                const allLeadStatus: any = JSON.stringify(status);
                localStorage.setItem('masterleadstatus', allLeadStatus);
              }
            });

            this.store.select(getProjectTypes).subscribe((data: any) => {
              if (data?.items?.length) {
                let projectTypeList = JSON.stringify(data?.items);
                localStorage.setItem('projectType', projectTypeList);
              }
            });

            this.store.select(getPropertyTypes).subscribe((data: any) => {
              if (data?.length) {
                let propertyTypeList = JSON.stringify(data);
                localStorage.setItem('propertyType', propertyTypeList);
              }
            });

            if (refreshToken) {
              const payload = {
                refreshToken,
                token,
              };
              return this.http
                .post(`${this.identityURL}api/tokens/refresh`, payload)
                .pipe(
                  switchMap((res: any) => {
                    const { idToken, refreshToken } = res?.data;
                    if (idToken && refreshToken && res?.data) {
                      localStorage.setItem('idToken', idToken);
                      localStorage.setItem('refreshToken', refreshToken);
                      // Update token expiration time
                      this.updateTokenExpiration(idToken);

                      if (idToken) {
                        this.store
                          .select(getGlobalSettingsAnonymous)
                          .pipe(
                            filter(
                              (data: any) => data && !!Object.keys(data).length
                            ),
                            take(1)
                          )
                          .subscribe((data: any) => {
                            if (data?.shouldEnablePropertyListing) {
                              this.store.dispatch(
                                new FetchPropertyTypesList('listing')
                              );
                            } else {
                              this.store.dispatch(new FetchPropertyTypesList());
                            }
                          });
                        this.store.dispatch(new FetchLeadStatusList());
                        this.store.dispatch(new FetchProjectTypes());
                      }
                    }
                    return next.handle(
                      request.clone({
                        setHeaders: {
                          ...(contentType && {
                            'Content-Type': contentType,
                          }),
                          Accept: 'application/json',
                          Authorization: `Bearer ${idToken}`,
                          tenant: this.subDomain,
                        },
                      })
                    );
                  }),
                  catchError((err: any) => {
                    // console.log('for this error - clearing localStorage', err);
                    localStorage.clear();
                    window.location.href = '/login';
                    return throwError(() => err);
                  })
                );
            }
          }
        } else if (err.url.includes('refresh') && err.status === 500) {
          localStorage.clear();
          window.location.href = '/login';
        } else if (err.status === 500) {
          this._notificationsService.error(
            'Internal server error. Please try again later.'
          );
        } else if (err.status === 404) {
          this._notificationsService.error('Requested resource not found.');
        }
        // else {
        //   this._notificationsService.error('An unexpected error occurred.');
        // }
        // } else if (err.status === 500 || err.status === 404) {
        //   // if (err.error.messages?.[0]) {
        //   //   this._notificationsService.error(err.error.messages?.[0]);
        //   // } else {
        //   this._notificationsService.error('Oops! Something went wrong');
        //   // }
        // }
        // if (err.status == 500 || err.status == 404 || err.status == 504) {
        //   const userDetails = JSON.parse(localStorage.getItem('userDetails') || '{}');
        //   const content = `Error Log Notification:

        //   Application Version: v${APP_VERSION},
        //   Tenant Name: ${getTenantName()},
        //   Environment: ${environment.envt},
        //   Module: '${window.location.pathname}',

        //   User Details: ${JSON.stringify(userDetails, null, 10)},

        //   Error Status: ${err.status},
        //   Exception: ${JSON.stringify(err?.error?.messages?.[0] || err?.error?.messages || err?.error || '', null, 10)}`;
        //   let payload: any = {
        //     Sender: '<EMAIL>',
        //     Subject: `WEB(${environment.envt}): API Error Alert v${APP_VERSION}`,
        //     ContentBody: content,
        //     ToRecipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        //   };
        //   this.store.dispatch(new SendEmailForm(payload));
        // }
        return throwError(() => err);
      })
    );
  }
}