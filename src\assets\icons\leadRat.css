@font-face {
  font-family: 'leadRat';
  src: url('leadRat.eot?rrfr3f');
  src: url('leadRat.eot?rrfr3f#iefix') format('embedded-opentype'),
    url('leadRat.ttf?rrfr3f') format('truetype'),
    url('leadRat.woff?rrfr3f') format('woff'),
    url('leadRat.svg?rrfr3f#leadRat') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="ic-"],
[class*=" ic-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'leadRat' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ic-delete:before {
  content: "\e900";
}

.ic-share:before {
  content: "\e901";
}

.ic-whatsapp:before {
  content: "\e902";
}

.ic-check:before {
  content: "\e903";
}

.ic-refine:before {
  content: "\e904";
}

.ic-tags:before {
  content: "\e905";
}

.ic-tick:before {
  content: "\e906";
}

.ic-cot:before {
  content: "\e907";
}

.ic-chevron-right:before {
  content: "\e908";
}

.ic-arrow-up:before {
  content: "\e909";
}

.ic-round-circles:before {
  content: "\e90a";
}

.ic-double-tick:before {
  content: "\e90b";
}

.ic-video:before {
  content: "\e90c";
}

.ic-folder-transfer:before {
  content: "\e90d";
}

.ic-plus:before {
  content: "\e90e";
}

.ic-update:before {
  content: "\e90f";
}

.ic-ios:before {
  content: "\e910";
}

.ic-playstore:before {
  content: "\e911";
}

.ic-close-secondary:before {
  content: "\e912";
}

.ic-curve-arrow:before {
  content: "\e913";
}

.ic-support:before {
  content: "\e914";
}

.ic-call-support:before {
  content: "\e915";
}

.ic-fusion-home:before {
  content: "\e916";
}

.ic-person:before {
  content: "\e917";
}

.ic-circle-exclamation-solid:before {
  content: "\e918";
}

.ic-chevron-down:before {
  content: "\e919";
}

.ic-arrow-down:before {
  content: "\e91a";
}

.ic-Call:before {
  content: "\e91b";
}

.ic-edit:before {
  content: "\e91c";
}

.ic-speaker-high:before {
  content: "\e91d";
}

.ic-search:before {
  content: "\e91e";
}

.ic-checked:before {
  content: "\e91f";
}

.ic-cancel:before {
  content: "\e920";
}

.ic-close:before {
  content: "\e921";
}

.ic-indeterminate:before {
  content: "\e922";
}

.ic-asterisk:before {
  content: "\e923";
}

.ic-whatsapp-support:before {
  content: "\e924";
}

.ic-utility:before {
  content: "\e925";
}

.ic-mail:before {
  content: "\e926";
}

.ic-bath-tub:before {
  content: "\e927";
}

.ic-triangle-up:before {
  content: "\e928";
}

.ic-triangle-down:before {
  content: "\e929";
}

.ic-unchecked:before {
  content: "\e92a";
}

.ic-eye-solid:before {
  content: "\e92b";
}

.ic-double-left:before {
  content: "\e92c";
}

.ic-double-right:before {
  content: "\e92d";
}

.ic-add:before {
  content: "\e92e";
}

.ic-calendar:before {
  content: "\e92f";
}

.ic-completed:before {
  content: "\e930";
}

.ic-find:before {
  content: "\e931";
}

.ic-reload:before {
  content: "\e932";
}

.ic-currency:before {
  content: "\e933";
}

.ic-appointment:before {
  content: "\e934";
}

.ic-filter:before {
  content: "\e935";
}

.ic-person-secondary:before {
  content: "\e936";
}

.ic-two-persons:before {
  content: "\e937";
}

.ic-refresh:before {
  content: "\e938";
}

.ic-file-clip:before {
  content: "\e939";
}

.ic-trash:before {
  content: "\e93a";
}

.ic-social-profile:before {
  content: "\e93b";
}

.ic-building-secondary:before {
  content: "\e93c";
}

.ic-cube:before {
  content: "\e93d";
}

.ic-expand:before {
  content: "\e93e";
}

.ic-collapse:before {
  content: "\e93f";
}

.ic-upload:before {
  content: "\e940";
}

.ic-user-secondary:before {
  content: "\e941";
}

.ic-key:before {
  content: "\e942";
}

.ic-handshake:before {
  content: "\e943";
}

.ic-escalate:before {
  content: "\e944";
}

.ic-star:before {
  content: "\e945";
}

.ic-fire:before {
  content: "\e946";
}

.ic-pencil:before {
  content: "\e947";
}

.ic-layout:before {
  content: "\e948";
}

.ic-copy-clipboard:before {
  content: "\e949";
}

.ic-thumbs-up:before {
  content: "\e94a";
}

.ic-eye:before {
  content: "\e94b";
}

.ic-logout:before {
  content: "\e94c";
}

.ic-camera:before {
  content: "\e94d";
}

.ic-down-arrow-secondary:before {
  content: "\e94e";
}

.ic-pen-solid:before {
  content: "\e94f";
}

.ic-home-secondary:before {
  content: "\e950";
}

.ic-leaf:before {
  content: "\e951";
}

.ic-download:before {
  content: "\e952";
}

.ic-connection:before {
  content: "\e953";
}

.ic-link:before {
  content: "\e954";
}

.ic-chevron-left:before {
  content: "\e955";
}

.ic-third-buliding:before {
  content: "\e956";
}

.ic-double-upload:before {
  content: "\e957";
}

.ic-wire:before {
  content: "\e958";
}

.ic-wrench:before {
  content: "\e959";
}

.ic-briefcase-solid:before {
  content: "\e95a";
}

.ic-paper-clip:before {
  content: "\e95b";
}

.ic-horn-solid:before {
  content: "\e95c";
}

.ic-envelope:before {
  content: "\e95d";
}

.ic-location-solid:before {
  content: "\e95e";
}

.ic-bed:before {
  content: "\e95f";
}

.ic-square-feet:before {
  content: "\e960";
}

.ic-arrow-left:before {
  content: "\e961";
}

.ic-arrow-right:before {
  content: "\e962";
}

.ic-handshake-solid:before {
  content: "\e963";
}

.ic-earth:before {
  content: "\e964";
}

.ic-globe:before {
  content: "\e965";
}

.ic-map-location:before {
  content: "\e966";
}

.ic-alert:before {
  content: "\e967";
}

.ic-clone:before {
  content: "\e968";
}

.ic-notes:before {
  content: "\e969";
}

.ic-message:before {
  content: "\e96a";
}

.ic-assets:before {
  content: "\e96b";
}

.ic-address-card:before {
  content: "\e96c";
}

.ic-convert:before {
  content: "\e96d";
}

.ic-circle-exclamation:before {
  content: "\e96e";
}

.ic-house-secondary:before {
  content: "\e96f";
}

.ic-wallet:before {
  content: "\e970";
}

.ic-world-time:before {
  content: "\e971";
}

.ic-converter:before {
  content: "\e972";
}

.ic-menu:before {
  content: "\e973";
}

.ic-diamond:before {
  content: "\e974";
}

.ic-compass:before {
  content: "\e975";
}

.ic-sofa:before {
  content: "\e976";
}

.ic-stairs:before {
  content: "\e977";
}

.ic-kitchen:before {
  content: "\e978";
}

.ic-balcony:before {
  content: "\e979";
}

.ic-sofa-lamp:before {
  content: "\e97a";
}

.ic-navigate:before {
  content: "\e97b";
}

.ic-rupee:before {
  content: "\e97c";
}

.ic-minus:before {
  content: "\e97d";
}

.ic-eye-slash:before {
  content: "\e97e";
}

.ic-phone:before {
  content: "\e97f";
}

.ic-rera:before {
  content: "\e980";
}

.ic-camera-solid:before {
  content: "\e981";
}

.ic-clock-rotate-left:before {
  content: "\e982";
}

.ic-corporation:before {
  content: "\e983";
}

.ic-cloud-upload:before {
  content: "\e984";
}

.ic-file:before {
  content: "\e985";
}

.ic-image:before {
  content: "\e986";
}

.ic-call-ring:before {
  content: "\e987";
}

.ic-filter-solid:before {
  content: "\e988";
}

.ic-cold:before {
  content: "\e989";
}

.ic-warm:before {
  content: "\e98a";
}

.ic-square-check:before {
  content: "\e98b";
}

.ic-square-minus:before {
  content: "\e98c";
}

.ic-square:before {
  content: "\e98d";
}

.ic-hamburger-menu:before {
  content: "\e98e";
}

.ic-setting-solid:before {
  content: "\e98f";
}

.ic-cancel-listing:before {
  content: "\e990";
}

.ic-tick-listing:before {
  content: "\e991";
}

.ic-assign-to:before {
  content: "\e992";
}

.ic-subscribe:before {
  content: "\e993";
}

.ic-unsubscribe:before {
  content: "\e994";
}

.ic-calendar-solid:before {
  content: "\e995";
}

.ic-secondary-clock:before {
  content: "\e996";
}

.ic-location-mark:before {
  content: "\e997";
}

.ic-triangle:before {
  content: "\e998";
}

.ic-engageto:before {
  content: "\e999";
}

.ic-buliding-secondary-cube:before {
  content: "\e99a";
}

.ic-clock-alert:before {
  content: "\e99b";
}

.ic-user-filter:before {
  content: "\e99c";
}

.ic-car-parking:before {
  content: "\e99d";
}

.ic-print:before {
  content: "\e99e";
}

.ic-qr-code:before {
  content: "\e99f";
}

.ic-tracker:before {
  content: "\e9a0";
}

.ic-clock-eight:before {
  content: "\e9a1";
}

.ic-clock-nine:before {
  content: "\e9a2";
}

.ic-folder-solid:before {
  content: "\e9a3";
}

.ic-notes-solid:before {
  content: "\e9a4";
}

.ic-clock-list:before {
  content: "\e9a5";
}

.ic-html-code:before {
  content: "\e9a6";
}

.ic-buliding:before {
  content: "\e9a7";
}

.ic-user-setting:before {
  content: "\e9a8";
}

.ic-venus-mars:before {
  content: "\e9a9";
}

.ic-dna-solid:before {
  content: "\e9aa";
}

.ic-user-tie:before {
  content: "\e9ab";
}

.ic-envelope-solid:before {
  content: "\e9ac";
}

.ic-location-map:before {
  content: "\e9ad";
}

.ic-circle-user:before {
  content: "\e9ae";
}

.ic-ivr-printer:before {
  content: "\e9af";
}

.ic-pen:before {
  content: "\e9b0";
}

.ic-suitcase-solid:before {
  content: "\e9b1";
}

.ic-phone-ring-solid:before {
  content: "\e9b2";
}

.ic-location-circle:before {
  content: "\e9b3";
}

.ic-circle-nodes:before {
  content: "\e9b4";
}

.ic-connection-solid:before {
  content: "\e9b5";
}

.ic-person-walking:before {
  content: "\e9b6";
}

.ic-apartment:before {
  content: "\e9b7";
}

.ic-alarm:before {
  content: "\e9b8";
}

.ic-message-lines:before {
  content: "\e9b9";
}

.ic-multi-location:before {
  content: "\e9ba";
}

.ic-location-dot:before {
  content: "\e9bb";
}

.ic-person-circle:before {
  content: "\e9bc";
}

.ic-alarm-solid:before {
  content: "\e9bd";
}

.ic-down-to-line:before {
  content: "\e9be";
}

.ic-mail-location:before {
  content: "\e9bf";
}

.ic-notification:before {
  content: "\e9c0";
}

.ic-secondary-filter-solid:before {
  content: "\e9c1";
}

.ic-circle-chevron-left:before {
  content: "\e9c2";
}

.ic-lines:before {
  content: "\e9c3";
}

.ic-flower:before {
  content: "\e9c4";
}

.ic-re-share:before {
  content: "\e9c5";
}

.ic-bed-secondary:before {
  content: "\e9c6";
}

.ic-home:before {
  content: "\e9c7";
}

.ic-couch:before {
  content: "\e9c8";
}

.ic-decrease-rupees:before {
  content: "\e9c9";
}

.ic-increase-rupees:before {
  content: "\e9ca";
}

.ic-rocket:before {
  content: "\e9cb";
}

.ic-notes-pen:before {
  content: "\e9cc";
}

.ic-notes-secondary:before {
  content: "\e9cd";
}

.ic-circle-user-secondary:before {
  content: "\e9ce";
}

.ic-user-data:before {
  content: "\e9cf";
}

.ic-calendar-minus:before {
  content: "\e9d0";
}

.ic-user-arrow:before {
  content: "\e9d1";
}

.ic-bank:before {
  content: "\e9d2";
}

.ic-swap:before {
  content: "\e9d3";
}

.ic-circle-rupee:before {
  content: "\e9d4";
}

.ic-sliders:before {
  content: "\e9d5";
}

.ic-circle-check:before {
  content: "\e9d6";
}

.ic-header:before {
  content: "\e9d7";
}

.ic-footer:before {
  content: "\e9d8";
}

.ic-turn-up-right:before {
  content: "\e9d9";
}

.ic-mobile:before {
  content: "\e9da";
}

.ic-book:before {
  content: "\e9db";
}

.ic-turn-up-left:before {
  content: "\e9dc";
}

.ic-desktop:before {
  content: "\e9dd";
}

.ic-transparency:before {
  content: "\e9de";
}

.ic-call-sms:before {
  content: "\e9df";
}

.ic-medal:before {
  content: "\e9e0";
}

.ic-three-person-solid:before {
  content: "\e9e1";
}

.ic-chart-pie:before {
  content: "\e9e2";
}

.ic-address-card-solid:before {
  content: "\e9e3";
}

.ic-house-solid:before {
  content: "\e9e4";
}

.ic-buliding-secondary-solid:before {
  content: "\e9e5";
}

.ic-calendar-tick:before {
  content: "\e9e6";
}

.ic-notepad-solid:before {
  content: "\e9e7";
}

.ic-percentage:before {
  content: "\e9e8";
}

.ic-search-house:before {
  content: "\e9e9";
}

.ic-certificate:before {
  content: "\e9ea";
}

.ic-ring:before {
  content: "\e9eb";
}

.ic-dollar:before {
  content: "\e9ec";
}

.ic-suitcase:before {
  content: "\e9ed";
}

.ic-hour-glass:before {
  content: "\e9ee";
}

.ic-turn-right:before {
  content: "\e9ef";
}

.ic-one-rotate:before {
  content: "\e9f0";
}

.ic-cash:before {
  content: "\e9f1";
}

.ic-three-persons-solid:before {
  content: "\e9f2";
}

.ic-circle:before {
  content: "\e9f3";
}

.ic-extend:before {
  content: "\e9f4";
}

.ic-upi:before {
  content: "\e9f5";
}

.ic-people:before {
  content: "\e9f6";
}

.ic-moon:before {
  content: "\e9f7";
}

.ic-hexagon-arrow:before {
  content: "\e9f8";
}

.ic-guard:before {
  content: "\e9f9";
}

.ic-ivr:before {
  content: "\e9fa";
}

.ic-search-solid:before {
  content: "\e9fb";
}

.ic-degree-camera:before {
  content: "\e9fc";
}

.ic-circuit-board:before {
  content: "\e9fd";
}

.ic-plug-circle:before {
  content: "\e9fe";
}

.ic-head-phone-solid:before {
  content: "\e9ff";
}

.ic-hexagon:before {
  content: "\ea00";
}

.ic-person-company:before {
  content: "\ea01";
}

.ic-monitor-phone:before {
  content: "\ea02";
}

.ic-bed-secondary-solid:before {
  content: "\ea03";
}

.ic-person-suitcase:before {
  content: "\ea04";
}

.ic-person-percantage:before {
  content: "\ea05";
}

.ic-bath-light:before {
  content: "\ea06";
}

.ic-chair:before {
  content: "\ea07";
}

.ic-stairs-solid:before {
  content: "\ea08";
}

.ic-molecules:before {
  content: "\ea09";
}

.ic-flag:before {
  content: "\ea0a";
}

.ic-restore-lock:before {
  content: "\ea0b";
}

.ic-setting-connections:before {
  content: "\ea0c";
}

.ic-circle-arrow:before {
  content: "\ea0d";
}

.ic-tower:before {
  content: "\ea0e";
}

.ic-community:before {
  content: "\ea0f";
}

.ic-serial-number:before {
  content: "\ea10";
}

.ic-split-arrows:before {
  content: "\ea11";
}

.ic-location-pin:before {
  content: "\ea12";
}

.ic-star-full:before {
  content: "\ea87";
}

.ic-right-left:before {
  content: "\ea89";
}

.ic-api-setting:before {
  content: "\ea13";
}

.ic-mail-server:before {
  content: "\ea14";
}

.ic-letter-b:before {
  content: "\ea15";
}

.ic-swipe-up:before {
  content: "\ea16";
}

.ic-briefcase:before {
  content: "\ea17";
}

.ic-call-forwarding:before {
  content: "\ea18";
}