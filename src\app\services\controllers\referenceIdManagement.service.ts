import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { buildHttpParams } from '../../core/utils/common.util';

@Injectable({
    providedIn: 'root',
})
export class ReferenceIdService {
    baseURL: string = `${env.baseURL}api/v1/propertyreferenceinfo`;

    constructor(private http: HttpClient) { }

    fetchAllReferenceIds(payload: any) {
        const params = buildHttpParams(payload);
        return this.http.get(`${this.baseURL}?${params.toString()}`);
    }

    fetchAllReferenceIdsList() {
        return this.http.get(`${this.baseURL}/refrenceids`);
    }

    fetchAllReferenceIdsCounts(payload: any) {
        const params = buildHttpParams(payload);
        return this.http.get(`${this.baseURL}/count?${params.toString()}`);
    }

    addReferenceId(payload: any) {
        return this.http.post(`${this.baseURL}`, payload);
    }

    updateReferenceId(payload: any) {
        return this.http.put(`${this.baseURL}`, payload);
    }

    deleteReferenceId(referenceId: string) {
        return this.http.delete(`${this.baseURL}?id=${referenceId}`);
    }

    uploadExcel(selectedFile: File) {
        let formData = new FormData();
        formData.append('file', selectedFile);
        return this.http.post(`${this.baseURL}/excel`, formData);
    }

    uploadMappedColumns(payload: any) {
        return this.http.post(`${this.baseURL}/bulkupload`, payload);
    }

    getExcelUploadedList(pageNumber: number, pageSize: number) {
        return this.http.get(`${this.baseURL}/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`);
    }

    fetchAllListingSources() {
        return this.http.get(`${this.baseURL}/listing-source`)
    }

    doesRefIdExist(refId: string, sourceId: string) {
        return this.http.get(`${this.baseURL}/referenceid?ReferenceId=${refId}&ListingSourceId=${sourceId}`);
    }

    refBulkDelete(id: string) {
        const ids = {
            body: {
                ids: id,
            },
        };
        return this.http.delete(`${this.baseURL}/bulkdelete`, ids);
    }
}
