<div class="mt-4 justify-between">
  <ng-container *ngIf="!params.data.isArchived">
    <div title="Edit" *ngIf="currentPath === '/invoice' ? canEditInvoice : canEditLead"
      class="bg-accent-green icon-badge" (click)="editLead(params.data)">
      <span class="icon ic-pen m-auto ic-xxs" id="clkEditLead" data-automate-id="clkEditLead"></span>
    </div>
    <div title="Notes" *ngIf="currentPath === '/invoice' ? canEditInvoice : canEditNotes"
      class="bg-green-100 icon-badge" id="clkNotesLead" data-automate-id="clkNotesLead" (click)="openNotesModal()"><span
        class="icon ic-notes m-auto ic-xxs"></span></div>
    <div title="Lead History" class="bg-brown icon-badge" id="clkHistoryLead" data-automate-id="clkHistoryLead"
      (click)="openLeadHistory()"><span class="icon ic-clock-rotate-left m-auto ic-xxs"></span></div>
    <div title="Lead Document" *ngIf="currentPath === '/invoice' ? canEditInvoice : canEditDocument"
      class="bg-blue-150 icon-badge" id="clkUplDocLead" data-automate-id="clkUplDocLead"
      (click)="openDocumentUploadModal()"><span class="icon ic-upload m-auto ic-xxs"></span></div>
    <ng-container
      *ngIf="params.data.links?.length && (params.data.links?.[0]?.type == 'PropertyFinder' || params.data.links?.[0]?.type == 'Bayut' || params.data.links?.[0]?.type == 'Dubizzle')">
      <div title="Navigate to Link" *ngIf="currentPath === '/invoice' ? canEditInvoice : canEditLead"
        class="bg-violet icon-badge position-relative" (click)="navigateToLink()">
        <span class="icon ic-link m-auto ic-xxs"></span>
        <span *ngIf="params.data.links?.[0]?.clickedCount"
          class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6">
          {{params.data.links?.[0]?.clickedCount >= 100 ? '99+' : params.data.links?.[0]?.clickedCount}}</span>
      </div>
    </ng-container>
    <ng-container *ngIf="currentPath === '/invoice' || canCommunicate">
      <div class="d-none ph-d-block">
        <div
          [title]="leadsCommunication?.[params?.data?.id]?.SMS > 0 ? 'SMS: ' + leadsCommunication?.[params?.data?.id]?.SMS : 'SMS'"
          [ngClass]="{ 'grid-blur cursor-default' : params.data.assignTo == EMPTY_GUID}"
          class="bg-violet icon-badge position-relative" id="clkSMS" data-automate-id="clkSMS"
          (click)="openTemplateModal($event, 'SMS')">
          <span class="icon ic-message-lines m-auto ic-xxs"></span>
          <span
            *ngIf="(leadsCommunication?.[params?.data?.id]?.SMS || isLeadsCommunicationLoading) && showCommunicationCount"
            class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6"
            [ngClass]="{'blinking': isLeadsCommunicationLoading}">
            {{leadsCommunication?.[params?.data?.id]?.SMS >= 100 ? '99+' : (!isLeadsCommunicationLoading ?
            leadsCommunication?.[params?.data?.id]?.SMS : null)}}</span>
        </div>
      </div>
      <div
        [title]="leadsCommunication?.[params?.data?.id]?.Email > 0 ? 'Email: ' + leadsCommunication?.[params?.data?.id]?.Email : 'Email'"
        [ngClass]="{ 'grid-blur cursor-default' : params.data.assignTo == EMPTY_GUID}"
        class="bg-dark-blue icon-badge position-relative" id="clkMailLead" data-automate-id="clkMailLead"
        (click)="openTemplateModal($event, 'Email')">
        <span class="icon ic-mail m-auto ic-xxs"></span><span
          *ngIf="(leadsCommunication?.[params?.data?.id]?.Email || isLeadsCommunicationLoading) && showCommunicationCount"
          class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6"
          [ngClass]="{'blinking': isLeadsCommunicationLoading}">
          {{leadsCommunication?.[params?.data?.id]?.Email >= 100 ? '99+' : (!isLeadsCommunicationLoading ?
          leadsCommunication?.[params?.data?.id]?.Email : null)}}</span>
      </div>
      <div *ngIf="!whatsAppComp"
        [title]="leadsCommunication?.[params?.data?.id]?.WhatsApp > 0 ? 'WhatsApp: ' + leadsCommunication?.[params?.data?.id]?.WhatsApp : 'WhatsApp'"
        [ngClass]="{ 'grid-blur cursor-default' : params.data.assignTo == EMPTY_GUID}"
        class="bg-accent-green-30 icon-badge position-relative" id="clkWhatsappLead" data-automate-id="clkWhatsappLead"
        (click)="openWhatsapp($event, whatsappType, 'WhatsApp')" target="_blank">
        <span class="icon ic-whatsapp m-auto ic-xxs"></span><span
          *ngIf="(leadsCommunication?.[params?.data?.id]?.WhatsApp || isLeadsCommunicationLoading) && showCommunicationCount"
          class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6"
          [ngClass]="{'blinking': isLeadsCommunicationLoading}">
          {{leadsCommunication?.[params?.data?.id]?.WhatsApp >= 100 ? '99+' : (!isLeadsCommunicationLoading ?
          leadsCommunication?.[params?.data?.id]?.WhatsApp : null)}}</span>
      </div>
      <div
        [title]="leadsCommunication?.[params?.data?.id]?.Call > 0 ? 'Call: ' + leadsCommunication?.[params?.data?.id]?.Call : 'Call'"
        [ngClass]="{ 'grid-blur cursor-default' : params.data.assignTo == EMPTY_GUID}"
        class="bg-accent-blue icon-badge position-relative"
        (click)="checkToCall($event, virtualNo, askBefore, contactAdmin, chooseToCallType, params?.data?.id)">
        <span class="icon ic-Call m-auto ic-xxs"></span>
        <span *ngIf="(leadsCommunication?.[params?.data?.id]?.Call || isLeadsCommunicationLoading)  && showCommunicationCount"
          class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6"
          [ngClass]="{'blinking': isLeadsCommunicationLoading}">{{leadsCommunication?.[params?.data?.id]?.Call
          >= 100 ? '99+' : (!isLeadsCommunicationLoading ? leadsCommunication?.[params?.data?.id]?.Call : null)}}</span>
      </div>
      <div title="Call Recording" *ngIf="params?.data?.callRecordingUrls || filteredCallLogs?.length"
        class="bg-dark-orange icon-badge" id="clkAudio" data-automate-id="clkAudio"
        (click)="openAudioPlayer($event, audioOption)"
        [ngClass]="{ 'grid-blur cursor-default' : params.data.assignTo == EMPTY_GUID}">
        <span class="icon ic-speaker-high m-auto ic-xxs"></span>
      </div>
    </ng-container>
    <div title="Delete"
      *ngIf="(currentPath === '/invoice' ? canDeleteInvoice : params?.data?.status?.actionName !== 'Invoiced' && canDeleteLead) && !whatsAppComp"
      class="bg-light-red icon-badge" (click)="deleteLead($event, params.data)"><span
        class="icon ic-delete m-auto ic-xxs" id="clkDeleteLead" data-automate-id="clkDeleteLead"></span></div>
  </ng-container>
  <div *ngIf="params.data.isArchived" class="flex-center w-100">
    <div title="Restore" class="bg-blue-450 icon-badge" (click)="restoreLead($event, params.data)"><span
        class="icon ic-update m-auto ic-xxs" id="clkRestoreLead" data-automate-id="clkEditLead"></span></div>
    <div title="Lead History" class="bg-brown icon-badge" id="clkHistoryLead" data-automate-id="clkHistoryLead"
      (click)="openLeadHistory()"><span class="icon ic-clock-rotate-left m-auto ic-xxs"></span></div>
    <div *ngIf="currentPath !== '/invoice' && canPermanentDelete" title="Permanent Delete"
      class="bg-light-red icon-badge" id="clkPermanentDeleteLead" data-automate-id="clkPermanentDeleteLead"
      (click)="permanentDelete($event, params.data)"><span class="icon ic-delete m-auto ic-xxs"></span></div>
  </div>
</div>

<ng-template #virtualNo>
  <a class="ic-close-secondary ic-close-modal ip-ic-close-modal" (click)="closeModal()"></a>
  <div class="p-16 fw-600 flex-center-col mt-20">
    <div>Select virtual Number</div>
    <div class="w-100 mt-8">
      <ng-select [virtualScroll]="true" placeholder="Select virtual Number" name="virtualNo" [items]="virtualNosList"
        ResizableDropdown [(ngModel)]="selectedVirtualNo">
      </ng-select>
    </div>
    <div class="flex-center mt-20">
      <button class="btn-gray" id="btnVirtualCancel" data-automate-id="btnVirtualCancel" (click)="closeModal()">
        {{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal ml-8" (click)="ivrClickToCheck(selectedVirtualNo, chooseToCallType)">
        {{ 'BUTTONS.connect' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #audioOption>
  <h3 class="bg-coal px-24 py-12 text-white fw-semi-bold">
    {{ 'LEADS.lead-call-recordings' | translate }}
  </h3>
  <div class="pr-12 scrollbar h-100">
    <ng-container *ngFor="let section of fieldSections; let i = index; let last = last">
      <div [ngClass]="{ 'pb-12': !last }" [id]="section?.displayName">
        <div class="bg-white">
          <div class="cursor-pointer align-center py-12 px-20" [ngClass]="!section.isHide ? 'border-bottom' : ''"
            (click)="section.isHide = !section.isHide">
            <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
              [ngClass]="{ 'rotate-270': section.isHide }"></span>
            <h5 class="text-black-200 fw-700 align-center gap-1">
              {{ section?.displayName || '' }} <span *ngIf="filteredCallLogs?.length"> - {{
                filteredCallLogs?.length || 0 }} </span>
            </h5>
          </div>
          <div *ngIf="!section.isHide">
            <ng-container [ngSwitch]="section?.displayName">
              <ng-container *ngSwitchCase="'Call Recording(s)'">
                <div class="px-10">
                  <ng-container *ngFor="let year of callRecordingDetails">
                    <ng-container *ngFor="let month of year.yearData">
                      <div class="align-center w-100 mt-20">
                        <h5 class="text-accent-green fw-600">
                          {{moment(month?.months).format('MMM')}} {{year?.years}}
                        </h5>
                        <div class="flex-grow-1 border-bottom ml-8"></div>
                      </div>
                      <ng-container *ngFor="let audio of month?.monthData">
                        <div class="bg-slate-150 br-10 mb-20">
                          <h5 class="mt-20 bg-black-100 px-20 py-10 br-10 text-white fw-semi-bold">
                            {{ getTimeZoneDate(audio?.callStartTime, userData?.timeZoneInfo?.baseUTcOffset) }}
                          </h5>
                          <div class="position-relative">
                            <audio preload="auto" #audioPlayer controls (play)="pauseOtherAudio(audioPlayer)"
                              (canplay)="isLoading = false" (loadedmetadata)="isLoading = false">
                              <source [src]='audio?.callRecordingUrl' type="audio/mp3">
                            </audio>
                            <div *ngIf="isLoading">
                              <ng-lottie [options]="loader" width="30px" height="30px"
                                class="position-absolute top-10 left-6">
                              </ng-lottie>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'IVR Recording(s)'">
                <div class="px-10">
                  <ng-container *ngFor="let year of ivrRecordingDetails">
                    <ng-container *ngFor="let month of year.yearData">
                      <div class="align-center w-100 mt-20">
                        <h5 class="text-accent-green fw-600">
                          {{moment(month?.months).format('MMM')}} {{year?.years}}
                        </h5>
                        <div class="flex-grow-1 border-bottom ml-8"></div>
                      </div>
                      <ng-container *ngFor="let audio of month.monthData">
                        <div class="bg-slate-150 br-10 mb-20">
                          <h5 class="mt-20 bg-black-100 px-20 py-10 br-10 text-white fw-semi-bold">
                            {{ getTimeZoneDate(audio?.date, userData?.timeZoneInfo?.baseUTcOffset)}}
                          </h5>
                          <div class="position-relative">
                            <audio preload="auto" #audioPlayer controls (play)="pauseOtherAudio(audioPlayer)"
                              (canplay)="isLoading = false" (loadedmetadata)="isLoading = false">
                              <source [src]='audio?.audioUrl ? decodeAudioUrl(audio.audioUrl) : null' type="audio/mp3">
                            </audio>
                            <div *ngIf="isLoading">
                              <ng-lottie [options]="loader" width="30px" height="30px"
                                class="position-absolute top-10 left-6">
                              </ng-lottie>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #askBefore>
  <div class="p-20 flex-center-col">
    <h3 class="text-black-100 fw-semi-bold mb-10">choose which type you want to call?</h3>
    <div class="flex-center fw-600 py-20">
      <div class="flex-center-col">
        <img src="../../../../assets/images/integration/ivr-logo.svg" alt="" class="mb-8 cursor-pointer" width="40"
          height="40" (click)="openIVROnly(virtualNo, contactAdmin, chooseToCallType)">
        <div>IVR</div>
      </div>
      <div class="flex-center-col ml-20">
        <div (click)="initCall()" class="br-6 mb-8 bg-accent-blue cursor-pointer">
          <span class="icon ic-Call ic-large m-10"></span>
        </div>
        <div>Dialer Pad</div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #whatsappType>
  <div class="p-20 flex-center-col">
    <h3 class="text-black-100 fw-semi-bold mb-10">Select your whatsapp type</h3>
    <div class="flex-center fw-600 py-20">
      <div class="flex-center-col">
        <img src="../../../../assets/images/personal-whatsapp.svg" alt="" class="mb-8 cursor-pointer" width="40"
          height="40" (click)="openPersonalWhatsapp('WhatsApp')">
        <div>Personal</div>
      </div>
      <div class="flex-center-col ml-20">
        <img src="../../../../assets/images/integration-whatsapp.svg" alt="" class="mb-8 cursor-pointer" width="40"
          height="40" (click)="openIntegratedWhatsapp('WhatsApp')">
        <div>Integrated</div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #contactAdmin>
  <div class="p-16 flex-center-col">
    <h4 class="text-gray fw-600 mt-20 mb-30 text-center">{{ contactAdminType }}</h4>
    <button class="btn-coal" (click)="modalRef.hide()">Ok, Got it</button>
  </div>
</ng-template>

<ng-template #chooseToCallType>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-10">choose which number you want to call?</h3>
    <div class="align-center">
      <div class="form-check form-check-inline" (click)="selectedCallType = params.data?.contactNo">
        <input type="radio" id="inpPhoneNumber" data-automate-id="inpPhoneNumber" class="radio-check-input"
          name="callType" checked>
        <label class="fw-600 text-secondary cursor-pointer text-large" for="inpPhoneNumber">
          Primary Number
        </label>
      </div>
    </div>
    <div class="align-center">
      <div class="form-check form-check-inline" (click)="selectedCallType = params.data?.alternateContactNo">
        <input type="radio" id="inpAlternateNumber" data-automate-id="inpAlternateNumber" class="radio-check-input"
          name="callType">
        <label class="fw-600 text-secondary cursor-pointer text-large" for="inpAlternateNumber">
          {{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}
        </label>
      </div>
    </div>
    <div class="flex-end mt-30">
      <button class="btn-gray" id="clkCallNumberCancel" data-automate-id="clkCallNumberCancel"
        (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-green ml-20" id="clkCallNumberConfirm" data-automate-id="clkCallNumberConfirm"
        (click)="ivrClickToCall(VNAssignment?.virtualNumber || selectedVirtualNo, true)">
        {{ 'DASHBOARD.call' | translate }}</button>
    </div>
  </div>
</ng-template>
<!-- VNAssignment?.virtualNumber || selectedVirtualNo -->

<ng-template #noEmailModal>
  <div class="p-20">
    <h4 class="text-black-100 fw-semi-bold mb-20 text-center word-break line-break">There is no email ID associated with
      this lead. Kindly update it before proceeding.</h4>
    <div class="flex-end mt-30">
      <div class="btn-gray mr-20" (click)="modalRef.hide()">
        {{ 'BUTTONS.cancel' | translate }}</div>
      <div class="btn-coal" id="deleteYes" data-automate-id="deleteYes"
        (click)="editLead(params.data); modalRef.hide()">
        Update</div>
    </div>
  </div>
</ng-template>

<ng-template #mobileNotification>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-10">we have sent call notification to mobile
    </h3>
    <div class="flex-end mt-30">
      <button class="btn-gray" id="clkCallNumberCancel" data-automate-id="clkCallNumberCancel"
        (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-green w-150 ml-20" id="clkCallNumberConfirm" data-automate-id="clkCallNumberConfirm"
        (click)="openDialerPad()">
        Choose Another App</button>
    </div>
  </div>
</ng-template>