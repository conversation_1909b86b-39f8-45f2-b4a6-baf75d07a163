import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { BaseService } from './shared/base.service';

@Injectable({
  providedIn: 'root'
})
export class FilterService extends BaseService<any> {
  serviceBaseUrl: string;

  getResourceUrl(): string {
    return 'filter';
  }

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  saveFilter(data: any) {
    return this.http.post(`${this.serviceBaseUrl}`, data);
  }

  updateFilter(resource: any): Observable<Object> {
    return this.http.put(`${this.serviceBaseUrl}`, resource);
  }

  getFilter(data: any) {
    return this.http.get(`${this.serviceBaseUrl}?Module=${data}`);
  }

  deleteFilter(data: any) {
    return this.http.delete(`${this.serviceBaseUrl}/${data}`);
  }

  filterExists(filter: any, module: any) {
    return this.http.get(`${this.serviceBaseUrl}/filter-exist?filter=${filter}&module=${module}`);
  }
}
