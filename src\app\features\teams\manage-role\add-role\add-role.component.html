<div>
    <form [formGroup]="addRoleForm" autocomplete="off">
        <h3 class="bg-coal px-24 py-12 text-white fw-700">
            {{ (roleId ? 'USER.edit-role' : 'USER.add-new-role') | translate }}</h3>
        <div class="pl-16 position-relative">
            <div class="pr-16">
                <div class="field-label-req">{{ 'ROLE.role-name' | translate }}</div>
                <form-errors-wrapper [control]="addRoleForm.controls['name']" label="{{'ROLE.role-name' | translate}}">
                    <input type="text" required id="inpRoleName" data-automate-id="inpRoleName"
                        placeholder="ex. Manager" formControlName="name" [readonly]="isReadOnlyRole()">
                </form-errors-wrapper>
            </div>
            <div class="position-relative">
                <div class="field-label-req">{{ 'ROLE.select-permissions' | translate }}</div>
                <div class="error-message-custom bottom-0" *ngIf="!isActionSelected">
                    {{ 'PROPERTY.select-permission' | translate }}
                </div>
            </div>
            <!---<label class="checkbox-container text-accent-green fw-600 mt-8">
            <input type="checkbox" id="inpSearch" data-automate-id="inpSearch">
            <span class="checkmark"></span>Search <span class="text-xs fw-400">(can search across full
                application)</span>
        </label>-->
            <div>
                <div class="scrollbar h-100-250 mt-20">
                    <div *ngFor="let permission of getSortedPermissionsList()" class="mb-24">
                        <div class="align-center w-100">
                            <label class="checkbox-container text-accent-green fw-600">
                                <input type="checkbox" id="checkBoxModule" data-automate-id="checkBoxModule"
                                    [checked]="allActionSelectionCheck(permission)"
                                    (change)="checkAllAction(permission, $event.target.checked)">
                                <span class="checkmark"></span>{{ renamePermission(permission[0]) }}
                            </label>
                            <div class="d-flex flex-grow-1 border mx-10"></div>
                        </div>
                        <div class="flex-wrap align-center w-100 pl-8 py-12">
                            <ng-container *ngFor="let action of sortActions(permission[1])">
                                <label class="checkbox-container text-mud w-50 mt-10 pl-30"
                                    [ngClass]="{'pe-none disabled': action === 'CreateDuplicateLeads' && (!duplicateFeatureInfo?.allowAllDuplicates || !duplicateFeatureInfo?.isFeatureAdded)}">
                                    <input type="checkbox" id="checkBoxAction" data-automate-id="checkBoxAction"
                                        [checked]="actionSelectedCheck(permission[0], action)"
                                        (change)="checkSingleActionAction(permission[0],action,$event.target.checked)">
                                    <span class="checkmark"></span>{{convertPascalToNormal(action)}}
                                </label>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="modal-footer flex-center pb-0" *ngIf="roleName !== 'Admin'">
        <div class="btn-gray" id="btnClearRole" data-automate-id="btnClearRole" (click)="selectedPermissionsList = []">
            {{ 'BUTTONS.clear' | translate }}</div>
        <div class="btn-coal ml-20" id="btnAddRole" data-automate-id="btnAddRole" (click)="addRole()">Save</div>
    </div>
</div>