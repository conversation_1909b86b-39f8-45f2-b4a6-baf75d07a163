import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getIndexes } from 'src/app/core/utils/common.util';
import { ExportAttendance } from 'src/app/reducers/attendance/attendance.actions';
import {
  ExportDataActivity,
  ExportDataCall,
  ExportDataProjectStatus,
  ExportDataSourceStatus,
  ExportDataSubSourceStatus,
  ExportDataUserStatus,
} from 'src/app/reducers/data-reports/data-reports.action';
import { ExportData } from 'src/app/reducers/data/data-management.actions';
import { ExportLeads } from 'src/app/reducers/lead/lead.actions';
import { ListingExport } from 'src/app/reducers/listing-site/listing-site.actions';
import {
  ExportAgency,
  ExportCampaign,
  ExportChannelPartner,
} from 'src/app/reducers/manage-marketing/marketing.action';
import { ExportProject } from 'src/app/reducers/project/project.action';
import { ExportProperty } from 'src/app/reducers/property/property.actions';
import {
  ExportActivity,
  ExportAgencyStatus,
  ExportCallStatus,
  ExportCityReport,
  ExportMeetingSiteVisitStatus,
  ExportProjectStatus,
  ExportProjectSubstatus,
  ExportReceivedDateStatus,
  ExportSourceStatus,
  ExportSubReport,
  ExportSubSourceStatus,
  ExportSubStatus,
  ExportUserMeetingSiteReport,
  ExportUserSourceReport,
  ExportUserStatus,
  ExportUserSubSourceReport,
} from 'src/app/reducers/reports/reports.actions';
import { ExportTeams, ExportUsers } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'export-mail',
  templateUrl: './export-mail.component.html',
})
export class ExportMailComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  payload: any;
  notesType: FormControl = new FormControl(false);
  notesCount: FormControl = new FormControl(3, Validators.required);
  notesTypeOptions = [
    { value: false, label: 'Exclude Notes' },
    { value: true, label: 'Include Notes' },
  ];
  facebookOptionType: FormControl = new FormControl(false);
  facebookOptions = [
    { value: true, label: 'Include Facebook' },
  ];
  userData: any;
  currentDate: Date = new Date();
  getSystemTimeOffset: any;
  constructor(private modalRef: BsModalRef, private store: Store<AppState>) { }

  ngOnInit() {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
  }

  getExportLabel(path: string): string {
    const labels: { [key: string]: string } = {
      'lead/new/all': 'lead(s)',
      prospect: 'data',
      'prospect/custom-filters': 'data',
      'Property/new/all': 'property(s)',
      project: 'project(s)',
      attendance: 'attendance',
      'team/id': 'member(s)',
      agency: 'agency(s)',
      'user/getallusers': 'user(s)',
      channelpartner: 'channel partner(s)',
      campaign: 'campaign(s)',
      Listing: 'listing property(s)',
    };
    return labels[path] || 'reports';
  }

  exportData() {
    let exportPayload: any = {
      ...this.payload,
      FileName: this.formatDateTime(),
    };
    if (exportPayload?.IsWithTeam) {
      exportPayload.IsWithTeam = true;
    }
    delete exportPayload.pageNumber;
    delete exportPayload.pageSize;

    delete exportPayload.PageNumber;
    delete exportPayload.PageSize;

    Object.entries(exportPayload).forEach(([key, value]) => {
      if (Array.isArray(value) && value[0] !== undefined) {
        const updatedArray = [getIndexes(key, value)];
        exportPayload[key] = updatedArray.flat();
      }
    });
    switch (exportPayload.path) {
      case 'lead/new/all':
        exportPayload = {
          ...exportPayload,
          AgencyNames:
            typeof this.payload?.AgencyNames === 'string'
              ? [this.payload?.AgencyNames]
              : this.payload?.AgencyNames,
          StatusIds:
            typeof this.payload?.StatusIds === 'string'
              ? [this.payload?.StatusIds]
              : this.payload?.StatusIds,
          assignTo:
            typeof this.payload?.assignTo === 'string'
              ? [this.payload?.assignTo]
              : this.payload?.assignTo,
          Projects:
            typeof this.payload?.Projects === 'string'
              ? [this.payload?.Projects]
              : this.payload?.Projects,
          SubSources:
            typeof this.payload?.SubSources === 'string'
              ? [this.payload?.SubSources]
              : this.payload?.SubSources,
          IsWithNotes: this.notesType.value,
          ...(this.notesType.value === true && {
            notesCount: this.notesCount.value,
          }),
          IsWithFacebookProperties: this.facebookOptionType.value
        };
        this.store.dispatch(new ExportLeads(exportPayload));
        break;
      case 'prospect/custom-filters':
      case 'prospect':
        exportPayload = {
          ...exportPayload,
          SecondLevelFilter: !exportPayload?.SecondLevelFilter
            ? 0
            : exportPayload?.SecondLevelFilter,
          IsWithNotes: this.notesType.value,
          ...(this.notesType.value === true && {
            notesCount: this.notesCount.value,
          }),
        };
        this.store.dispatch(new ExportData(exportPayload));
        break;
      case "project":
        exportPayload = {
          ...exportPayload,
          ProjectType: ['All', 'Residential', 'Commercial', 'Agricultural']?.indexOf(this.payload?.ProjectType),
        }
        this.store.dispatch(new ExportProject(exportPayload));
        break;
      case 'Property/new/all':
        this.store.dispatch(new ExportProperty(exportPayload));
        break;
      case 'attendance':
        this.store.dispatch(new ExportAttendance(exportPayload));
        break;
      case 'report/user/new/status':
        this.store.dispatch(new ExportUserStatus(exportPayload));
        break;
      case 'report/project/status/new':
        this.store.dispatch(new ExportProjectStatus(exportPayload));
        break;
      case 'report/source/status/new':
        this.store.dispatch(new ExportSourceStatus(exportPayload));
        break;
      case 'report/subsource/status/new':
        this.store.dispatch(new ExportSubSourceStatus(exportPayload));
        break;
      case 'report/agency/status/new':
        this.store.dispatch(new ExportAgencyStatus(exportPayload));
        break;
      case 'report/user/meetingandvisit/level1':
        this.store.dispatch(new ExportMeetingSiteVisitStatus(exportPayload));
        break;
      case 'report/activity/level9':
        this.store.dispatch(new ExportActivity(exportPayload));
        break;
      case 'datareport/activity':
        this.store.dispatch(new ExportDataActivity(exportPayload));
        break;
      case 'report/substatus/bysubsource/updated':
        this.store.dispatch(new ExportSubReport(exportPayload));
        break;
      case 'report/substatus':
        this.store.dispatch(new ExportSubStatus(exportPayload));
        break;
      case 'report/user/call-log/new':
        this.store.dispatch(new ExportCallStatus(exportPayload));
        break;
      case 'report/project/bysubstatus/updated':
        this.store.dispatch(new ExportProjectSubstatus(exportPayload));
        break;
      case 'report/datewisesource':
        this.store.dispatch(new ExportReceivedDateStatus(exportPayload));
        break;
      case 'datareport/user/status':
        this.store.dispatch(new ExportDataUserStatus(exportPayload));
        break;
      case 'datareport/project/status':
        this.store.dispatch(new ExportDataProjectStatus(exportPayload));
        break;
      case 'datareport/source/status':
        this.store.dispatch(new ExportDataSourceStatus(exportPayload));
        break;
      case 'datareport/subsource/status':
        this.store.dispatch(new ExportDataSubSourceStatus(exportPayload));
        break;
      case 'datareport/user/data-call-log':
        this.store.dispatch(new ExportDataCall(exportPayload));
        break;
      case 'datareport/activity/communication':
        this.store.dispatch(new ExportDataActivity(exportPayload));
        break;
      case 'report/uservssource':
        this.store.dispatch(new ExportUserSourceReport(exportPayload));
        break;
      case 'report/uservssubsource':
        this.store.dispatch(new ExportUserSubSourceReport(exportPayload));
        break;
      case 'user/getallusers':
        this.store.dispatch(new ExportUsers(exportPayload));
        break;
      case 'report/user/meetingandvisit/new/level':
        this.store.dispatch(new ExportUserMeetingSiteReport(exportPayload));
        break;
      case 'agency':
        this.store.dispatch(new ExportAgency(exportPayload));
        break;
      case 'channelpartner':
        this.store.dispatch(new ExportChannelPartner(exportPayload));
        break;
      case 'campaign':
        this.store.dispatch(new ExportCampaign(exportPayload));
        break;
      case 'team/id':
        this.store.dispatch(new ExportTeams(exportPayload));
        break;
      case 'report/cityvslead':
        this.store.dispatch(new ExportCityReport(exportPayload));
        break;
      case 'Listing':
        this.store.dispatch(new ListingExport(exportPayload));
    }
    this.modalRef.hide();
  }
  // utc format
  // formatDateTime() {
  //   const now = new Date();
  //   const padToTwoDigits = (num: number) => num.toString().padStart(2, '0');

  //   const day = padToTwoDigits(now.getUTCDate());
  //   const month = padToTwoDigits(now.getUTCMonth() + 1);
  //   const year = now.getUTCFullYear().toString().slice(-2);
  //   const hours = padToTwoDigits(now.getUTCHours());
  //   const minutes = padToTwoDigits(now.getUTCMinutes());
  //   const seconds = padToTwoDigits(now.getUTCSeconds());
  //   return `${day}-${month}-${year}_${hours}:${minutes}:${seconds}`;
  // }

  formatDateTime() {
    const now = new Date(this.currentDate);

    const padToTwoDigits = (num: any) => num.toString().padStart(2, '0');

    const day = padToTwoDigits(now.getDate());
    const month = padToTwoDigits(now.getMonth() + 1);
    const year = now.getFullYear().toString().slice(-2);
    const hours = padToTwoDigits(now.getHours());
    const minutes = padToTwoDigits(now.getMinutes());
    const seconds = padToTwoDigits(now.getSeconds());

    return `${day}-${month}-${year}_${hours}:${minutes}:${seconds}`;
  }

  closePopup() {
    this.modalRef.hide();
  }
}
