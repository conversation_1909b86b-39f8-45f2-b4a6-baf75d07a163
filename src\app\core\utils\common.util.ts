import { HttpParams } from '@angular/common/http';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import * as moment from 'moment';

import {
  EMPTY_GUID,
  PROPERTY_BUDGET_FILTER,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import {
  BHKType,
  DateRange,
  DoneStatus,
  EnquiryType,
  Facing,
  FurnishStatus,
  Gender,
  LeadSource,
  MaritalStatusType,
  OfferType,
  PossessionType,
  PropertyPriceFilter,
  PropertyStatus,
  PurposeType,
  SaleType,
} from 'src/app/app.enum';
import { MasterAreaUnitType } from 'src/app/core/interfaces/master-data.interface';
import { environment as env } from 'src/environments/environment';

export const getEnvDetails = (isDomain: boolean = false): string => {
  let extension: string = '';
  let defaultDomain: string = '';
  switch (env.envt) {
    case 'dev':
      extension = '.leadratd.com';
      defaultDomain = 'alpha';
      break;
    case 'qa':
      extension = '.leadrat.info';
      defaultDomain = 'vibranium';
      break;
    case 'uat':
      // extension = '.leadrat.app';
      extension = '.dhinwa.com';
      defaultDomain = 'uat';
      break;
    case 'prod':
      extension = '.leadrat.com';
      defaultDomain = 'prdblack';
      break;
  }
  return isDomain ? defaultDomain : extension;
};

export const getTenantName = (): string => {
  let subDomain: string = '';
  if (location.href.split('.').length > 1) {
    if (location.href.split('.')[0].includes('www')) {
      subDomain = location.href.split('.')[1];
    } else {
      subDomain = location.href.split('.')[0].split('//')[1];
    }
  }
  return (
    subDomain || localStorage.getItem('subDomain') || getEnvDetails(true) || ''
  );
};

export const getAppName = () => {
  let appName = '';
  switch (env.envt) {
    case 'dev':
      appName = 'leadrat';
      break;
    case 'qa':
      appName = 'leadrat';
      break;
    case 'uat':
      appName = 'dhinwa';
      break;
    case 'prod':
      appName = 'leadrat';
      break;
  }
  return appName;
};

export const getAppImages = () => {
  let subDomain = getTenantName();

  switch (true) {
    // case env.envt == 'dev' && subDomain == 'carbon':
    //   return {
    //     appFavIcon: 'assets/images/favicon.ico',
    //     appLogo: 'assets/images/app-logo-hj.svg',
    //     appText: 'assets/images/app-logo-text-hj.svg',
    //     appFull: 'assets/images/app-logo-text-hj-blue.svg',
    //     appLoader: 'assets/images/app-text-shadow.svg',
    //   };

    case env.envt == 'qa' && subDomain == 'helium':
      return {
        appFavIcon: 'assets/images/favicon.ico',
        appLogo: 'assets/images/app-logo-kunj4u.svg',
        appText: 'assets/images/app-text-kunj4u.svg',
        appFull: 'assets/images/app-logo-text-kunj4u.svg',
        appLoader: 'assets/images/app-text-shadow.svg',
      };

    // case env.envt == 'uat' && subDomain == 'app':
    //   return {
    //     appFavIcon: 'assets/images/favicon.ico',
    //     appLogo: 'assets/images/app-logo-dhinwa.svg',
    //     appText: 'assets/images/app-logo-text-dhinwa.svg',
    //     appFull: 'assets/images/app-logo-text-dhinwa-red.svg',
    //     appLoader: 'assets/images/app-text-dhinwa.svg',
    //   };

    case env.envt == 'prod' && subDomain == 'hj':
      return {
        appFavIcon: 'assets/images/favicon.ico',
        appLogo: 'assets/images/app-logo-hj.svg',
        appText: 'assets/images/app-logo-text-hj.svg',
        appFull: 'assets/images/app-logo-text-hj-blue.svg',
        appLoader: 'assets/images/app-text-shadow.svg',
      };

    case env.envt == 'prod' && subDomain == 'prowinproperties':
      return {
        appFavIcon: 'assets/images/favicon.ico',
        appLogo: 'assets/images/app-logo-prowin.svg',
        appText: 'assets/images/app-text-prowin.svg',
        appFull: 'assets/images/app-logo-text-prowin.svg',
        appLoader: 'assets/images/app-text-shadow.svg',
      };
    case env.envt == 'prod' && subDomain == 'realtors-hub':
      return {
        appFavIcon: 'assets/images/favicon.ico',
        appLogo: 'assets/images/app-logo-rh.svg',
        appText: 'assets/images/app-text-rh.svg',
        appFull: 'assets/images/app-logo-text-rh.svg',
        appLoader: 'assets/images/app-text-shadow.svg',
      };
    case env.envt == 'prod' && subDomain == 'kunj4u':
      return {
        appFavIcon: 'assets/images/favicon.ico',
        appLogo: 'assets/images/app-logo-kunj4u.svg',
        appText: 'assets/images/app-text-kunj4u.svg',
        appFull: 'assets/images/app-logo-text-kunj4u.svg',
        appLoader: 'assets/images/app-text-shadow.svg',
      };
    default:
      return {
        appFavIcon: 'assets/images/favicon.ico',
        appLogo: 'assets/images/app-logo-green.svg',
        appText: 'assets/images/app-logo-text-green.svg',
        appFull: 'assets/images/app-logo-text-green.svg',
        appLoader: 'assets/images/app-text-shadow.svg',
      };
  }
};

export const getPages = (totalCount: number, pageSize: number) =>
  Math.ceil(totalCount / pageSize);

export const capitalizeFirstLetter = (string: string) =>
  string.charAt(0).toUpperCase() + string.slice(1);

export const patchFormControlValue = (
  form: FormGroup,
  formControlName: string,
  value: any
) => {
  form.patchValue({ [formControlName]: value });
};

export const getTimeZoneDate = (
  date: Date,
  userTimeZone: string,
  returnType?: string
) => {
  // 2024-10-03T10:00:00Z
  const offset = (userTimeZone?.charAt(0) === '-' ? '' : '+') + userTimeZone;

  const formats: { [key: string]: string } = {
    timeWithMeridiem: 'hh:mm A',
    dayMonthYear: 'DD-MM-YYYY',
    dayMonthYearText: 'DD MMM, YYYY',
    fullDateTime: 'DD-MM-YYYY hh:mm:ss A',
    dateTimeDefault: 'DD-MM-YYYY hh:mm A',
    dateWithTime: 'DD MMM, YYYY | hh:mm A',
    dateWithFullTime: 'DD MMM, YYYY | hh:mm:ss A',
    monthYear: 'MMM yyyy',
    dayMonth: 'DD MMM',
    ISO: 'YYYY-MM-DDTHH:mm:ss',
  };
  const format = formats[returnType] || formats['dateTimeDefault'];
  return moment(date).utcOffset(offset).format(format);
};

export const setTimeZoneDate = (date: Date, baseUTcOffset: string) => {
  if (!date) return null;
  const updatedDate = new Date(date);
  updatedDate.setHours(0, 0, 0, 0);
  const formattedDate =
    updatedDate.getFullYear() +
    '-' +
    String(updatedDate.getMonth() + 1).padStart(2, '0') +
    '-' +
    String(updatedDate.getDate()).padStart(2, '0') +
    'T' +
    String(updatedDate.getHours()).padStart(2, '0') +
    ':' +
    String(updatedDate.getMinutes()).padStart(2, '0') +
    ':' +
    String(updatedDate.getSeconds()).padStart(2, '0');

  const Offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();
  const convertedTime = convertToUtc(formattedDate, Offset);
  return convertedTime;
};

export const patchTimeZoneDate = (
  dateString: string,
  baseUTcOffset: string
) => {
  // 2024-10-15T12:00:00Z
  if (!dateString) {
    return null;
  }

  const date = dateConverted(new Date(dateString));

  let totalOffsetMinutes: number;

  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();
  const sign = offset.startsWith('-') ? -1 : 1;
  const offsetParts = offset.replace('-', '').split(':').map(Number);
  totalOffsetMinutes = sign * (offsetParts[0] * 60 + offsetParts[1]);

  const adjustedDate = new Date(
    date.getTime() + totalOffsetMinutes * 60 * 1000
  );

  // Tue Oct 15 2024 00:00:00 GMT+1400 (Line Islands Time)
  return adjustedDate;
};

export const convertToUtc = (localTime: string, baseUTcOffset: string) => {
  const localDate = moment.utc(localTime);
  const utcDate = localDate
    .clone()
    .utcOffset(-moment.duration(baseUTcOffset).asMinutes());
  return utcDate.format('YYYY-MM-DDTHH:mm:ss[Z]');
};

export const setTimeZoneDateWithTime = (date: Date, baseUTcOffset: string) => {
  if (!date) return null;
  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();
  const localeTimeString = new Date(date);
  const formattedDate =
    localeTimeString.getFullYear() +
    '-' +
    String(localeTimeString.getMonth() + 1).padStart(2, '0') +
    '-' +
    String(localeTimeString.getDate()).padStart(2, '0') +
    'T' +
    String(localeTimeString.getHours()).padStart(2, '0') +
    ':' +
    String(localeTimeString.getMinutes()).padStart(2, '0') +
    ':' +
    String(localeTimeString.getSeconds()).padStart(2, '0');
  const convertedTime = convertToUtc(formattedDate, offset);
  return convertedTime;
};

export const patchTimeZoneWithTime = (
  utcDateInput: Date,
  baseUTcOffset: string
) => {
  if (!utcDateInput) return null;
  const utcDate = new Date(utcDateInput);
  if (isNaN(utcDate.getTime())) {
    throw new Error('Invalid Date');
  }

  if (!baseUTcOffset) return utcDate;
  const utcDateString = utcDate.toISOString();
  const localTimeString = convertToLocalTime(utcDateString, baseUTcOffset);
  return new Date(localTimeString);
};

export const convertToLocalTime = (utcTime: string, offset: string) => {
  const utcDate = moment.utc(utcTime);
  const offsetMinutes = moment.duration(offset).asMinutes();
  const localTime = utcDate.clone().utcOffset(offsetMinutes);
  return localTime.format('YYYY-MM-DDTHH:mm:ss');
};

export const setTimeZoneTime = (date: Date, baseUTcOffset: string) => {
  if (!date) return null;

  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();
  const isNegative = offset.startsWith('-');
  const [offsetHours, offsetMinutes] = offset
    .replace('+', '')
    .replace('-', '')
    .split(':')
    .map(Number);
  const totalOffsetMinutes = offsetHours * 60 + offsetMinutes;
  const utcDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);

  const utcHours = utcDate.getUTCHours();
  const utcMinutes = utcDate.getUTCMinutes();
  const utcSeconds = utcDate.getUTCSeconds();

  let adjustedTotalMinutes;
  if (isNegative) {
    adjustedTotalMinutes = utcHours * 60 + utcMinutes + totalOffsetMinutes;
  } else {
    adjustedTotalMinutes = utcHours * 60 + utcMinutes - totalOffsetMinutes;
  }

  const adjustedHours = Math.floor(adjustedTotalMinutes / 60) % 24;
  const adjustedMinutes = adjustedTotalMinutes % 60;

  const formattedTime = `${String((adjustedHours + 24) % 24).padStart(
    2,
    '0'
  )}:${String((adjustedMinutes + 60) % 60).padStart(2, '0')}:${String(
    utcSeconds
  ).padStart(2, '0')}`;
  return formattedTime;
};

export const patchTime = (timeString: string, offset: string) => {
  //time - 15:30:00
  if (!timeString) return null;
  const currentDate = new Date();
  const [time, modifier] = timeString.split(' ');
  let [hours, minutes, seconds] = time.split(':').map(Number);

  if (modifier === 'PM' && hours < 12) hours += 12;
  if (modifier === 'AM' && hours === 12) hours = 0;

  currentDate.setHours(hours, minutes, seconds || 0);
  const baseoffset = offset ? offset : getSystemTimeOffset();
  const isNegative = baseoffset.startsWith('-');

  const [offsetHours, offsetMinutes] = baseoffset
    .replace('+', '')
    .replace('-', '')
    .split(':')
    .map(Number);
  const totalOffsetMillis = (offsetHours * 60 * 60 + offsetMinutes * 60) * 1000;

  const adjustedTime = isNegative
    ? new Date(currentDate.getTime() - totalOffsetMillis)
    : new Date(currentDate.getTime() + totalOffsetMillis);
  // Thu Oct 24 2024 11:51:28 GMT+0530 (India Standard Time)
  return adjustedTime;
};

export const getTimeZoneTime = (timeString: string, offset: string | null) => {
  if (!timeString) return null;

  const [time, modifier] = timeString.split(' ');
  let [hours, minutes, seconds] = time.split(':').map(Number);

  if (modifier === 'PM' && hours < 12) hours += 12;
  if (modifier === 'AM' && hours === 12) hours = 0;

  const baseDate = new Date();
  baseDate.setHours(hours, minutes, seconds || 0);

  const baseOffset = offset ? offset : getSystemTimeOffset();
  const isNegativeOffset = baseOffset.startsWith('-');

  const [offsetHours, offsetMinutes] = baseOffset
    .replace('+', '')
    .replace('-', '')
    .split(':')
    .map(Number);
  const totalOffsetMillis = (offsetHours * 60 * 60 + offsetMinutes * 60) * 1000;

  const adjustedDate = isNegativeOffset
    ? new Date(baseDate.getTime() - totalOffsetMillis)
    : new Date(baseDate.getTime() + totalOffsetMillis);

  return adjustedDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
};

export const getSystemTimeOffset: () => string = () => {
  const offsetInMinutes = new Date().getTimezoneOffset();
  const hours = Math.floor(Math.abs(offsetInMinutes) / 60);
  const minutes = Math.abs(offsetInMinutes) % 60;
  const sign = offsetInMinutes <= 0 ? '' : '-';
  return `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(
    2,
    '0'
  )}:00`;
};

export const getSystemTimeZoneId: () => string = () => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

export const dateConverted = (date: Date): Date => {
  if (!date) return null;

  let convertedDate = new Date(date);
  const timezoneOffset = convertedDate.getTimezoneOffset();
  const offsetInMs = timezoneOffset * 60 * 1000;
  convertedDate = new Date(convertedDate.getTime() + offsetInMs);
  return convertedDate;
};

export const changeCalendar = (baseUTcOffset: string): Date => {
  const startAt = new Date();

  const utcTime = new Date(
    startAt.getUTCFullYear(),
    startAt.getUTCMonth(),
    startAt.getUTCDate(),
    startAt.getUTCHours(),
    startAt.getUTCMinutes(),
    startAt.getUTCSeconds()
  );
  const offsetString = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();
  const [hours, minutes] = offsetString.split(':').map(Number);
  const offsetMinutes = hours * 60 + (minutes || 0);
  const adjustedTime = new Date(utcTime.getTime() + offsetMinutes * 60000);
  return adjustedTime;
};

export const formatDateToCustomString = (date: Date, type?: string): string => {
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const day = date.getDate();
  const monthIndex = date.getMonth();
  const year = date.getFullYear();

  if (type === 'month') {
    return `${monthNames[monthIndex]} ${year}`;
  }
  return `${day} ${monthNames[monthIndex]} ${year}`;
};

export const onPickerOpened = (date: Date, type?: string): void => {
  const observer = new MutationObserver(() => {
    highlightCustomToday(date, type);
  });

  setTimeout(() => {
    const calendarElement = document.querySelector('.owl-dt-calendar');
    if (calendarElement) {
      observer.observe(calendarElement, { childList: true, subtree: true });
      highlightCustomToday(date, type);
    }
  }, 100);
};

const highlightCustomToday = (date: Date, type?: string): void => {
  const calendarCells = document.querySelectorAll('.owl-dt-calendar-cell');
  const formattedMinAllowedDate = formatDateToCustomString(date, type);

  calendarCells.forEach((cell) => {
    const ariaLabel = cell.getAttribute('aria-label');

    if (ariaLabel) {
      const cellDate = new Date(ariaLabel);

      if (!isNaN(cellDate.getTime())) {
        const formattedAriaLabelDate = formatDateToCustomString(cellDate, type);

        if (formattedAriaLabelDate === formattedMinAllowedDate) {
          if (!cell.classList.contains('owl-dt-calendar-cell-selected')) {
            const cellContent = cell.querySelector(
              '.owl-dt-calendar-cell-content'
            );
            if (cellContent) {
              cellContent.classList.add('custom-today');
            }
          }
        }
      }
    }
  });
};

export const getISODateFormat = (dateTime: Date) => {
  if (!dateTime) {
    return null;
  }
  return `${moment(dateTime).format('YYYY-MM-DD')}T${moment(dateTime).format(
    'HH:mm'
  )}:00.000Z`;
};

export const getISOOnlyDate = (dateTime: any) => {
  if (!dateTime) {
    return null;
  }
  return `${moment(dateTime).format('YYYY-MM-DD')}T00:00:00.000Z`;
};
export const getISODate = (dateTime: Date) => {
  if (!dateTime) {
    return null;
  }
  return `${moment(dateTime).format('YYYY-MM-DD')}T00:00:00`;
};
export const validateAllFormFields = (formGroup: FormGroup) => {
  Object.keys(formGroup.controls).forEach((field) => {
    const control = formGroup.get(field);
    if (control instanceof FormControl) {
      control.markAsTouched({ onlySelf: true });
    } else if (control instanceof FormGroup) {
      validateAllFormFields(control);
    }
  });
};
export const formatCurrency = (
  amount: string,
  locale: string,
  currencySymbol: string
) => {
  if (amount == null || amount == '') return '';
  return `${currencySymbol} ${Number(amount).toLocaleString(locale)}`;
};
export const sortArrayByKey = (array: any[], keyName: string) => {
  return array.sort((a: any, b: any) => (a[keyName] > b[keyName] ? 1 : -1));
};
export const getLeadStatusId = (status: string, statusList: any) =>
  statusList.filter((item: any) => item.actionName == status)[0].id;
export const toggleValidation = (
  validationType: string,
  formGroup: FormGroup,
  field: string,
  validators?: Array<ValidatorFn>
) => {
  const control = formGroup.get(field);
  if (control instanceof FormControl) {
    switch (validationType) {
      case VALIDATION_CLEAR:
        control.clearValidators();
        break;
      case VALIDATION_SET:
        control.setValidators(validators);
        break;
    }
    control.updateValueAndValidity();
  }
};
export const getDateDiffInDays: any = (date1: any, date2: any) =>
  moment(date1).isSameOrBefore(moment(date2), 'days');
export const isEmptyObject = (obj: any) =>
  Object.keys(obj).length === 0 && obj.constructor === Object;
export const setPropertySubTypeList: any = (
  pType: string,
  propertyTypeList: any[],
  isProject: boolean = false
) => {
  let propertySubTypeList;
  if (!propertyTypeList?.length) {
    return;
  }
  const [property]: any = propertyTypeList.filter(
    (prop: any) => prop.displayName === pType
  );
  if (property?.displayName === 'Residential' && !isProject) {
    propertySubTypeList =
      [
        ...new Set(property?.childTypes.map((item: any) => item.displayName)),
      ].map((item: any) => {
        return { displayName: item };
      }) || [];
  } else {
    propertySubTypeList = property?.childTypes || [];
  }
  return propertySubTypeList;
};
export const setPropertyBhkTypeList: any = (
  pType: string,
  propertyTypeList: any[]
) => {
  let propertyBhkTypeList;
  const [property]: any = propertyTypeList.filter(
    (prop: any) => prop.displayName === pType
  );
  if (property?.displayName === 'Residential') {
    propertyBhkTypeList =
      [...new Set(property?.childTypes.map((item: any) => item.bhkType))]
        .map((item: any) => {
          return item;
        })
        .filter((item: string) => {
          if (item != null) {
            return { bhkType: item };
          }
          return item;
        }) || [];
  }
  return propertyBhkTypeList;
};

export const getPropertyTypeIds = (
  propertyTypeList: any[],
  propertyType: string,
  propertySubTypes: string[]
): any[] => {
  const selectedPropertyType = propertyTypeList.find(
    (pType) => pType.displayName === propertyType
  );

  if (!selectedPropertyType?.childTypes?.length) return [];

  if (selectedPropertyType.displayName === 'Residential') {
    return selectedPropertyType.childTypes
      .filter((pSubType: any) =>
        propertySubTypes.includes(pSubType.displayName)
      )
      .map((pSubType: any) => pSubType.id);
  } else {
    return selectedPropertyType.childTypes
      .filter((pSubType: any) =>
        propertySubTypes.includes(pSubType.displayName)
      )
      .map((pSubType: any) => pSubType.id);
  }
};

export const getPropertyTypeId: any = (
  propertyTypeList: any[],
  propertyType: string,
  propertySubType: string
) => {
  const [selectedPropertyType]: any = propertyTypeList.filter(
    (pType: any) => pType.displayName == propertyType
  );
  if (!selectedPropertyType?.childTypes.length) return;
  else if (
    selectedPropertyType?.displayName == 'Residential' &&
    propertySubType != 'Plot'
  ) {
    const [selectedPropertySubType]: any =
      selectedPropertyType?.childTypes.filter(
        (pSubType: any) => pSubType.displayName == propertySubType
      );
    return selectedPropertySubType?.id;
  } else {
    const [selectedPropertySubType]: any =
      selectedPropertyType?.childTypes.filter(
        (pSubType: any) => pSubType.displayName == propertySubType
      );
    return selectedPropertySubType?.id;
  }
};

export const istFormat: any = (utc: any) => moment.utc(utc).toDate();
export const getFormattedDate = (dateTime: Date, format: string) =>
  moment(new Date(dateTime)).utcOffset(0).format(format);
export const isValidFilePath = (urlString: string) =>
  urlString.includes('data:');

export const getAWSImagePath = (imagePath: string) => {
  return isValidFilePath(imagePath)
    ? imagePath
    : `${env.s3ImageBucketURL}${imagePath}`;
};
export const getAreaUnit: any = (
  id: string,
  areaUnitList: Array<MasterAreaUnitType>
) => areaUnitList.filter((item: MasterAreaUnitType) => item.id === id)[0];

export const isTokenExpired = () => {
  const idToken = localStorage.getItem('idToken');
  if (idToken && idToken.length) {
    const expirationTime = JSON.parse(atob(idToken.split('.')[1])).exp;
    const isExpiredToken =
      Math.floor(new Date().getTime() / 1000) >= expirationTime;
    return isExpiredToken;
  }
  return true;
};

export const isEmptyGuid = (guid: string) => guid === EMPTY_GUID;

export const getAssignedToDetails = (
  userInfo: string,
  usersList: any,
  returnNameOnly: boolean = false
) => {
  if (!userInfo || isEmptyGuid(userInfo) || !usersList?.length) return null;

  const user = usersList?.filter(
    (user: any) =>
      user.id == userInfo ||
      (user.firstName == userInfo?.split(' ')?.[0] &&
        user.lastName == userInfo?.split(' ')?.[1]) ||
      `${user.firstName} ${user.lastName}` === userInfo
  )?.[0];

  return user
    ? returnNameOnly
      ? `${user.firstName} ${user.lastName}`
      : user
    : null;
};

export const matchValidator = (
  control: AbstractControl,
  controlTwo: AbstractControl
): ValidatorFn => {
  return () => {
    if (control.value !== controlTwo.value)
      return { match_password: 'Password and Confirm Password does not match' };
    return null;
  };
};

export const groupBy = (xs: any, key: any) =>
  xs.reduce((rv: any, x: any) => {
    (rv[x[key]] = rv[x[key]] || []).reverse().push(x);
    return rv;
  }, {});

export const getIndexes = (property: string, array: any[]) => {
  if (
    property === 'Source' ||
    property === 'Sources' ||
    property === 'LeadSources'
  ) {
    array = array.map((item) => {
      return LeadSource[item];
    });
  } else if (property === 'BHKTypes') {
    array = array.map((item) => {
      return BHKType[item];
    });
  } else if (property === 'Budget') {
    array = array.map((item) => {
      return PROPERTY_BUDGET_FILTER.indexOf(item);
    });
  } else if (property === 'PriceRange') {
    array = array.map((item) => {
      return PropertyPriceFilter[item];
    });
  } else if (property === 'enquiredFor') {
    array = array.map((item) => {
      return EnquiryType[item];
    });
  } else if (property === 'MeetingOrVisitStatuses') {
    array = array.map((item) => {
      return DoneStatus[item];
    });
  } else if (property === 'FurnishStatuses' || property === 'Furnished') {
    array = array.map((item) => {
      return FurnishStatus[item];
    });
  } else if (property === 'OfferTypes') {
    array = array.map((item) => {
      return OfferType[item];
    });
  } else if (property === 'Purposes') {
    array = array.map((item) => {
      return PurposeType[item];
    });
  }
  return array;
};

export const formatBudget = (budget: number, currency: string | null) => {
  const formatInternationalCurrency = (budget: number, symbol: string) => {
    if (budget < 1000) {
      return symbol + ' ' + budget?.toString();
    } else if (budget >= 1000 && budget < 1000000) {
      return symbol + ' ' + (budget / 1000).toFixed(2) + ' K';
    } else if (budget >= 1000000 && budget < 1000000000) {
      return symbol + ' ' + (budget / 1000000).toFixed(2) + ' M';
    } else {
      return symbol + ' ' + (budget / 1000000000).toFixed(2) + ' B';
    }
  };
  const formatINRCurrency = (budget: number, symbol: string) => {
    if (budget < 1000) {
      return symbol + ' ' + budget?.toString();
    } else if (budget >= 1000 && budget < 100000) {
      return symbol + ' ' + (budget / 1000).toFixed(2) + ' K';
    } else if (budget >= 100000 && budget < 10000000) {
      return symbol + ' ' + (budget / 100000).toFixed(2) + ' Lacs';
    } else {
      return symbol + ' ' + (budget / 10000000).toFixed(2) + ' Cr';
    }
  };

  if (currency === null) {
    return budget?.toString();
  }

  if (currency?.includes('INR')) {
    return formatINRCurrency(budget, 'INR');
  } else {
    return formatInternationalCurrency(budget, currency);
  }
};

export const onlyNumbers = (event: any) => {
  const keyCode = event.keyCode;
  const excludedKeys = [8, 37, 39, 46];
  if (
    !(
      (keyCode >= 48 && keyCode <= 57) ||
      (keyCode >= 96 && keyCode <= 105) ||
      excludedKeys.includes(keyCode)
    )
  ) {
    event.preventDefault();
  }
};

export const onlyNumbersWithDecimal = (event: any, currentValue: string) => {
  const keyCode = event.keyCode;
  const excludedKeys = [8, 9, 37, 39, 46];
  const isNumberKey = (keyCode >= 48 && keyCode <= 57) || (keyCode >= 96 && keyCode <= 105);
  const isDecimalKey = keyCode === 190 || keyCode === 110;
  if (!(isNumberKey || isDecimalKey || excludedKeys.includes(keyCode))) {
    event.preventDefault();
    return;
  }
  if ((keyCode === 190 || keyCode === 110) && currentValue.includes('.')) {
    event.preventDefault();
  }
};

export const LeadTemplateMsg = (
  template: any,
  leadData: any,
  tenantName: string,
  defaultCurrency: string,
  header?: any,
  footer?: any,
  allUserList?: any,
  userData?: any,
  currentDate?: any,
  module?: string
) => {
  let combinedMsg = '';
  const transformedAttributes: any = {};

  let replacements: any = [];

  template = template?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });
  header = header?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });
  footer = footer?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });

  const normalizeVar = (variable: string) =>
    variable.toLowerCase()?.replace(/[\s-]+/g, '');

  const processReplacements = (msg: any, replacements: any) => {
    if (msg) {
      replacements.forEach((item: any) => {
        if (Array.isArray(item.var)) {
          item.var.forEach((variable: string) => {
            const normalizedVar = normalizeVar(variable);
            msg = msg?.replaceAll(normalizedVar, item?.text?.trim());
          });
        } else {
          const normalizedVar = normalizeVar(item.var);
          msg = msg?.replaceAll(normalizedVar, item?.text?.trim());
        }
      });
    }

    return msg;
  };

  const processLeadData = (dataItem: any, index: number) => {
    let date;
    let remTime;
    let remTimeStr;
    if (dataItem?.scheduledDate || dataItem?.scheduleDate) {
      date = new Date(dataItem?.scheduledDate || dataItem?.scheduleDate);
      let today = new Date(currentDate);

      remTime = date.getTime() - today.getTime();
      remTimeStr = '';

      if (remTime > 0) {
        remTimeStr = Math.floor(remTime / (1000 * 60 * 60)) + ' hours ';
        remTime = remTime % (1000 * 60 * 60);
        remTimeStr += Math.floor(remTime / (1000 * 60)) + ' mins';
      }
    }

    let msg = template;
    if (dataItem.attributes?.length) {
      for (const attribute of dataItem.attributes) {
        if (!attribute?.attributeName.startsWith('Is')) {
          transformedAttributes[attribute?.attributeName] = attribute.value;
        }
      }
    }
    replacements = [
      { var: '#leadName#', text: dataItem?.name?.replace(/\s+/g, ' ').trim() },
      {
        var: '#userName#',
        text: `${userData?.firstName} ${userData?.lastName}`,
      },
      { var: '#userEmail#', text: userData?.email },
      { var: '#userMobile#', text: userData?.phoneNumber },
      { var: '#tenantName#', text: tenantName || '' },
      {
        var: ['#date#', '#Schedule Date#'],
        text:
          dataItem?.scheduledDate || dataItem?.scheduleDate
            ? getTimeZoneDate(
              dataItem?.scheduledDate || dataItem?.scheduleDate,
              userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            )
            : '',
      },
      {
        var: '#PropertyMicrositeUrl#',
        text: dataItem?.properties?.length
          ? dataItem?.properties
            ?.map((property: any) => getMSUrl(property.serialNo))
            .join(' , ')
          : '',
      },
      {
        var: '#ProjectMicrositeUrl#',
        text: dataItem?.projects?.length
          ? dataItem?.projects
            ?.map((project: any) => getMSUrl(project.serialNo, true))
            .join(' , ')
          : '',
      },
      {
        var: ['#time#', '#ScheduleTime#'],
        text:
          dataItem?.scheduledDate || dataItem?.scheduleDate
            ? getTimeZoneDate(
              dataItem?.scheduledDate || dataItem?.scheduleDate,
              userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
      },
      {
        var: ['#projectName#', '#Projects#'],
        text: dataItem?.projects?.length
          ? dataItem?.projects?.map((project: any) => project.name).join(', ')
          : '',
      },
      {
        var: ['#propertyName#', '#Properties#'],
        text: dataItem?.properties?.length
          ? dataItem?.properties
            ?.map((property: any) => property.title)
            .join(', ')
          : '',
      },
      {
        var: '#LowerBudget#',
        text: dataItem?.enquiry?.lowerBudget
          ? dataItem?.enquiry?.lowerBudget +
          ' (' +
          formatBudget(
            dataItem?.enquiry?.lowerBudget,
            dataItem?.enquiry?.currency || defaultCurrency
          ) +
          ')'
          : '',
      },
      {
        var: ['#priceRange#', '#UpperBudget#'],
        text: dataItem?.enquiry?.upperBudget
          ? dataItem?.enquiry?.upperBudget +
          ' (' +
          formatBudget(
            dataItem?.enquiry?.upperBudget,
            dataItem?.enquiry?.currency || defaultCurrency
          ) +
          ')'
          : '',
      },
      {
        var: [
          '#enquiryType#',
          '#EnquiryTypes#',
          '#EnquiredFor#',
          '#Enquired For#',
          '#SaleType#',
          '#Sale Type#',
        ],
        text: dataItem?.enquiry?.enquiryTypes
          ? [...dataItem?.enquiry?.enquiryTypes]
            ?.map((type: any) => EnquiryType[type])
            ?.join(', ')
          : '',
      },
      { var: '#link#', text: 'link' },
      {
        var: '#remainingTime#',
        text: remTime > 0 ? remTimeStr : '00:00 hrs',
      },
      {
        var: [
          '#enquiredLocation#',
          '#Addresses#',
          '#Address#',
          '#PreferredLocation#',
        ],
        text: dataItem?.enquiry?.addresses
          ? dataItem?.enquiry?.addresses
            .map((address: any) => getLocationDetailsByObj(address))
            .join(', ')
          : '--',
      },
      {
        var: '#Lead Alternate Contact No#',
        text: dataItem?.alternateContactNo || '',
      },
      {
        var: ['#Lead Contact No#', '#LeadFullContactNo#'],
        text: dataItem?.contactNo || '',
      },
      {
        var: '#Lead Landline Number#',
        text: dataItem?.landLine || '',
      },
      {
        var: '#Lead Email#',
        text: dataItem?.email || '',
      },
      {
        var: '#Referral Name#',
        text: dataItem?.referralName || '',
      },
      {
        var: '#Referral Contact No#',
        text: dataItem?.referralContactNo || '',
      },
      {
        var: '#Referral Email#',
        text: dataItem?.referralEmail || '',
      },
      {
        var: '#Company Name#',
        text: dataItem?.companyName || '',
      },
      {
        var: ['#Primary Owner#', '#Assign To#'],
        text: (() => {
          const user = allUserList?.find(
            (user: any) => user.id === dataItem?.assignTo
          );
          return user ? user.firstName + ' ' + user.lastName : '';
        })(),
      },
      {
        var: '#Secondary Owner#',
        text: (() => {
          const user = allUserList?.find(
            (user: any) => user.id === dataItem?.secondaryUserId
          );
          return user ? user.firstName + ' ' + user.lastName : '';
        })(),
      },
      {
        var: '#Closing Manager#',
        text: (() => {
          const user = allUserList?.find(
            (user: any) => user.id === dataItem?.closingManager
          );
          return user ? user.firstName + ' ' + user.lastName : '';
        })(),
      },
      {
        var: '#Sourcing Manager#',
        text: (() => {
          const user = allUserList?.find(
            (user: any) => user.id === dataItem?.sourcingManager
          );
          return user ? user.firstName + ' ' + user.lastName : '';
        })(),
      },
      {
        var: '#Channel Partner Name#',
        text: dataItem?.channelPartners
          ? dataItem.channelPartners
            .map((partner: any) => partner.firmName)
            .join(', ')
          : '',
      },
      {
        var: '#Designation#',
        text: dataItem?.designation || '',
      },
      {
        var: '#Sale Type#',
        text: SaleType[dataItem?.enquiry?.saleType] || '',
      },
      {
        var: '#Lead Source#',
        text:
          LeadSource[dataItem?.enquiry?.leadSource] ||
          dataItem?.enquiry?.prospectSource?.displayName ||
          '',
      },
      {
        var: '#Sub Source#',
        text: dataItem?.enquiry?.subSource || '',
      },
      {
        var: '#Property Type#',
        text: dataItem?.enquiry?.propertyTypes?.[0]?.displayName || '',
      },
      {
        var: '#PropertySubType#',
        text:
          dataItem?.enquiry?.propertyTypes
            ?.map((item: any) => item?.childType?.displayName)
            ?.join(', ') || '',
      },
      {
        var: ['#noOfBhk#', '#BHKs#'],
        text: dataItem?.enquiry?.bhKs
          ? [...dataItem?.enquiry?.bhKs]
            ?.map((bhk: any) => getBHKDisplayString(bhk))
            ?.join(', ')
          : '',
      },
      /* BR field commented out
      {
        var: '#BR#',
        text: dataItem?.enquiry?.bhKs
          ? [...dataItem?.enquiry?.bhKs]
            ?.map((br: any) => getBRDisplayString(br))
            ?.join(', ')
          : '',
      },
      */
      {
        var: ['#bHKType#', '#BHKTypes#'],
        text: dataItem?.enquiry?.bhkTypes
          ? [...dataItem?.enquiry?.bhkTypes]
            ?.map((type: any) => BHKType[type])
            ?.join(', ')
          : '',
      },
      {
        var: '#Beds#',
        text: dataItem?.enquiry?.beds?.length
          ? [...dataItem.enquiry?.beds]
            ?.map((bed: any) => (bed === 0 || bed === '0' ? 'Studio' : bed))
            ?.join(', ')
          : '',
      },
      {
        var: '#Baths#',
        text: dataItem?.enquiry?.baths
          ? [...dataItem?.enquiry?.baths]?.map((bath: any) => bath)?.join(', ')
          : '',
      },
      {
        var: '#Furnish Status#',
        text: FurnishStatus[dataItem?.enquiry?.furnished] || '',
      },
      {
        var: '#Preferred Floor#',
        text: dataItem?.enquiry?.floors
          ? [...dataItem?.enquiry?.floors]
            ?.map((floor: any) => floor)
            ?.join(', ')
          : '',
      },
      {
        var: '#Offering Type#',
        text: OfferType[dataItem?.enquiry?.offerType] || '',
      },
      {
        var: '#Carpet Area#',
        text:
          (dataItem?.enquiry?.carpetArea ?? '') +
          ' ' +
          (dataItem?.enquiry?.carpetAreaUnit ?? ''),
      },
      {
        var: '#Saleable Area#',
        text:
          (dataItem?.enquiry?.saleableArea ?? '') +
          ' ' +
          (dataItem?.enquiry?.saleableAreaUnit ?? ''),
      },
      {
        var: '#BuiltUp Area#',
        text:
          (dataItem?.enquiry?.builtUpArea ?? '') +
          ' ' +
          (dataItem?.enquiry?.builtUpAreaUnit ?? ''),
      },
      {
        var: '#Property Area#',
        text:
          (dataItem?.enquiry?.propertyArea ?? '') +
          ' ' +
          (dataItem?.enquiry?.propertyAreaUnit ?? ''),
      },
      {
        var: '#Net Area#',
        text:
          (dataItem?.enquiry?.netArea ?? '') +
          ' ' +
          (dataItem?.enquiry?.netAreaUnit ?? ''),
      },
      {
        var: '#Unit Number or Name#',
        text: dataItem?.enquiry?.unitName || '',
      },
      {
        var: '#Cluster Name#',
        text: dataItem?.enquiry?.clusterName || '',
      },
      {
        var: '#Purpose#',
        text: PurposeType[dataItem?.enquiry?.purpose]
          ? PurposeType[dataItem?.enquiry?.purpose]
          : '',
      },
      {
        var: '#Nationality#',
        text: dataItem?.nationality || '',
      },
      {
        var: '#Possession Date#',
        text: (() => {
          const possessionType =
            module?.includes('data') ? dataItem?.enquiry?.possesionType : dataItem?.possesionType;
          const possessionDate =
            module?.includes('data') ? dataItem?.possesionDate : dataItem?.enquiry?.possessionDate;
          if (possessionType && possessionType > 0 && PossessionType[possessionType]) {
            if (possessionType === PossessionType['Custom Date'] && possessionDate) {
              return getTimeZoneDate(
                possessionDate,
                userData?.timeZoneInfo?.baseUTcOffset,
                'dayMonthYear'
              );
            }
            return PossessionType[possessionType];
          }
          return '';
        })(),
      },
      {
        var: '#Currency#',
        text: dataItem?.enquiry?.currency || '',
      },
      {
        var: '#Agencies#',
        text: dataItem?.agencies
          ? dataItem?.agencies.map((item: any) => item.name).join(', ')
          : '',
      },
      {
        var: '#Campaigns#',
        text: dataItem?.campaigns
          ? dataItem?.campaigns.map((item: any) => item.name).join(', ')
          : '',
      },
      {
        var: '#Custom Lead Status#',
        text:
          dataItem?.status?.childType?.displayName ??
          dataItem?.status?.displayName ??
          '',
      },
      {
        var: '#Gender#',
        text: Gender[dataItem?.gender] || '',
      },
      {
        var: '#Marital Status#',
        text: MaritalStatusType[dataItem?.maritalStatus] || '',
      },
      {
        var: '#Date of Birth#',
        text: dataItem?.dateOfBirth ? getTimeZoneDate(dataItem?.dateOfBirth, '00:00:00', 'dayMonthYear') : '',
      },
    ];
    return processReplacements(msg, replacements);
  };

  if (Array.isArray(leadData)) {
    leadData?.forEach((dataItem: any, index: number) => {
      const msg = processLeadData(dataItem, index);
      combinedMsg += index + 1 + '. ' + msg;
      if (index < leadData.length - 1) {
        combinedMsg += '\n';
      }
    });
  } else {
    combinedMsg = processLeadData(leadData, 0);
  }
  if (header) {
    header = processReplacements(header, replacements);
  }

  if (footer) {
    footer = processReplacements(footer, replacements);
  }

  return combinedMsg
    ? (header ? header?.replace(/\n/g, '\\n') + '\\n' : '') +
    combinedMsg?.replace(/\n/g, '\\n') +
    (footer ? '\\n' + footer?.replace(/\n/g, '\\n') : '')
    : '';
};

export const WhatsAppTemplateMsg = (
  template: any,
  leadData: any,
  tenantName: string,
  defaultCurrency: string,
  replaceData?: any,
  media?: any,
  allUserList?: any,
  userData?: any,
  currentDate?: any
) => {
  let combinedMsg = '';

  let replacements: any = [];
  const processReplacements = (msg: any, replacements: any) => {
    if (typeof msg === 'string') {
      replacements.forEach((item: any) => {
        if (item.var === "\"#BodyValues#\"") {
          msg = msg.replace(
            "\"#BodyValues#\"",
            item?.text?.map((s: string) => `"${s}"`).join(',') || ''
          );
        } else {
          const regex = new RegExp(item.var, 'gi');
          msg = msg.replaceAll(regex, item?.text?.trim());
        }
      });
    }
    return msg;
  };

  const processLeadData = (dataItem: any, index: number) => {
    let msg = replaceData;
    let header: any, body: any;
    if (template?.headerValues) {
      header = Object.values(template?.headerValues);
      header = header.join(', ');
    }
    if (template?.bodyValues) {
      body = Object.values(template?.bodyValues).map((item: any) =>
        LeadTemplateMsg(
          item || 'default',
          leadData,
          tenantName,
          defaultCurrency,
          null,
          null,
          allUserList,
          userData,
          currentDate
        )
      );
    }
    replacements = [
      {
        var: '#LeadFullContactNo#',
        text: dataItem?.contactNo,
      },
      // {
      //   var: '\n',
      //   text: '\\n',
      // },
      {
        var: '#HeaderValues#',
        text: LeadTemplateMsg(
          header || 'default',
          leadData,
          tenantName,
          defaultCurrency,
          null,
          null,
          allUserList,
          userData,
          currentDate
        ),
      },
      {
        var: '\"#BodyValues#\"',
        text: body,
      },
      {
        var: '#Message#',
        text:
          LeadTemplateMsg(
            template?.message,
            leadData,
            tenantName,
            defaultCurrency,
            template?.header,
            template?.footer,
            allUserList,
            userData,
            currentDate
          ) || 'None',
      },
      { var: '#TemplateName#', text: template?.title || null },
      { var: '#TemplateId#', text: template?.id },
      { var: '#FileName#', text: media?.fileName || 'default' },
      { var: '#MediaUrl#', text: media?.mediaUrl || 'default' },
      { var: '#MessageType#', text: media?.mediaType || '' },
      { var: '#leadName#', text: leadData?.name },
    ];

    return processReplacements(msg, replacements);
  };

  if (Array.isArray(leadData)) {
    leadData?.forEach((dataItem: any, index: number) => {
      const msg = processLeadData(dataItem, index);
      combinedMsg += index + 1 + '. ' + msg;
      if (index < leadData.length - 1) {
        combinedMsg += '\n';
      }
    });
  } else {
    combinedMsg = processLeadData(leadData, 0);
  }

  return combinedMsg ? combinedMsg : '';
};

export const PropertyTemplateMsg = (
  template: any,
  propertyData: any,
  areaSizeUnits: string,
  tenantName: string,
  header: any,
  footer: any,
  canViewOwnerDetails: boolean,
  defaultCurrency: string,
  userData?: any,
  currentDate?: any
) => {
  let combinedMsg = '';
  const transformedAttributes: any = {};
  let replacements: any = [];

  template = template?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });
  header = header?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });
  footer = footer?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });

  const normalizeVar = (variable: string) =>
    variable.toLowerCase()?.replace(/[\s-]+/g, '');

  const processReplacements = (msg: any, replacements: any) => {
    if (msg) {
      replacements.forEach((item: any) => {
        if (Array.isArray(item.var)) {
          item.var.forEach((variable: string) => {
            const normalizedVar = normalizeVar(variable);
            msg = msg?.replaceAll(normalizedVar, item?.text?.trim());
          });
        } else {
          const normalizedVar = normalizeVar(item.var);
          msg = msg?.replaceAll(normalizedVar, item?.text?.trim());
        }
      });
    }
    return msg;
  };

  const processPropertyData = (dataItem: any, index: number) => {
    let msg = template;
    if (dataItem.attributes?.length) {
      for (const attribute of dataItem.attributes) {
        if (!attribute?.attributeName.startsWith('Is')) {
          transformedAttributes[attribute?.attributeName] = attribute.value;
        }
      }
    }

    let propertyLinks = '';

    dataItem?.links?.map((link: any) => {
      propertyLinks += link + ', ';
    });
    propertyLinks = propertyLinks.slice(0, -2);

    replacements = [
      {
        var: '#PropertyStatus#',
        text: dataItem?.status == 1 ? 'Sold' : PropertyStatus[dataItem?.status],
      },
      {
        var: '#EnquiredFor#',
        text: dataItem?.enquiredFor ? EnquiryType[dataItem?.enquiredFor] : '',
      },
      {
        var: '#PropertyType#',
        text: dataItem.propertyType?.displayName
          ? dataItem.propertyType?.displayName
          : '',
      },
      {
        var: '#PropertySubType#',
        text: dataItem.propertyType?.childType?.displayName
          ? dataItem.propertyType?.childType?.displayName
          : '',
      },
      {
        var: '#NoOfBHK#',
        text: dataItem?.noOfBHK ? getBHKDisplayString(dataItem?.noOfBHK) : '',
      },
      {
        var: '#NoOfBR#',
        text: dataItem?.noOfBHK ? getBRDisplayString(dataItem?.noOfBHK) : '',
      },
      {
        var: '#BHKType#',
        text: dataItem?.bhkType ? BHKType[dataItem?.bhkType] : '',
      },
      {
        var: '#BRType#',
        text: dataItem?.bhkType ? BHKType[dataItem?.bhkType] : '',
      },
      { var: '#Title#', text: dataItem?.title ? dataItem?.title : '' },
      {
        var: ['#PropertyArea#', '#PropertySize#'],
        text: `${dataItem?.dimension?.area ? dataItem?.dimension?.area : ''} ${dataItem.dimension?.areaUnitId
          ? getAreaUnit(dataItem.dimension?.areaUnitId, areaSizeUnits)?.unit
          : ''
          }`,
      },
      {
        var: '#CarpetArea#',
        text: `${dataItem?.dimension?.carpetArea ? dataItem?.dimension?.carpetArea : ''
          } ${dataItem.dimension?.carpetAreaId
            ? getAreaUnit(dataItem.dimension?.carpetAreaId, areaSizeUnits)?.unit
            : ''
          }`,
      },
      {
        var: '#BuiltUpArea#',
        text: `${dataItem?.dimension?.buildUpArea
          ? dataItem?.dimension?.buildUpArea
          : ''
          } ${dataItem.dimension?.buildUpAreaId
            ? getAreaUnit(dataItem.dimension?.buildUpAreaId, areaSizeUnits)
              ?.unit
            : ''
          }`,
      },
      {
        var: '#SaleableArea#',
        text: `${dataItem?.dimension?.saleableArea
          ? dataItem?.dimension?.saleableArea
          : ''
          } ${dataItem.dimension?.saleableAreaId
            ? getAreaUnit(dataItem.dimension?.saleableAreaId, areaSizeUnits)
              ?.unit
            : ''
          }`,
      },
      {
        var: '#NetArea#',
        text: `${dataItem?.dimension?.netArea ? dataItem?.dimension?.netArea : ''
          } ${dataItem.dimension?.netAreaUnitId
            ? getAreaUnit(dataItem.dimension?.netAreaUnitId, areaSizeUnits)
              ?.unit
            : ''
          }`,
      },
      {
        var: '#Dimension#',
        text: `${dataItem?.dimension?.length ? 'L-' + dataItem?.dimension?.length : ''
          } ${dataItem?.dimension?.breadth && dataItem?.dimension?.length ? 'X' : ''
          } ${dataItem?.dimension?.breadth
            ? 'B-' + dataItem?.dimension?.breadth
            : ''
          }`,
      },
      {
        var: '#SaleType#',
        text: dataItem.saleType ? SaleType[dataItem.saleType] : '',
      },
      {
        var: '#PossessionDate#',
        text: (() => {
          const possessionType = dataItem?.possesionType;
          const possessionDate = dataItem?.possessionDate;
          if (possessionType && possessionType > 0 && PossessionType[possessionType]) {
            if (possessionType === PossessionType['Custom Date'] && possessionDate) {
              const isReadyToMove = getTimeZoneDate(possessionDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') <= getTimeZoneDate(currentDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
              return isReadyToMove
                ? 'Ready To Move'
                : getTimeZoneDate(
                  possessionDate,
                  userData?.timeZoneInfo?.baseUTcOffset,
                  'dayMonthYear'
                );
            }
            return PossessionType[possessionType];
          }
          return '';
        })(),
      },
      {
        var: '#TotalPrice#',
        text: dataItem?.monetaryInfo?.expectedPrice
          ? dataItem?.monetaryInfo?.expectedPrice +
          ' (' +
          formatBudget(
            dataItem?.monetaryInfo?.expectedPrice,
            dataItem?.monetaryInfo?.currency || defaultCurrency
          ) +
          ')'
          : '',
      },
      {
        var: '#Brokerage#',
        text: `${dataItem?.monetaryInfo?.brokerage
          ? dataItem?.monetaryInfo?.brokerage
          : ''
          } ${dataItem?.monetaryInfo?.brokerageCurrency
            ? dataItem?.monetaryInfo?.brokerageCurrency
            : ''
          }`,
      },
      { var: '#Notes#', text: dataItem?.notes ? dataItem?.notes : '' },
      {
        var: '#AboutProperty#',
        text: dataItem?.aboutProperty ? dataItem?.aboutProperty : '',
      },
      {
        var: '#Address#',
        text: dataItem?.address
          ? getLocationDetailsByObj(dataItem?.address)
          : '',
      },
      {
        var: '#OwnerName#',
        text:
          dataItem?.propertyOwnerDetails?.length && canViewOwnerDetails
            ? dataItem?.propertyOwnerDetails?.filter((owner: any) => owner.name?.trim())?.map((owner: any) => owner.name).join(', ')
            : '',
      },
      {
        var: '#OwnerPhone#',
        text:
          dataItem?.propertyOwnerDetails?.length && canViewOwnerDetails
            ? dataItem?.propertyOwnerDetails?.filter((owner: any) => owner.phone?.trim()).map((owner: any) => owner.phone).join(', ')
            : '',
      },
      {
        var: '#OwnerEmail#',
        text:
          dataItem?.propertyOwnerDetails?.length && canViewOwnerDetails
            ? dataItem?.propertyOwnerDetails?.filter((owner: any) => owner.email?.trim())?.map((owner: any) => owner.email).join(', ')
            : '',
      },
      {
        var: '#TotalFloors#',
        text: transformedAttributes['numberOfFloors']
          ? transformedAttributes['numberOfFloors']
          : '',
      },
      {
        var: '#FloorNumber#',
        text: transformedAttributes['floorNumber']
          ? transformedAttributes['floorNumber']
          : '',
      },
      {
        var: '#NoOfBathRooms#',
        text: transformedAttributes['numberOfBathrooms']
          ? transformedAttributes['numberOfBathrooms']
          : '',
      },
      {
        var: '#NoOfBedRooms#',
        text: transformedAttributes['numberOfBedrooms']
          ? transformedAttributes['numberOfBedrooms']
          : '',
      },
      {
        var: '#NoOfKitchens#',
        text: transformedAttributes['numberOfKitchens']
          ? transformedAttributes['numberOfKitchens']
          : '',
      },
      {
        var: '#NoOfLivingRooms#',
        text: transformedAttributes['numberOfLivingRooms']
          ? transformedAttributes['numberOfLivingRooms']
          : '',
      },
      {
        var: '#NoOfUtilities#',
        text: transformedAttributes['numberOfUtilities']
          ? transformedAttributes['numberOfUtilities']
          : '',
      },
      {
        var: '#NoOfBalconies#',
        text: transformedAttributes['numberOfBalconies']
          ? transformedAttributes['numberOfBalconies']
          : '',
      },
      {
        var: '#NoofParking#',
        text: transformedAttributes['numberOfParking']
          ? transformedAttributes['numberOfParking']
          : '',
      },
      {
        var: '#FurnishStatus#',
        text: dataItem?.furnishStatus
          ? FurnishStatus[dataItem?.furnishStatus]
          : '',
      },
      {
        var: '#Facing#',
        text: dataItem?.facing ? Facing[dataItem?.facing] : '',
      },
      {
        var: '#MicrositeUrl#',
        text: getMSUrl(dataItem?.serialNo, false, true),
      },
      { var: '#Property URL#', text: propertyLinks },
      {
        var: '#userName#',
        text: `${userData?.firstName} ${userData?.lastName}`,
      },
      { var: '#userEmail#', text: userData?.email },
      { var: '#userMobile#', text: userData?.phoneNumber },
      { var: '#tenantName#', text: tenantName || '' },
      { var: '#LeadName#', text: dataItem?.leadName || '' },
    ];
    return processReplacements(msg, replacements);
  };

  if (Array.isArray(propertyData)) {
    propertyData?.forEach((dataItem: any, index: number) => {
      const msg = processPropertyData(dataItem, index);
      combinedMsg += index + 1 + '. ' + msg;
      if (index < propertyData.length - 1) {
        combinedMsg += '\n';
      }
    });
  } else {
    combinedMsg = processPropertyData(propertyData, 0);
  }

  if (header) {
    header = processReplacements(header, replacements);
  }

  if (footer) {
    footer = processReplacements(footer, replacements);
  }

  return combinedMsg
    ? (header ? header + '\n' : '') +
    combinedMsg +
    (footer ? '\n' + footer : '')
    : '';
};

export const ProjectTemplateMsg = (
  template: string,
  projectData: any,
  areaSizeUnits: any,
  tenantName: string,
  header: string | null,
  footer: string | null,
  unitInfo: any,
  key: string,
  userData?: any,
  currentDate?: any
) => {
  let combinedMsg = '';
  const transformedAttributes: any = {};
  let replacements: any = [];

  template = template?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });
  header = header?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });
  footer = footer?.replace(/#([\w\s-]+)#/g, (match: string) => {
    return (
      '#' +
      match
        .slice(1, -1)
        .toLowerCase()
        ?.replace(/[\s-]+/g, '') +
      '#'
    );
  });

  const normalizeVar = (variable: string) =>
    variable.toLowerCase()?.replace(/[\s-]+/g, '');

  const processReplacements = (msg: any, replacements: any) => {
    if (msg) {
      replacements.forEach((item: any) => {
        if (Array.isArray(item.var)) {
          item.var.forEach((variable: string) => {
            const normalizedVar = normalizeVar(variable);
            const replacementText =
              typeof item?.text === 'string' ? item.text.trim() : item.text;
            msg = msg?.replaceAll(normalizedVar, replacementText);
          });
        } else {
          const normalizedVar = normalizeVar(item.var);
          const replacementText =
            typeof item?.text === 'string' ? item.text.trim() : item.text;
          msg = msg?.replaceAll(normalizedVar, replacementText);
        }
      });
    }
    return msg;
  };

  const processProjectData = (dataItem: any, index: number) => {
    let msg = template;

    if (dataItem.attributes?.length) {
      for (const attribute of dataItem.attributes) {
        if (!attribute?.attributeName.startsWith('Is')) {
          transformedAttributes[attribute?.attributeName] = attribute.value;
        }
      }
    }

    function getValueBasedOnKey(property: any) {
      if (key === 'share-project' || key === 'share-matching-lead') {
        return unitInfo?.[0]?.[property] ? unitInfo?.[0]?.[property] : '';
      } else {
        return dataItem?.unitInfo?.[property] || dataItem?.[property] || '';
      }
    }
    let projectLinks = '';

    dataItem?.links?.map((link: any) => {
      projectLinks += link + ', ';
    });
    projectLinks = projectLinks.slice(0, -2);

    replacements = [
      {
        var: '#Project Status#',
        text:
          dataItem?.currentStatus == 1
            ? 'Sold'
            : PropertyStatus[dataItem?.status],
      },
      {
        var: '#ProjectType#',
        text: dataItem.projectType?.displayName
          ? dataItem.projectType?.displayName
          : '',
      },
      {
        var: '#ProjectSubType#',
        text: dataItem.projectType?.childType?.displayName
          ? dataItem.projectType?.childType?.displayName
          : '',
      },
      { var: '#Name#', text: dataItem?.name ? dataItem?.name : '' },
      {
        var: '#PossessionDate#',
        text: (() => {
          const possessionType = dataItem?.possesionType;
          const possessionDate = dataItem?.possessionDate;
          if (possessionType && possessionType > 0 && PossessionType[possessionType]) {
            if (possessionType === PossessionType['Custom Date'] && possessionDate) {
              const possessionDateObj = new Date(possessionDate);
              const currentDateObj = new Date(currentDate);
              const isPast = possessionDateObj <= currentDateObj;
              return isPast
                ? 'Ready To Move'
                : getTimeZoneDate(
                  possessionDate,
                  userData?.timeZoneInfo?.baseUTcOffset,
                  'dayMonthYear'
                );
            }
            return PossessionType[possessionType];
          }
          return '';
        })(),
      },
      // {
      //   var: '#TotalPrice#',
      //   text: dataItem?.monetaryInfo?.expectedPrice
      //     ? dataItem?.monetaryInfo?.expectedPrice +
      //     ' (' +
      //     formatBudget(
      //       dataItem?.monetaryInfo?.expectedPrice,
      //       dataItem?.monetaryInfo?.currency
      //     ) +
      //     ')'
      //     : '',
      // },
      {
        var: '#Brokerage#',
        text: `${dataItem?.monetaryInfo?.brokerage
          ? dataItem?.monetaryInfo?.brokerage
          : ''
          } ${dataItem?.monetaryInfo?.brokerageCurrency
            ? dataItem?.monetaryInfo?.brokerageCurrency
            : ''
          }`,
      },
      {
        var: '#LandArea#',
        text: `${dataItem?.area ? dataItem?.area : ''} ${dataItem.areaUnitId
          ? getAreaUnit(dataItem.areaUnitId, areaSizeUnits)?.unit
          : ''
          }`,
      },
      {
        var: '#Address#',
        text: dataItem?.address
          ? getLocationDetailsByObj(dataItem?.address)
          : '',
      },
      {
        var: '#BuilderName#',
        text: dataItem?.builderDetail?.name
          ? dataItem?.builderDetail?.name
          : '',
      },
      {
        var: '#Notes#',
        text: dataItem?.notes ? dataItem?.notes : '',
      },
      {
        var: '#BuilderPhone#',
        text: dataItem?.builderDetail?.contactNo
          ? dataItem?.builderDetail?.contactNo
          : '',
      },
      {
        var: '#BuilderPointOfContact#',
        text: dataItem?.builderDetail?.pointOfContact
          ? dataItem?.builderDetail?.pointOfContact
          : '',
      },
      {
        var: '#TotalFloors#',
        text: transformedAttributes['numberOfFloors']
          ? transformedAttributes['numberOfFloors']
          : '',
      },
      {
        var: '#Facing#',
        text: dataItem?.facings?.length
          ? dataItem?.facings.map((index: number) => Facing[index]).join(', ')
          : '',
      },
      {
        var: '#UnitName#',
        text: getValueBasedOnKey('name'),
      },
      {
        var: '#UnitArea#',
        text: `${dataItem?.area ? dataItem?.area : ''} ${dataItem.areaUnitId
          ? getAreaUnit(dataItem.areaUnitId, areaSizeUnits)?.unit
          : ''
          }`,
      },
      {
        var: '#CarpetArea#',
        text: `${dataItem?.carpetArea ? dataItem?.carpetArea : ''} ${dataItem.carpetAreaUnitId
          ? getAreaUnit(dataItem.carpetAreaUnitId, areaSizeUnits)?.unit
          : ''
          }`,
      },
      {
        var: '#BuiltupArea#',
        text: `${dataItem?.buildUpArea ? dataItem?.buildUpArea : ''} ${dataItem.buildUpAreaId
          ? getAreaUnit(dataItem.buildUpAreaId, areaSizeUnits)?.unit
          : ''
          }`,
      },
      {
        var: '#SuperBuiltupArea#',
        text: `${dataItem?.superBuildUpArea ? dataItem?.superBuildUpArea : ''
          } ${dataItem.superBuildUpAreaUnit
            ? getAreaUnit(dataItem.superBuildUpAreaUnit, areaSizeUnits)?.unit
            : ''
          }`,
      },
      {
        var: '#MaintenanceCost#',
        text:
          getValueBasedOnKey('maintenanceCost') +
          '(' +
          formatBudget(
            getValueBasedOnKey('maintenanceCost'),
            getValueBasedOnKey('currency')
          ) +
          ')',
      },
      {
        var: '#PricePerUnit#',
        text:
          getValueBasedOnKey('pricePerUnit') +
          '(' +
          formatBudget(
            getValueBasedOnKey('pricePerUnit'),
            getValueBasedOnKey('currency')
          ) +
          ')',
      },
      {
        var: '#TotalPrice#',
        text:
          getValueBasedOnKey('price') +
          '(' +
          formatBudget(
            getValueBasedOnKey('price'),
            getValueBasedOnKey('currency')
          ) +
          ')',
      },
      {
        var: '#UnitType#',
        text: getValueBasedOnKey('unitType?.displayName'),
      },
      {
        var: '#UnitSubType#',
        text: getValueBasedOnKey('unitType?.childType?.displayName'),
      },
      {
        var: '#Bhk#',
        text:
          key !== 'share-project'
            ? dataItem?.unitInfo?.noOfBHK
              ? getBHKDisplayString(dataItem.unitInfo.noOfBHK)
              : getBHKDisplayString(dataItem?.noOfBHK)
            : dataItem?.unitInfo?.[0]?.noOfBHK
              ? getBHKDisplayString(dataItem.unitInfo[0].noOfBHK)
              : '',
      },
      {
        var: '#BhkType#',
        text:
          key === 'share-project'
            ? dataItem?.unitInfo?.bhkType
              ? BHKType[dataItem?.unitInfo?.bhkType]
              : BHKType[dataItem?.bhkType]
            : unitInfo?.[0]?.bhkType
              ? BHKType[unitInfo?.[0]?.bhkType]
              : '',
      },
      {
        var: '#Facing#',
        text:
          key !== 'share-project'
            ? dataItem?.unitInfo?.facings?.length
              ? dataItem.unitInfo.facings
                .map((index: number) => Facing[index])
                .join(', ')
              : ''
            : unitInfo?.[0]?.facings?.length
              ? unitInfo?.[0]?.facings
                .map((index: number) => Facing[index])
                .join(', ')
              : '',
      },
      {
        var: '#FurnishingStatus#',
        text:
          key !== 'share-project'
            ? dataItem?.unitInfo?.furnishingStatus !== undefined
              ? FurnishStatus[dataItem.unitInfo.furnishingStatus]
              : FurnishStatus[dataItem?.furnishingStatus]
            : unitInfo?.[0]?.furnishingStatus !== undefined
              ? FurnishStatus[unitInfo[0].furnishingStatus]
              : '',
      },
      {
        var: '#MicrositeUrl#',
        text: getMSUrl(dataItem?.serialNo, true),
      },
      { var: '#Project URL#', text: projectLinks },
      {
        var: '#userName#',
        text: `${userData?.firstName} ${userData?.lastName}`,
      },
      { var: '#userEmail#', text: userData?.email },
      { var: '#userMobile#', text: userData?.phoneNumber },
      { var: '#tenantName#', text: tenantName || '' },
      { var: '#LeadName#', text: dataItem?.leadName || '' },
    ];

    return processReplacements(msg, replacements);
  };
  if (Array.isArray(projectData)) {
    projectData?.forEach((dataItem: any, index: number) => {
      const msg = processProjectData(dataItem, index);
      combinedMsg += index + 1 + '. ' + msg;
      if (index < projectData.length - 1) {
        combinedMsg += '\n';
      }
    });
  } else {
    combinedMsg = processProjectData(projectData, 0);
  }

  if (header) {
    header = processReplacements(header, replacements);
  }

  if (footer) {
    footer = processReplacements(footer, replacements);
  }

  return combinedMsg
    ? (header ? header + '\n' : '') +
    combinedMsg +
    (footer ? '\n' + footer : '')
    : '';
};

export const getMSUrl = (
  serialNo: string,
  isProject: boolean = false,
  isListing: boolean = false
) => {
  const subDomain = getTenantName();
  const userName: any = JSON.parse(
    localStorage.getItem('userDetails')
  )?.preferred_username;

  let previewType = 'property-preview'; // default

  if (isProject) {
    previewType = 'project-preview';
  } else if (isListing) {
    previewType = 'listing-preview';
  }

  return `https://${subDomain + getEnvDetails()}/external/${previewType}/${userName}/${serialNo}`;
};

export const sortAssignedUsers = (assignedUser: any, allActiveUsers: any[]) => {
  const assignedUserIds = Array.isArray(assignedUser.value)
    ? assignedUser.value
    : [assignedUser.value];
  const assignedUsers: any[] = [];

  allActiveUsers.forEach((user) => {
    if (assignedUserIds.includes(user.id)) {
      if (user.firstName === 'You') {
        assignedUsers.unshift(user);
      } else {
        assignedUsers.push(user);
      }
    }
  });
  assignedUsers.sort((a, b) => {
    if (a.firstName === 'You') return -1;
    if (b.firstName === 'You') return 1;
    const nameA = `${a.firstName} ${a.lastName}`;
    const nameB = `${b.firstName} ${b.lastName}`;
    return nameA.localeCompare(nameB);
  });

  const unassignedUsers = allActiveUsers.filter(
    (user) => !assignedUserIds.includes(user.id)
  );
  unassignedUsers.sort((a, b) => {
    if (a.firstName === 'You') return -1;
    if (b.firstName === 'You') return 1;
    const nameA = `${a.firstName} ${a.lastName}`;
    const nameB = `${b.firstName} ${b.lastName}`;
    return nameA.localeCompare(nameB);
  });

  return assignedUsers.concat(unassignedUsers);
};

export const assignToSort = (
  allUserList: Array<Object>,
  assignedToUserId: string = '',
  fullName: boolean = false
) => {
  let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
  let loggedInUser,
    assignedUser,
    activeUsers: Array<Object> = [],
    inactiveUsers: Array<Object> = [],
    usersList: Array<Object> = [];

  allUserList?.map((user: any) => {
    if (user.id === userId) {
      loggedInUser = {
        ...user,
        ...(fullName ? {} : { firstName: 'You', lastName: '' }),
      };
    }
    if (user.id === assignedToUserId && user.id !== userId) {
      assignedUser = user;
    }
    if (user.id !== userId && user.id !== assignedToUserId && user.isActive)
      activeUsers.push(user);
    if (user.id !== userId && user.id !== assignedToUserId && !user.isActive)
      inactiveUsers.push(user);
  });

  if (assignedUser) usersList.push(assignedUser);
  if (loggedInUser) usersList.push(loggedInUser);

  return allUserList ? [...usersList, ...activeUsers, ...inactiveUsers] : [];
};

export const getTotalCountForReports = (items: any, statusList?: any) => {
  let total: any = {
    projectTitle: 'Total',
    firstName: 'Total',
    lastName: '',
    userName: 'Total',
    subSource: 'Total',
    agencyName: 'Total',
    source: 'Total',
    name: 'Total',
    allCount: 0,
    activeCount: 0,
    newCount: 0,
    totalCount: 0,
    pendingCount: 0,
    overdueCount: 0,
    callbackCount: 0,
    meetingScheduledCount: 0,
    siteVisitScheduledCount: 0,
    meetingDoneCount: 0,
    meetingNotDoneCount: 0,
    siteVisitDoneCount: 0,
    siteVisitNotDoneCount: 0,
    bookedCount: 0,
    notInterestedCount: 0,
    droppedCount: 0,
    meetingDoneUniqueCount: 0,
    meetingNotDoneUniqueCount: 0,
    siteVisitDoneUniqueCount: 0,
    siteVisitNotDoneUniqueCount: 0,
    averageWorkingHours: 0,
    callsInitiatedCount: 0,
    callsInitiatedLeadsCount: 0,
    whatsAppInitiatedCount: 0,
    whatsAppInitiatedLeadsCount: 0,
    emailsInitiatedCount: 0,
    emailsInitiatedLeadsCount: 0,
    smsInitiatedCount: 0,
    smsInitiatedLeadsCount: 0,
    statusEditsCount: 0,
    statusEditsLeadsCount: 0,
    formEditsCount: 0,
    formEditsLeadsCount: 0,
    notesAddedCount: 0,
    notesAddedLeadsCount: 0,
    callbackScheduledLeadsCount: 0,
    bookedLeadsCount: 0,
    notInterestedLeadsCount: 0,
    droppedLeadsCount: 0,
    hotLeadsCount: 0,
    warmLeadsCount: 0,
    coldLeadsCount: 0,
    escalatedLeadsCount: 0,
    highlightedLeadsCount: 0,
    aboutToConvertLeadsCount: 0,
    all: 0,
    active: 0,
    overdue: 0,
    callback: 0,
    busy: 0,
    toScheduleAMeeting: 0,
    followUp: 0,
    toScheduleSiteVisit: 0,
    planPostponed: 0,
    needMoreInfo: 0,
    notAnswered: 0,
    notReachable: 0,
    dropped: 0,
    notLooking: 0,
    ringingNotReceived: 0,
    wrongOrInvalidNo: 0,
    purchasedFromOthers: 0,
    meetingScheduled: 0,
    onCall: 0,
    online: 0,
    inPerson: 0,
    others: 0,
    notInterested: 0,
    differentLocation: 0,
    differentRequirements: 0,
    unmatchedBudget: 0,
    siteVisitScheduled: 0,
    firstVisit: 0,
    reVisit: 0,
    pending: 0,
    booked: 0,
    new: 0,
    notInterestedAfterMeetingDone: 0,
    notInterestedAfterSiteVisitDone: 0,
    droppedAfterMeetingDone: 0,
    droppedAfterSiteVisitDone: 0,
    incomingAnswered: 0,
    incomingMissed: 0,
    totalIncomingCalls: 0,
    outgoingAnswered: 0,
    outgoingNotConnected: 0,
    totalOutgoingCalls: 0,
    totalCalls: 0,
    totalTalkTime: 0,
    averageTalkTime: 0,
    maxTalkTime: 0,
    minTalkTime: 0,
    convertedDataCount: 0,
  };

  const calculateTotalForStatus = (
    items: any[],
    statusId: string,
    total: any
  ) => {
    items.forEach((item: any) => {
      const dataArray = (item?.data || []).filter(
        (data: any) => data?.statusId === statusId
      );
      const dataCount = dataArray.reduce(
        (sum: number, data: any) => sum + (data?.dataCount || 0),
        0
      );
      total[`${statusId}DataCount`] =
        (total[`${statusId}DataCount`] || 0) + dataCount;
    });
  };

  const calculateTotalForSubSource = (
    items: any[],
    statusId: string,
    total: any
  ) => {
    items.forEach((item: any) => {
      const dataArray = (item?.subSource || []).filter(
        (data: any) =>
          `${data?.subSource}(${data?.sourceName})` === statusId?.toString()
      );
      const dataCount = dataArray?.reduce(
        (sum: number, data: any) => sum + (data?.count || 0),
        0
      );
      total[`${statusId}DataCount`] =
        (total[`${statusId}DataCount`] || 0) + dataCount;
    });
  };

  const calculateTotalForSource = (
    items: any[],
    statusId: string,
    total: any
  ) => {
    items.forEach((item: any) => {
      const dataArray = (item?.source || []).filter(
        (data: any) => data?.displayName === statusId?.toString()
      );
      const dataCount = dataArray?.reduce(
        (sum: number, data: any) => sum + (data?.count || 0),
        0
      );
      total[`${statusId}DataCount`] =
        (total[`${statusId}DataCount`] || 0) + dataCount;
    });
  };
  statusList?.forEach((status: any) => {
    total[`${status}DataCount`] = 0;
    total[`${status}DataCount`] = 0;
    calculateTotalForSource(items, status, total);
    calculateTotalForSubSource(items, status, total);
  });

  statusList?.forEach((status: any) => {
    calculateTotalForStatus(items, status, total);
  });

  items?.map((item: any) => {
    let objArray = Object.entries(item);
    objArray.map((entry: any) => {
      if (typeof entry[1] === 'number') {
        if (!total?.[entry[0]]) {
          total[entry[0]] = 0;
        }
        total[entry[0]] += entry[1];
      } else if (
        entry[0] === 'averageWorkingHours' ||
        entry[0] === 'minTalkTime' ||
        entry[0] === 'maxTalkTime' ||
        entry[0] === 'averageTalkTime' ||
        entry[0] === 'totalTalkTime'
      ) {
        const workingHours = moment.duration(entry[1]);
        total[entry[0]] += workingHours.asSeconds();
      }
    });
  });

  const formatDuration = (seconds: number) => {
    const duration = moment.duration(seconds, 'seconds');
    const hours = Math.floor(duration?.asHours())?.toString()?.padStart(2, '0');
    const minutes = duration?.minutes()?.toString()?.padStart(2, '0');
    const secondsStr = duration?.seconds()?.toString()?.padStart(2, '0');
    return `${hours}:${minutes}:${secondsStr}`;
  };

  if (items?.length > 1) {
    [
      'averageWorkingHours',
      'minTalkTime',
      'maxTalkTime',
      'averageTalkTime',
      'totalTalkTime',
    ].forEach((field) => {
      if (total[field] !== undefined) {
        total[field] = formatDuration(total[field]);
      }
    });
    return [...items, total];
  }
  return items;
};

export const getDaysInMonth = (year: number, month: number) => {
  let date = new Date(Date.UTC(year, month, 1));
  let monthDays = [];
  while (date.getUTCMonth() === month) {
    monthDays.push(new Date(date));
    date.setUTCDate(date.getUTCDate() + 1);
  }
  return monthDays;
};

export const containsOnlyEmojis = (input: any): boolean => {
  const emojiRegex =
    /^(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|[\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|[\ud83c[\ude32-\ude3a]|[\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])+$/;

  return emojiRegex.test(input);
};

export const getLocationDetailsByObj = (object: any): string => {
  const {
    subLocality,
    locality,
    subCommunity,
    community,
    towerName,
    city,
    district,
    state,
    country,
    postalCode,
  } = object || {};
  const addressParts = [
    subLocality,
    locality && locality !== subLocality && locality,
    city && city !== subLocality && city !== locality && city,
    subCommunity,
    community,
    towerName,
    district,
    state,
    country,
    postalCode,
  ]
    .filter((part) => part)
    .map((part) => part.trim())
    .join(', ');
  if (addressParts.trim().endsWith(',')) {
    return addressParts.trim().slice(0, -1);
  }
  return addressParts || '';
};

export const getLocalityDetailsByObj = (obj: any) => {
  if (!obj) {
    return null;
  }
  const subLocality = obj.subLocality;
  const locality = obj.locality;
  const city = obj.city;
  const enquiredLocComponents = [];

  if (subLocality !== locality) enquiredLocComponents.push(subLocality);
  if (locality !== city) enquiredLocComponents.push(locality);

  let enquiredLoc = enquiredLocComponents.join(', ');
  enquiredLoc = enquiredLoc.trim().endsWith(',')
    ? enquiredLoc.trim().slice(0, -1)
    : enquiredLoc;

  return enquiredLoc || null;
};

/**
 * A function to check if a string contains
 * any element in a given array
 * @returns boolean
 */
export const isStringSubsetInArray = (
  inputString: string,
  arrayOfStrings: string[]
): boolean => {
  for (let i = 0; i < arrayOfStrings.length; i++) {
    if (inputString.includes(arrayOfStrings[i])) {
      return true;
    }
  }
  return false;
};

/**
 * validator function to check if selected date with time is lesser than current date and time
 * @returns
 */
export const validateScheduleTime = (currentDate: any): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    const selectedTime = new Date(control?.value);
    selectedTime.setSeconds(0, 0);

    if (!control?.value) {
      return { required: true };
    }

    const currentTime = new Date(currentDate);
    currentTime.setSeconds(0, 0);

    if (currentTime >= selectedTime) {
      return { invalidTime: true };
    }

    return null;
  };
};

export const getBHKDisplayString = (
  bhkNo: string,
  isBHKText: boolean = false
): string => {
  const displayBHKNo = bhkNo == '0.5' ? '1' : bhkNo;
  const displayType = bhkNo == '0.5' ? 'RK' : 'BHK';
  return !isBHKText ? `${displayBHKNo} ${displayType}` : `${displayBHKNo}`;
};

export const getBRDisplayString = (
  brNo: string,
  isBRText: boolean = false
): string => {
  if (brNo == '0.5') {
    return 'Studio';
  }
  return isBRText ? `${brNo}` : `${brNo} BR`;
};

//History
export const getBRDisplay = (brNo: string): string => {
  if (!brNo) return '';

  const brNumbers = brNo
    .split(',')
    .map((n) => parseFloat(n.trim()))
    .filter((n) => !isNaN(n));

  return brNumbers.length > 0
    ? brNumbers.map((br) => `${br} BR`).join(', ')
    : '';
};

export const getBedsDisplay = (bedNo: string): string => {
  if (!bedNo) return '';
  const bedNumbers = bedNo?.split(',')?.map((n) => parseFloat(n.trim()));

  const formattedBeds = bedNumbers?.map((bed) => {
    if (bed === 0) {
      return 'Studio';
    } else {
      return bed?.toString();
    }
  });

  return formattedBeds?.join(', ') || '';
};

//History
export const getBHKDisplay = (bhkNo: string): string => {
  if (!bhkNo) return '';
  const bhkNumbers = bhkNo?.split(',')?.map((n) => parseFloat(n.trim()));

  const formattedBHKs = bhkNumbers?.map((bhk) => {
    if (bhk === 0.5) {
      return '1RK';
    } else if (bhk) {
      return `${bhk} BHK`;
    } else {
      return bhk?.toString();
    }
  });

  return formattedBHKs?.join(', ') || '';
};

export const generateLeadSourcesArray = (): Array<string> => {
  const leadSourcesArray: Array<string> = Object.keys(LeadSource)
    .slice(44)
    .sort();
  return leadSourcesArray;
};

export const snakeToCamel = (snakeCase: string): string => {
  if (!snakeCase) return snakeCase;

  const components = snakeCase.split('_');
  const camelCaseString =
    components[0] +
    components
      .slice(1)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');

  return camelCaseString;
};

export const getDateRange = (range: DateRange, currentDate: Date): Date[] => {
  const today = new Date(currentDate);
  let startDate: Date;
  let endDate: Date;

  switch (range) {
    case DateRange.Today:
      startDate = new Date(today);
      endDate = new Date(today);
      break;

    case DateRange.Yesterday:
      startDate = new Date(today);
      startDate.setDate(today.getDate() - 1);
      endDate = new Date(today);
      endDate.setDate(today.getDate() - 1);
      break;

    case DateRange.Last7Days:
      startDate = new Date(today);
      startDate.setDate(today.getDate() - 6);
      endDate = new Date(today);
      break;

    case DateRange.CurrentMonth:
      startDate = new Date(today.getFullYear(), today.getMonth(), 1);
      endDate = new Date(today);
      break;

    case DateRange.TillDate:
      startDate = null;
      endDate = new Date(today);
      break;

    default:
      startDate = new Date(today);
      endDate = new Date(today);
      break;
  }
  return [startDate, endDate];
};

export const hexToRgba = (hex: string, opacity: number) => {
  hex = hex?.replace('#', '');
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);
  const validOpacity = Math.min(1, Math.max(0, opacity));
  return `rgba(${r}, ${g}, ${b}, ${validOpacity})`;
};

export const atLeastTwoSelectedValidator: ValidatorFn = (
  control: AbstractControl
): ValidationErrors | null => {
  const teamUsers = control.value;
  if (!teamUsers || teamUsers.length < 2) {
    return { atLeastTwoSelected: true };
  }
  return null;
};

export function onFilterChanged(event: any) {
  const nodes: any = [];
  event.api.forEachNodeAfterFilter((node: any) => {
    if (node.isSelected()) {
      nodes.push(node);
    }
  });
  event.api.getSelectedNodes().forEach((node: any) => {
    if (!nodes.includes(node)) {
      node.setSelected(false);
    }
  });
}

export function isUrl(str: string): boolean {
  const urlPattern = new RegExp(
    '^(http(s)?:\\/\\/)?' + // http:// or https:// (optional)
    '(www\\.)?' + // www. (optional)
    '[a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b' + // domain.tld
    '([-a-zA-Z0-9@:%_\\+.~#?&//=]*)$', // path (optional)
    'i' // Case-insensitive flag
  );

  return urlPattern.test(str);
}

export function convertUrlsToLinks(
  text: string,
  applyLinkColor: boolean = false
): string {
  if (!text) return '';
  const urlRegex =
    /(\b(?:https?:\/\/|www\.|[a-zA-Z0-9-]+\.[a-z]{2,})(?:[^\s]*))/g;
  return text.replace(urlRegex, (url) => {
    let clickableUrl = url;
    if (
      !url.startsWith('http://') &&
      !url.startsWith('https://') &&
      !url.startsWith('www.')
    ) {
      clickableUrl = `http://${url}`;
    }
    return applyLinkColor
      ? `<a href="${clickableUrl}" target="_blank" class="text-accent-green">${url}</a>`
      : `<a href="${clickableUrl}" target="_blank">${url}</a>`;
  });
}

export const atLeastOneSelectedValidator: ValidatorFn = (
  control: AbstractControl
): ValidationErrors | null => {
  const controlValue = control.value;
  if (!controlValue || controlValue.length < 1) {
    return { atLeastOneSelected: true };
  }
  return null;
};

export function generateFloorOptions(): string[] {
  const floors = ['Upper Basement', 'Lower Basement', 'Ground'];
  for (let i = 1; i <= 200; i++) {
    floors.push(i.toString());
  }
  return floors;
}

export function buildHttpParams(payload: any): any {
  let params = new HttpParams();

  Object.entries(payload).forEach(([key, value]: any) => {
    if (value || value === 0) {
      if (Array.isArray(value)) {
        const indexes = getIndexes(key, value);
        indexes.forEach((element: any) => {
          params = params.append(key, element);
        });
      } else {
        params = params.set(key, value);
      }
    }
  });

  return params;
}

export function generateEnumList(
  enumData: any,
  displayNames?: string[]
): Array<any> {
  return Object.keys(enumData)
    .filter((key) => isNaN(Number(key)) && key !== 'None')
    .map((key, index) => ({
      enumValue: enumData[key],
      name: key,
      displayName: displayNames?.[index] || key,
    }));
}

export function getFormattedLocation(location: string): string {
  if (!location) {
    return '';
  }
  location = location.replace(/^,+/, '');
  location = location.replace(/,+/g, ',');
  return location.trim();
}

export function allowLandlineInput(event: KeyboardEvent) {
  const allowed = /[0-9\-]/;
  if (!allowed.test(event.key)) {
    event.preventDefault();
  }
}


export async function handleCachedData(
  storeName: string,
  cacheKey: string,
  fetchLastModified: () => Promise<string | null>,
  fetchData: () => Promise<any>,
  buildRecord: (data: any, lastModified: string | null) => any,
  getItems: (data: any, isLocalData: boolean) => any[],
): Promise<any[]> {
  const dbName = 'CachingDb';
  let db: IDBDatabase | null = null;
  const idToken = localStorage.getItem('idToken');
  if (!idToken || !('indexedDB' in window)) {
    const fresh = await fetchData();
    db?.close();
    return getItems(fresh, false);
  }
  try {
    db = await new Promise((resolve, reject) => {
      const request = indexedDB.open(dbName);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });

    if (!db.objectStoreNames.contains(storeName)) {
      const fresh = await fetchData();
      db?.close();
      return getItems(fresh, false);
    }

    const localData = await new Promise<any>((resolve) => {
      try {
        const tx = db!.transaction(storeName, 'readonly');
        const store = tx.objectStore(storeName);
        const getReq = store.get(cacheKey);
        getReq.onsuccess = () => resolve(getReq.result || null);
        getReq.onerror = () => resolve(null);
      } catch (txErr) {
        console.error(`[Cache] Transaction error reading from ${storeName}:`, txErr);
        resolve(null);
      }
    });

    const localLastModified = localData?.lastModified || null;

    let serverLastModified: string | null = null;
    try {
      serverLastModified = await fetchLastModified();
    } catch (err) {
      console.error(`[Cache] Failed to fetch server lastModified:`, err);
      if (db) db.close();

      const fresh = await fetchData();
      return getItems(fresh, false);
    }

    if (!serverLastModified || serverLastModified !== localLastModified) {
      const fresh = await fetchData();

      try {
        await new Promise<void>((resolve, reject) => {
          const tx = db!.transaction(storeName, 'readwrite');
          const store = tx.objectStore(storeName);
          const newRecord = buildRecord(fresh, serverLastModified);
          if (fresh && serverLastModified) {
            store.put(newRecord);
          }
          tx.oncomplete = () => resolve();
          tx.onerror = () => reject(tx.error);
        });
      } catch (updateErr) {
        console.error(`[Cache] Failed to update cache for ${storeName}:`, updateErr);
      }

      if (db) db.close();
      return getItems(fresh, false);

    } else {
      if (db) db.close();
      return getItems(localData, true);
    }

  } catch (err) {
    console.error(`[Cache] DB error for ${storeName}:`, err);
    if (db) db.close();

    try {
      const fresh = await fetchData();
      return getItems(fresh, false);
    } catch (fetchErr) {
      console.error(`[Cache] Failed to fetch fresh data:`, fetchErr);
      throw fetchErr;
    }
  }
}