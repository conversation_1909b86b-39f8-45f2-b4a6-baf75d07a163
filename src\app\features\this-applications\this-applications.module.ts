import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';

import { HttpLoaderFactory } from 'src/app/app.imports';
import { SharedModule } from 'src/app/shared/shared.module';
import { ThisApplicationsRoutingModule } from './this-applications-routing.module';
import { ThisApplicationsComponent } from './this-applications.component';

@NgModule({
  declarations: [
    ThisApplicationsComponent,
    // InViewDirective
  ],
  imports: [
    CommonModule,
    ThisApplicationsRoutingModule,
    SharedModule,
    GoogleMapsModule,
    NgxMatIntlTelInputComponent,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    LottieModule,
    FormsModule,
    ReactiveFormsModule,
    DragScrollModule,
  ],
  exports: [DragScrollModule],
})
export class ThisApplicationsModule { }
