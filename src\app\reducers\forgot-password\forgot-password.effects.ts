import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import {
  ChangePassword,
  ForgotPasswordActions,
  GetOtp,
  ResetPassword,
  VerifyOtp,
  VerifyOtpSuccess,
  VerifyUsername,
  VerifyUsernameSuccess,
} from 'src/app/reducers/forgot-password/forgot-password.actions';
import { ForgotPasswordService } from 'src/app/services/controllers/forgot-password.service';

@Injectable()
export class ForgotPasswordEffects {
  verifyUsername$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ForgotPasswordActions.VERIFY_USERNAME),
      map((action: VerifyUsername) => action),
      switchMap((action: VerifyUsername) => {
        return this.api.verifyUserName(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new VerifyUsernameSuccess(resp.data);
            }
            let error;
            this.translate.get('AUTH.valid-username').subscribe((res: string) => error = res);
            this._notificationService.error((error));
            return new VerifyUsernameSuccess();
          }),
          catchError((err) => {
            if (action.payload) {
              this._notificationService.error('Enter a valid username');
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  verifyOtp$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ForgotPasswordActions.VERIFY_OTP),
      map((action: VerifyOtp) => action),
      switchMap((action: VerifyOtp) => {
        return this.api.otpVerification(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (!resp.data) {
                this._notificationService.error('Enter a valid otp');
              }
              return new VerifyOtpSuccess(resp.data);
            }
            return new VerifyOtpSuccess();
          }),
          catchError((err) => {
            this._notificationService.error('Enter a valid otp');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getOtp$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ForgotPasswordActions.GET_OTP),
      map((action: GetOtp) => action),
      switchMap((action: GetOtp) => {
        return this.api.generateOtp(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp.message);
              return;
            }
            return null;
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  resetPassword$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ForgotPasswordActions.RESET_PASSWORD),
      map((action: ResetPassword) => action),
      switchMap((action: ResetPassword) => {
        return this.api.resetPassword(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Password changed successfully'
              );
              this.router.navigate(['/login'], { queryParams: { passwordReset: 'true' } });
              return;
            }
            return null;
          }),
          catchError((err) => {
            this._notificationService.error('Password is not valid');
            return of(new OnError(err));
          })
        );
      })
    )
  );

  changePassword$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ForgotPasswordActions.CHANGE_PASSWORD),
      map((action: ChangePassword) => action),
      switchMap((action: ChangePassword) => {
        return this.api.changePassword(action.payload, action.userId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp.message);
              localStorage.clear();
              location.href = '/login';
              return;
            }
            return null;
          }),
          catchError((err) => {
            this._notificationService.error(err?.error?.messages?.[0]);
            return of(new OnError(err));
          })
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private _notificationService: NotificationsService,
    private api: ForgotPasswordService,
    private router: Router,
    private translate: TranslateService,
  ) { }
}
