import { Action } from '@ngrx/store';
import { FetchResponse } from '../lead/lead.reducer';
export enum DataReportsActionTypes {
    FETCH_DATA_REPORTS_USER = '[DATA_REPORTS] Fetch User',
    FETCH_DATA_REPORTS_USER_SUCCESS = '[DATA_REPORTS] Fetch Data User Success',
    UPDATE_DATA_USER_FILTER_PAYLOAD = '[DATA_REPORTS] Fetch Data User Filter',
    FETCH_DATA_REPORTS_USER_TOTAL_COUNT = '[DATA_REPORTS] Fetch Data User Total Count',
    FETCH_DATA_REPORTS_USER_TOTAL_COUNT_SUCCESS = '[DATA_REPORTS] Fetch Data User Total Count Success',
    FETCH_DATA_REPORTS_USER_EXPORT = '[DATA_REPORTS] Fetch Data User Export',
    FETCH_DATA_REPORTS_USER_EXPORT_SUCCESS = '[DATA_REPORTS] Fetch Data User Export Success',
    EXPORT_DATA_USER_STATUS = '[DATA_REPORTS] Fetch Data User Status',
    EXPORT_DATA_USER_STATUS_SUCCESS = '[DATA_REPORTS] Fetch Data User Status Success',

    FETCH_DATA_REPORTS_PROJECT = '[DATA_REPORTS] Fetch Project',
    FETCH_DATA_REPORTS_PROJECT_SUCCESS = '[DATA_REPORTS] Fetch Data Project Success',
    UPDATE_DATA_PROJECT_FILTER_PAYLOAD = '[DATA_REPORTS] Fetch Data Project Filter',
    FETCH_DATA_REPORTS_PROJECT_TOTAL_COUNT = '[DATA_REPORTS] Fetch Data Project Total Count',
    FETCH_DATA_REPORTS_PROJECT_TOTAL_COUNT_SUCCESS = '[DATA_REPORTS] Fetch Data Project Total Count Success',
    FETCH_DATA_REPORTS_PROJECT_EXPORT = '[DATA_REPORTS] Fetch Data Project Export',
    FETCH_DATA_REPORTS_PROJECT_EXPORT_SUCCESS = '[DATA_REPORTS] Fetch Data Project Export Success',
    EXPORT_DATA_PROJECT_STATUS = '[DATA_REPORTS] Fetch Data Project Status',
    EXPORT_DATA_PROJECT_STATUS_SUCCESS = '[DATA_REPORTS] Fetch Data Project Status Success',

    FETCH_DATA_REPORTS_SOURCE = "[DATA_REPORTS] Fetch Source",
    FETCH_DATA_REPORTS_SOURCE_SUCCESS = "[DATA_REPORTS] Fetch Data Source Success",
    UPDATE_DATA_SOURCE_FILTER_PAYLOAD = "[DATA_REPORTS] Fetch Data Source Filter",
    FETCH_DATA_REPORTS_SOURCE_TOTAL_COUNT = "[DATA_REPORTS] Fetch Data Source Total Count",
    FETCH_DATA_REPORTS_SOURCE_TOTAL_COUNT_SUCCESS = "[DATA_REPORTS] Fetch Data Source Total Count Success",
    FETCH_DATA_REPORTS_SOURCE_EXPORT = "[DATA_REPORTS] Fetch Data Source Export",
    FETCH_DATA_REPORTS_SOURCE_EXPORT_SUCCESS = "[DATA_REPORTS] Fetch Data Source Export Success",
    EXPORT_DATA_SOURCE_STATUS = "[DATA_REPORTS] Fetch Data Source Status",
    EXPORT_DATA_SOURCE_STATUS_SUCCESS = "[DATA_REPORTS] Fetch Data Source Status Success",

    FETCH_DATA_REPORTS_SUB_SOURCE = "[DATA_REPORTS] Fetch Sub Source",
    FETCH_DATA_REPORTS_SUB_SOURCE_SUCCESS = "[DATA_REPORTS] Fetch Data Sub Source Success",
    FETCH_DATA_REPORTS_SUB_SOURCE_TOTAL_COUNT = "[DATA_REPORTS] Fetch Data Sub Source Total Count",
    FETCH_DATA_REPORTS_SUB_SOURCE_TOTAL_COUNT_SUCCESS = "[DATA_REPORTS] Fetch Data Sub Source Total Count Success",
    FETCH_DATA_REPORTS_SUB_SOURCE_EXPORT = "[DATA_REPORTS] Fetch Data Sub Source Export",
    FETCH_DATA_REPORTS_SUB_SOURCE_EXPORT_SUCCESS = "[DATA_REPORTS] Fetch Data Sub Source Export Success",
    UPDATE_DATA_SUB_SOURCE_FILTER_PAYLOAD = "[DATA_REPORTS] Fetch Data Sub Source Filter",
    EXPORT_DATA_SUB_SOURCE_STATUS = "[DATA_REPORTS] Fetch Data Sub Source Status",
    EXPORT_DATA_SUB_SOURCE_STATUS_SUCCESS = "[DATA_REPORTS] Fetch Data Sub Source Status Success",

    FETCH_DATA_REPORTS_CALL = "[DATA_REPORTS] Fetch Call",
    FETCH_DATA_REPORTS_CALL_SUCCESS = "[DATA_REPORTS] Fetch Data Call Success",
    FETCH_DATA_REPORTS_CALL_TOTAL_COUNT = "[DATA_REPORTS] Fetch Data Call Total Count",
    FETCH_DATA_REPORTS_CALL_TOTAL_COUNT_SUCCESS = "[DATA_REPORTS] Fetch Data Call Total Count Success",
    FETCH_DATA_REPORTS_CALL_EXPORT = "[DATA_REPORTS] Fetch Data Call Export",
    FETCH_DATA_REPORTS_CALL_EXPORT_SUCCESS = "[DATA_REPORTS] Fetch Data Call Export Success",
    UPDATE_DATA_CALL_FILTER_PAYLOAD = "[DATA_REPORTS] Fetch Data Call Filter",
    EXPORT_DATA_CALL = "[DATA_REPORTS] Fetch Data Call Status",
    EXPORT_DATA_CALL_SUCCESS = "[DATA_REPORTS] Fetch Data Call Status Success",

    FETCH_DATA_ACTIVITY_REPORTS = "[DATA_REPORTS] Fetch Data Activity",
    FETCH_DATA_ACTIVITY_REPORTS_SUCCESS = "[DATA_REPORTS] Fetch Data Activity Success",
    FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS = "[DATA_REPORTS] Fetch Data Activity Communication",
    FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS_SUCCESS = "[DATA_REPORTS] Fetch Data Activity Communication Success",
    FETCH_DATA_ACTIVITY_REPORTS_TOTAL_COUNT = "[DATA_REPORTS] Fetch Data Activity Reports Total Count",
    FETCH_DATA_ACTIVITY_REPORTS_TOTAL_COUNT_SUCCESS = "[DATA_REPORTS] Fetch Data Activity Reports Total Count Success",
    UPDATE_DATA_ACTIVITY_FILTER_PAYLOAD = "[DATA_REPORTS] Fetch Data Activity Filter",
    FETCH_DATA_ACTIVITY_REPORTS_EXPORT = "[DATA_REPORTS] Fetch Data Activity Export",
    FETCH_DATA_ACTIVITY_REPORTS_EXPORT_SUCCESS = "[DATA_REPORTS] Fetch Data Activity Export Success",
    EXPORT_DATA_ACTIVITY = "[DATA_REPORTS] Data Export Activity through excel",
    EXPORT_DATA_ACTIVITY_SUCCESS = "[DATA_REPORTS] Data Export Activity through excel Success"
}

export class FetchDataReportsUser implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_USER;
    constructor() { }
}
export class FetchDataReportsUserSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_USER_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class UpdateDataUserFilterPayload implements Action {
    readonly type: string = DataReportsActionTypes.UPDATE_DATA_USER_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}

export class FetchDataReportsUserTotalCount implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_USER_TOTAL_COUNT;
    constructor() { }
}

export class FetchDataReportsUserTotalCountSuccess implements Action {
    readonly type: string =
        DataReportsActionTypes.FETCH_DATA_REPORTS_USER_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchDataUserExport implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_USER_EXPORT;
    constructor(public payload: any) { }
}
export class FetchDataUserExportSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_USER_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class ExportDataUserStatus implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_USER_STATUS;
    constructor(public payload: any) { }
}
export class ExportDataUserStatusSuccess implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_USER_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}
// project

export class FetchDataReportsProject implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT;
    constructor() { }
}
export class FetchDataReportsProjectSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class UpdateDataProjectFilterPayload implements Action {
    readonly type: string = DataReportsActionTypes.UPDATE_DATA_PROJECT_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}

export class FetchDataReportsProjectTotalCount implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_TOTAL_COUNT;
    constructor() { }
}

export class FetchDataReportsProjectTotalCountSuccess implements Action {
    readonly type: string =
        DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchDataProjectExport implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_EXPORT;
    constructor(public payload: any) { }
}
export class FetchDataProjectExportSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}


export class ExportDataProjectStatus implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_PROJECT_STATUS;
    constructor(public payload: any) { }
}
export class ExportDataProjectStatusSuccess implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_PROJECT_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

//souce

export class FetchDataReportsSource implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE;
    constructor() { }
}
export class FetchDataReportsSourceSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class UpdateDataSourceFilterPayload implements Action {
    readonly type: string = DataReportsActionTypes.UPDATE_DATA_SOURCE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}

export class FetchDataReportsSourceTotalCount implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_TOTAL_COUNT;
    constructor() { }
}

export class FetchDataReportsSourceTotalCountSuccess implements Action {
    readonly type: string =
        DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchDataSourceExport implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchDataSourceExportSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}


export class ExportDataSourceStatus implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_SOURCE_STATUS;
    constructor(public payload: any) { }
}
export class ExportDataSourceStatusSuccess implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_SOURCE_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

//subsource
export class FetchDataReportsSubSource implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE;
    constructor() { }
}
export class FetchDataReportsSubSourceSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class UpdateDataSubSourceFilterPayload implements Action {
    readonly type: string = DataReportsActionTypes.UPDATE_DATA_SUB_SOURCE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}

export class FetchDataReportsSubSourceTotalCount implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_TOTAL_COUNT;
    constructor() { }
}

export class FetchDataReportsSubSourceTotalCountSuccess implements Action {
    readonly type: string =
        DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchDataSubSourceExport implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchDataSubSourceExportSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class ExportDataSubSourceStatus implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_SUB_SOURCE_STATUS;
    constructor(public payload: any) { }
}
export class ExportDataSubSourceStatusSuccess implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_SUB_SOURCE_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

//call

export class FetchDataReportsCall implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_CALL;
    constructor() { }
}
export class FetchDataReportsCallSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class UpdateDataCallFilterPayload implements Action {
    readonly type: string = DataReportsActionTypes.UPDATE_DATA_CALL_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}

export class FetchDataReportsCallTotalCount implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_TOTAL_COUNT;
    constructor() { }
}

export class FetchDataReportsCallTotalCountSuccess implements Action {
    readonly type: string =
        DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchDataCallExport implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_EXPORT;
    constructor(public payload: any) { }
}
export class FetchDataCallExportSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class ExportDataCall implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_CALL;
    constructor(public payload: any) { }
}
export class ExportDataCallSuccess implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_CALL_SUCCESS;
    constructor(public resp: string = '') { }
}

//activity

export class FetchDataReportsActivityCommunication implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS;
    constructor(public section: String = '' ) { }
}
export class FetchDataReportsActivityCommunicationSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class FetchDataReportsActivity implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS;
    constructor() { }
}
export class FetchDataReportsActivitySuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class FetchDataReportsActivityTotalCount implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_TOTAL_COUNT;
    constructor() { }
}

export class FetchDataReportsActivityTotalCountSuccess implements Action {
    readonly type: string =
        DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class UpdateDataActivityFilterPayload implements Action {
    readonly type: string = DataReportsActionTypes.UPDATE_DATA_ACTIVITY_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}

export class FetchDataActivityExport implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_EXPORT;
    constructor(public payload: any) { }
}
export class FetchDataActivityExportSuccess implements Action {
    readonly type: string = DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class ExportDataActivity implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_ACTIVITY;
    constructor(public payload: any) { }
}
export class ExportDataActivitySuccess implements Action {
    readonly type: string = DataReportsActionTypes.EXPORT_DATA_ACTIVITY_SUCCESS;
    constructor(public resp: string = '') { }
}