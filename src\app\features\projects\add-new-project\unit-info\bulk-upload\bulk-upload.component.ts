import { Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { PROJECT_UNIT_EXCEL_TEMPLATE, ProjectUnitDataColumns, ProjectUnitDisplayColumns, ProjectUnitReqColumns } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, getAppName, getSystemTimeOffset, getSystemTimeZoneId, validateAllFormFields } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchCustomFormField } from 'src/app/reducers/custom-form/custom-form.action';
import { getCustomFormField } from 'src/app/reducers/custom-form/custom-form.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectUnitExcelUploadedList, ProjectUnitExcelUpload, UploadMappedColumns } from 'src/app/reducers/project/project.action';
import { getColumnHeadings } from 'src/app/reducers/project/project.reducer';
import { FetchAdminsAndReportees, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
@Component({
  selector: 'bulk-upload',
  templateUrl: './bulk-upload.component.html',
})
export class BulkUploadComponent implements OnInit, OnDestroy {
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  excelTemplatePath: string = PROJECT_UNIT_EXCEL_TEMPLATE;
  projectUnitData: any;
  unitData = ProjectUnitReqColumns;
  projectUnitMappingForm: FormGroup;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  projectId: number;
  selectedUserId: string[] = [];
  selectedFile: File;
  count: any;
  formKeys: Array<string> = [];
  allActiveUsers: any[] = [];
  canAssignToAny: boolean = false;
  activeUsers: any[];
  s3BucketKey: string;
  notUploadedLeadsExcelPath: string;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  userData: any;
  getAppName = getAppName;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private sharedDataService: ShareDataService,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title
  ) {
    this.metaTitle.setTitle('CRM | Import Project Units');
    this.headerTitle.setLangTitle('Import Project Units');
    this.projectUnitMappingForm = this.fb.group({
      name: [null, Validators.required],
      unitArea: [null, Validators.required],
      areaUnit: [null, Validators.required],
      carpetArea: [null],
      carpetAreaUnit: [null],
      builtupArea: [null],
      builtupAreaUnit: [null],
      superBuiltupArea: [null],
      superBuiltupAreaUnit: [null],
      currency: [null],
      pricePerUnit: [null],
      totalPrice: [null],
      unitType: [null],
      unitSubType: [null],
      noOfBhk: [null],
      bhkType: [null],
      facing: [null],
      furnishingStatus: [null],
      maintenanceCost: [null],
      numberOfBalconies: [null],
      numberOfBathrooms: [null],
      numberOfDrawingOrLivingRooms: [null],
      numberOfBedrooms: [null],
      numberOfUtilities: [null],
      numberOfKitchens: [null],
      maximumOccupants: [null],
    });
  }

  ngOnInit() {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });

    this.sharedDataService.projectId$.subscribe((data: any) => {
      this.projectId = data;
    });
    this._store.dispatch(new FetchCustomFormField());
    this._store
      .select(getCustomFormField)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        debugger
        this.projectUnitData = data;
      });
    // this.navigateToProjects()
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  onAutoMapChange() {
    const mappingColumns = ProjectUnitDisplayColumns;
    const mappingReqColumns = ProjectUnitReqColumns;
    const form = this.projectUnitMappingForm;

    mappingColumns?.forEach(column => {
      if (column?.displayName && form?.controls[column?.controlName] && this.formKeys?.includes(column?.displayName)) {
        form.patchValue({
          [column?.controlName]: column?.displayName
        });
      }
    });
    mappingReqColumns?.forEach(column => {
      if (column?.displayName && form?.controls[column?.controlName] && this.formKeys?.includes(column?.displayName)) {
        form.patchValue({
          [column?.controlName]: column?.displayName
        });
      }
    });
  }

  uploadFile() {
    this._store.dispatch(new ProjectUnitExcelUpload(this.selectedFile));
    this._store.select(getColumnHeadings).subscribe((data: any) => {
      this.excelSheets = data?.multiSheetColumnNames;
      if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
      this.selectedSheet = this.sheetNames[0];
      this.formKeys = data?.columnNames;
      this.s3BucketKey = data?.s3BucketKey;
      if (this.formKeys?.length) {
        this.currentStep = 3;
      }
      this.resetForm();
      this.onAutoMapChange();
    });
  }

  isValidForm() {
    if (!this.selectedSheet || !this.projectUnitMappingForm.valid) {
      validateAllFormFields(this.projectUnitMappingForm);
      return;
    } else {
      this.isValidModal = true;
      this.modalRef = this.modalService.show(this.validModal, {
        class: 'modal-350 modal-dialog-centered ip-modal-unset',
        keyboard: false,
      });
    }
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  openBulkUploadedStatusModal() {
    this.navigateToProjects();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: location?.href.includes('properties') ? 'property' : 'project',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState
    });
  }

  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
  }

  navigateToProjects() {
    this.router.navigate([`projects/edit-project/unit-info/${this.projectId}`]);
  }

  confirmSheet(trackerInfoModal: TemplateRef<any>) {
    this.modalRef.hide();
    this.sendProjectMappingDetails(trackerInfoModal);
  }

  sendProjectMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (
      !this.selectedSheet ||
      !this.projectUnitMappingForm.controls['name'].value
    ) {
      return;
    }
    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnData: {},
      userIds: [],
      projectId: this.projectId,
      timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
    };

    ProjectUnitDataColumns.map((item: any) => {
      if (this.projectUnitMappingForm.value[item.value]) {
        payload.mappedColumnData[item.displayName] =
          this.projectUnitMappingForm.value[item.value];
      }
    });
    payload.sheetName = this.selectedSheet || this.sheetNames[0];

    this._store.dispatch(new UploadMappedColumns(payload));
    this._store.dispatch(new FetchProjectUnitExcelUploadedList(1, 10));
    this.modalRef = this.modalService.show(
      trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  resetForm() {
    if (this.projectUnitMappingForm)
      this.projectUnitMappingForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
