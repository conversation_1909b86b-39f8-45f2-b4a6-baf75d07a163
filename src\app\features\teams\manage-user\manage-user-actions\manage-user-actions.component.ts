import {
  Component,
  EventEmitter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { Subscription } from 'src/app/core/interfaces/profile.interface';
import {
  getAppName,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  isEmptyObject,
} from 'src/app/core/utils/common.util';
import { getAttendanceSettings } from 'src/app/reducers/attendance/attendance.reducer';
import {
  getDeletePermissions,
  getEditPermissions,
  getPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { getSubscription } from 'src/app/reducers/profile/profile.reducers';
import {
  DeleteAssignedUser,
  DeleteUser,
  FetchUserAssignedDataById,
  FetchUserAssignedDataByIdSuccess,
} from 'src/app/reducers/teams/teams.actions';
import {
  getBulkUserLeadCount,
  getIsUserDeleted,
  getUserAssignData,
  getUserAssignDataIsLoading,
  getUserBasicDetails,
} from 'src/app/reducers/teams/teams.reducer';
import { TeamsService } from 'src/app/services/controllers/teams.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { DeletedUsersTrackerComponent } from 'src/app/shared/components/deleted-users-tracker/deleted-users-tracker.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment } from 'src/environments/environment';
import { AddGeoFencingComponent } from '../geo-fencing/add-geo-fencing/add-geo-fencing.component';

@Component({
  selector: 'manage-user-actions',
  templateUrl: './manage-user-actions.component.html',
})
export class ManageUserActionsComponent implements OnInit, OnDestroy {
  @ViewChild('trackerInfoModal') trackerInfoModal: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  isUserActive: any;
  canEditUser: boolean = false;
  canDeleteUser: boolean = false;
  userLeadsCount: any;
  subscription: Subscription;
  assignedDetails: any[];
  userData: any;
  userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  getAppName = getAppName;
  canMakeDefaultPassword: boolean = false;
  canGeoFence: boolean = false;
  isGeoFenceEnabled: boolean = false;

  constructor(
    public modalService: BsModalService,
    private store: Store<AppState>,
    private modalRef: BsModalRef,
    private router: Router,
    private teamsService: TeamsService,
    private _notificationService: NotificationsService,
    public trackingService: TrackingService
  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

    this.store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
      })

    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Users')) {
          this.canEditUser = true;
        }
      });

    this.store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete?.includes('Users')) {
          this.canDeleteUser = true;
        }
      });

    this.store
      .select(getSubscription)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subscription = data;
      });
  }

  ngOnInit(): void {
    this.store
      .select(getUserAssignData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          this.assignedDetails = data;
        }
      });

    this.store
      .select(getBulkUserLeadCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userLeadsCount = data.filter(
          (user: any) => user.userId === this.params.value[1]
        );
      });
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canMakeDefaultPassword = permissionsSet.has('Permissions.Users.DefaultPassword');
        this.canGeoFence = permissionsSet.has('Permissions.Users.SetGeoFence');
      });
  }

  agInit(params: any): void {
    this.params = params;
    this.isUserActive = params.data.isActive;
  }

  editUser() {
    this.trackingService.trackFeature(`Web.TeamUser.Button.Edit.Click`)
    this.router.navigate(['teams/edit-user/' + this.params.data.userId], {
      state: this.params.data,
    });
  }

  deleteUser(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.store.dispatch(new FetchUserAssignedDataByIdSuccess({}));
    this.store.dispatch(new FetchUserAssignedDataById(data?.userId));
    this.store
      .select(getUserAssignDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        if (!isLoading && this.modalService.getModalsCount() === 0) {
          let initialState: any = {
            type: 'userDelete',
            data: {
              ...this.assignedDetails,
              id: data?.userId,
              fieldType: 'Warning',
              heading: 'Are you sure you want to delete user?',
            },
            class: 'modal-400 modal-dialog-centered ph-modal-unset',
          };

          this.modalRef = this.modalService.show(
            UserAlertPopupComponent,
            Object.assign(
              {},
              {
                class: 'modal-450 modal-dialog-centered ph-modal-unset',
                initialState,
              }
            )
          );

          if (this.modalRef?.onHide) {
            this.modalRef.onHide.subscribe((reason: string) => {
              if (reason == 'confirmed') {
                const payload = {
                  userId: data?.userId,
                  isDelete: true,
                  timeZoneId:
                    this.userData?.timeZoneInfo?.timeZoneId ||
                    getSystemTimeZoneId(),
                  baseUTcOffset:
                    this.userData?.timeZoneInfo?.baseUTcOffset ||
                    getSystemTimeOffset(),
                };
                this.trackingService.trackFeature(`Web.TeamUser.Button.Delete.Click`)
                this.store.dispatch(new DeleteAssignedUser(payload));
                this.store
                  .select(getIsUserDeleted)
                  .pipe(takeUntil(this.stopper))
                  .subscribe((isDeleteSuccess: boolean) => {
                    if (isDeleteSuccess) {
                      this.showTrackerInfoModal();
                    }
                  });
              }
            });
          }
        }
      });
  }

  showTrackerInfoModal() {
    this.modalRef = this.modalService.show(
      this.trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  navigateToHome() {
    this.router.navigate(['teams/manage-user']);
  }

  openDeleteTracker() {
    this.navigateToHome();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: 'user',
    };
    this.modalRef = this.modalService.show(DeletedUsersTrackerComponent, {
      class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
      initialState,
    });
  }

  initToggleUserStatus(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: this.isUserActive ? 'deactivate' : 'activate',
      title: data.firstName + ' ' + data.lastName,
      fieldType: 'user',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.toggleUserStatus(
            this.params.data.userId,
            this.params.data.isActive,
            this.userData?.timeZoneInfo
          );
        }
      });
    }
  }

  toggleUserStatus(id: string, isActive: boolean, timeZoneInfo: any) {
    const payload = {
      userId: id,
      activateUser: !isActive,
      timeZoneId: timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      currentUserId: this.userId,
    };
    this.store.dispatch(new DeleteUser(payload));
  }

  handleUserStatus(licenseBoughtModal: any) {
    this.trackingService.trackFeature(`Web.TeamUser.Button.Active.Click`)
    if (!this.isUserActive) {
      const { licenseBought, activeUsers } = this.subscription;
      if (licenseBought <= activeUsers) {
        this.modalRef = this.modalService.show(licenseBoughtModal, {
          class: 'modal-600 top-modal ip-modal-unset',
        });
      } else {
        this.initToggleUserStatus(this.params.data);
      }
    } else {
      this.initToggleUserStatus(this.params.data);
    }
  }

  geoFencing() {
    const initialState: any = {
      userId: this.params.data.userId
    };

    this.modalRef = this.modalService.show(
      AddGeoFencingComponent,
      {
        class: 'up-modal modal-350',
        initialState
      }
    );
  }

  closeModal() {
    this.modalRef.hide();
  }

  resetPassword(data: any) {
    this.trackingService.trackFeature(`Web.TeamUser.Button.ResetDefualtPassword.Click`)
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'reset',
      title: data.firstName + ' ' + data.lastName,
      fieldType: "password to 'Default Password'",
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.teamsService.resetPassword([data.userId]).subscribe((res: any) => {
            if (res?.succeeded) this._notificationService.success('The password has been reset to the default successfully.');
          })
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
