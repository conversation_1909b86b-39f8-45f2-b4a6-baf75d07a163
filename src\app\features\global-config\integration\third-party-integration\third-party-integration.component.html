<form class="text-coal" [ngClass]="{'pe-none blinking': isIntegrationLoading}">
    <!-- img section -->
    <div class="align-center flex-between p-20">
        <div class="align-center">
            <div class="icon ic-triangle rotate-90 ic-xxs ic-coal cursor-pointer p-8 mr-4"
                [routerLink]="displayName == 'Facebook' ? '/global-config/facebook' : '/global-config'">
            </div>
            <img [type]="'leadrat'" [appImage]="image" alt="img" />
        </div>
        <ng-container *ngIf="displayName == 'Gmail'; else otherIntegration">
            <div class="g-sign-in-button" (click)="googleOAuthSignIn()">
                <div class="content-wrapper">
                    <div class="logo-wrapper">
                        <img [src]="googleButton" alt="google" />
                    </div>
                    <span class="text-container">
                        <span>{{ 'INTEGRATION.sign-in-with-google' | translate }}</span>
                    </span>
                </div>
            </div>
        </ng-container>
        <ng-template #otherIntegration>
            <div class="justify-center ph-flex-col">
                <button *ngIf="canAdd" type="button" class="btn-coal" (click)="addNewAccount(addAccount)"
                    id="btnAddAccount" data-automate-id="btnAddAccount">
                    {{ 'SIDEBAR.add' | translate }} {{'SIDEBAR.account' | translate}}</button>
                <div class="btn-coal ml-20 ph-mt-4"
                    *ngIf="integrationAssignmentData?.isWhatsAppDeepIntegration && displayName == 'Whatsapp' && updatedIntegrationList?.length"
                    (click)="toggleWhatsappSettings(whatsappSetting)">
                    Config </div>
            </div>
        </ng-template>
    </div>
    <!-- start page -->
    <ng-container *ngIf="!updatedIntegrationList?.length && !searchTerm else addAndListingPage">
        <div class="px-20 mt-20">
            <div class="align-center">
                <div class="d-flex">
                    <div class="align-center-col">
                        <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                            <span class="icon ic-download ic-lg ic-coal"></span>
                        </div>
                        <div class="border-left-dotted h-60"></div>
                    </div>
                    <p class="text-coal ml-20 mt-20 fw-600">
                        {{(displayName == 'Gmail' ? 'INTEGRATION.click-google-btn'
                        : 'INTEGRATION.download-excel') | translate }}</p>
                </div>
            </div>
            <div class="align-center"
                *ngIf="displayName !== 'Google ads landing page' && displayName !== 'Google ads leads form' && displayName !== 'Microsoft Ads'">
                <div class="d-flex">
                    <div class="align-center-col">
                        <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                            <span class="icon ic-share ic-lg ic-coal"></span>
                        </div>
                        <div class="border-left-dotted h-60"></div>
                    </div>
                    <div *ngIf="displayName == 'Gmail'; else otherIntegrationSteps" class="text-gray ml-20 mt-20">
                        <p>{{ 'INTEGRATION.share-message-google1' | translate }} {{getAppName()}} {{
                            'INTEGRATION.share-message-google2' | translate }}</p>
                    </div>
                    <ng-template #otherIntegrationSteps>
                        <div class="text-gray ml-20 mt-20">
                            <p>
                                <span class="fw-600 text-coal">{{ displayName }} </span>
                                <span class="fw-semi-bold"> {{ 'INTEGRATION.share-message2' | translate
                                    }}</span>
                                <span class="fw-600 text-coal"> {{ displayName }}</span>
                                <span class="fw-semi-bold"></span> {{ 'INTEGRATION.share-message3' | translate
                                }}
                                {{getAppName()}} {{ 'INTEGRATION.share-message4' | translate }}
                            </p>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="d-flex">
                <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                    <span class="icon ic-connection ic-lg ic-coal"></span>
                </div>
                <div class="text-gray ml-20 mt-20">
                    <p>{{ 'INTEGRATION.connection-message' | translate }}</p>
                </div>
            </div>
        </div>
    </ng-container>
    <!-- new account  -->
    <ng-template #addAccount>
        <div class="bg-dark w-100 px-16 py-12 text-white flex-between">
            <div class="align-center">
                <div class="icon ic-envelope-solid ic-large mr-8"></div>
                <h4 class="fw-semi-bold">API Email Integration </h4>
            </div>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
        </div>
        <ng-container *ngIf="displayName !== 'Webhook'">
            <form [formGroup]="integrationForm">
                <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-110 scrollbar">
                    <div class="field-label-req">Account Name </div>
                    <form-errors-wrapper label="Account Name" [control]="integrationForm?.controls['accountName']">
                        <input type="text" required formControlName="accountName" id="inpPartyAccountName"
                            data-automate-id="inpPartyAccountName" autocomplete="off" placeholder="ex. Mounika Pampana"
                            appDebounceInput [debounceTime]="500" (debounceEvent)="doesAccountNameExists()" />
                    </form-errors-wrapper>
                    <div class="field-label">Login Id/Login Email</div>
                    <form-errors-wrapper label="Login Id/Login Email"
                        [control]="integrationForm.controls['loginEmail']">
                        <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                    </form-errors-wrapper>
                    <!-- <small class="text-danger text-xxs flex-end mr-8 position-absolute right-20"
                                *ngIf="addAgencyForm.get('email')?.errors?.email">
                                Please enter a valid email address.
                            </small> -->
                    <div class="field-label-req">Relationship Manager Email</div>
                    <form-errors-wrapper label="Relationship Manager Email"
                        [control]="integrationForm?.controls['toRecipients']">
                        <div class="flex-between position-relative">
                            <input type="text" required placeholder="ex. <EMAIL>" #emailToInput id="emailToInput"
                                class="outline-0 padd-r pr-36" autocomplete="off"
                                (keyup.enter)="addInputField(emailToInput.value, emailToInput, integrationToRecipients())"
                                (input)="handleEmailValidation(integrationForm, emailToInput, 'toRecipients')"
                                [ngClass]="(emailToInput.value && !validateEmail(emailToInput.value)) || (integrationForm.get('toRecipients').touched && !integrationToRecipients().length && !emailToInput.value) ? 'border-red-800': ''" />

                            <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                                [ngClass]="emailToInput.value && !validateEmail(emailToInput.value) ? 'pe-none ': 'cursor-pointer'"
                                (click)="addInputField(emailToInput.value, emailToInput, integrationToRecipients())">
                                <span class="icon ic-plus ic-x-xs"></span>
                            </div>
                        </div>
                        <div *ngIf="emailToInput.value && !validateEmail(emailToInput.value)"
                            class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                            Enter a valid Email ID.
                        </div>
                    </form-errors-wrapper>
                    <div class="d-flex flex-wrap mt-12" *ngIf="integrationToRecipients()?.controls?.length">
                        <div *ngFor="let email of integrationToRecipients()?.controls; let i = index"
                            class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                            <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                email.value }}</span>
                            <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                (click)="removeEmail(i, integrationToRecipients(), emailToInput)"></span>
                        </div>
                    </div>
                    <div class="field-label">Additional Email</div>
                    <form-errors-wrapper label="Additional Email" [control]="integrationForm.controls['ccRecipients']">
                        <div class="flex-between position-relative">
                            <input type="text" placeholder="ex. <EMAIL>" #emailCcInput id="emailCcInput"
                                class="outline-0 padd-r pr-36" autocomplete="off"
                                (keyup.enter)="addInputField(emailCcInput.value, emailCcInput, integrationCcRecipients())"
                                [ngClass]="emailCcInput.value && !validateEmail(emailCcInput.value) ? 'border-red-800': ''" />
                            <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                                [ngClass]="emailCcInput.value && !validateEmail(emailCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                                (click)="addInputField(emailCcInput.value, emailCcInput, integrationCcRecipients())">
                                <span class="icon ic-plus ic-x-xs"></span>
                            </div>
                            <div *ngIf="emailCcInput.value && !validateEmail(emailCcInput.value)"
                                class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                                Enter a valid Email ID.
                            </div>
                        </div>
                    </form-errors-wrapper>
                    <div class="d-flex flex-wrap mt-12" *ngIf="integrationCcRecipients()?.controls?.length">
                        <div *ngFor="let email of integrationCcRecipients()?.controls; let i = index"
                            class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                            <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                email.value }}</span>
                            <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                (click)="removeEmail(i, integrationCcRecipients())"></span>
                        </div>
                    </div>
                    <div class="field-label">Bcc Email</div>
                    <form-errors-wrapper label="Bcc Email" [control]="integrationForm.controls['bccRecipients']">

                        <div class="flex-between position-relative">
                            <input type="text" placeholder="ex. <EMAIL>" #emailBccInput id="emailBccInput"
                                class="outline-0 padd-r pr-36" autocomplete="off"
                                (keyup.enter)="addInputField(emailBccInput.value, emailBccInput, integrationBccRecipients())"
                                [ngClass]="emailBccInput.value && !validateEmail(emailBccInput.value) ? 'border-red-800': ''" />

                            <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                                [ngClass]="emailBccInput.value && !validateEmail(emailBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                                (click)="addInputField(emailBccInput.value, emailBccInput, integrationBccRecipients())">
                                <span class="icon ic-plus ic-x-xs"></span>
                            </div>
                            <div *ngIf="emailBccInput.value && !validateEmail(emailBccInput.value)"
                                class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                                Enter a valid Email ID.
                            </div>
                        </div>
                    </form-errors-wrapper>
                    <div class="d-flex flex-wrap mt-12" *ngIf="integrationBccRecipients()?.controls?.length">
                        <div *ngFor="let email of integrationBccRecipients()?.controls; let i = index"
                            class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                            <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                email.value }}</span>
                            <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                (click)="removeEmail(i, integrationBccRecipients())"></span>
                        </div>
                    </div>

                </div>
                <div class="flex-end p-16 box-shadow-20">
                    <button type="button" class="btn-gray mr-20" (click)="closeModal()">
                        {{ 'BUTTONS.cancel' | translate }}</button>
                    <ng-container *ngIf="displayName != 'Gmail'">
                        <button type="button" class="btn-coal" (click)="downloadExcelFile()">
                            Add Account</button>
                    </ng-container>
                </div>
            </form>
        </ng-container>
        <!-- new account webhook -->
        <ng-container *ngIf="displayName == 'Webhook'">
            <h5 class="text-black-200 mt-12 pb-6 px-16">
                <span class="break-all">Webhook Link</span><span
                    class="icon ic-xxs ic-black-200 ml-6 ic-copy-clipboard cursor-pointer"
                    (click)="copyToClipboard()"></span>
            </h5>
            <form [formGroup]="webhookMappingForm">
                <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-140 scrollbar">
                    <div class="flex-column flex-grow-1">
                        <div class="field-label-req">Account Name</div>
                        <form-errors-wrapper label="Account Name"
                            [control]="webhookMappingForm.controls['accountName']">
                            <input type="text" required formControlName="accountName" id="inpWebhookAccountName"
                                data-automate-id="inpWebhookAccountName" [readonly]="isEditWebhook ? true: false "
                                autocomplete="off" appDebounceInput [debounceTime]="500"
                                (debounceEvent)="doesAccountNameExists()" placeholder="ex. Mounika Pampana">
                        </form-errors-wrapper>
                        <div class="field-label">Login Id/Login Email</div>
                        <form-errors-wrapper label="Login Id/Login Email"
                            [control]="webhookMappingForm.controls['loginEmail']">
                            <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                        </form-errors-wrapper>
                        <div class="field-label-req"
                            *ngIf="!isEditWebhook || webhookToRecipients()?.controls?.length > 0">Relationship
                            Manager Email</div>
                        <form-errors-wrapper label="Relationship Manager Email" [hidden]="isEditWebhook"
                            [control]="webhookMappingForm?.controls['toRecipients']">
                            <div class="flex-between position-relative">
                                <input type="text" placeholder="ex. <EMAIL>" #webhookEmailToInput
                                    id="webhookEmailToInput" class="outline-0 padd-r pr-36" autocomplete="off"
                                    [readonly]="isEditWebhook ? true : false"
                                    (keyup.enter)="addInputField(webhookEmailToInput.value, webhookEmailToInput, webhookToRecipients())"
                                    (input)="handleEmailValidation(webhookMappingForm, webhookEmailToInput, 'toRecipients')"
                                    [ngClass]="webhookEmailToInput.value && !validateEmail(webhookEmailToInput.value) ? 'border-red-800': ''" />
                                <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                                    [ngClass]="webhookEmailToInput.value && !validateEmail(webhookEmailToInput.value) ? 'pe-none ': 'cursor-pointer'"
                                    (click)="addInputField(webhookEmailToInput.value, webhookEmailToInput, webhookToRecipients())">
                                    <span class="icon ic-plus ic-x-xs"></span>
                                </div>
                                <div *ngIf="webhookEmailToInput.value && !validateEmail(webhookEmailToInput.value)"
                                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                                    Enter a valid Email ID.
                                </div>
                            </div>
                        </form-errors-wrapper>
                        <div class="d-flex flex-wrap mt-12" *ngIf="webhookToRecipients()?.controls?.length">
                            <div *ngFor="let email of webhookToRecipients()?.controls; let i = index"
                                class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                                <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                    email.value }}</span>
                                <span *ngIf="!isEditWebhook"
                                    class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                    (click)="removeEmail(i, webhookToRecipients(), webhookEmailToInput)">
                                </span>
                            </div>
                        </div>
                        <div class="field-label" *ngIf="!isEditWebhook || webhookCcRecipients()?.controls?.length > 0">
                            Additional Email
                        </div>
                        <form-errors-wrapper label="Additional Email" *ngIf="!isEditWebhook"
                            [control]="webhookMappingForm.controls['ccRecipients']">
                            <div class="flex-between position-relative">
                                <input type="text" placeholder="ex. <EMAIL>" #webhookEmailCcInput
                                    id="webhookEmailCcInput" class="outline-0 padd-r pr-36" autocomplete="off"
                                    [readonly]="isEditWebhook ? true: false "
                                    (keyup.enter)="addInputField(webhookEmailCcInput.value, webhookEmailCcInput, webhookCcRecipients())"
                                    [ngClass]="webhookEmailCcInput.value && !validateEmail(webhookEmailCcInput.value) ? 'border-red-800': ''" />

                                <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                                    [ngClass]="webhookEmailCcInput.value && !validateEmail(webhookEmailCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                                    (click)="addInputField(webhookEmailCcInput.value, webhookEmailCcInput, webhookCcRecipients())">
                                    <span class="icon ic-plus ic-x-xs"></span>
                                </div>
                                <div *ngIf="webhookEmailCcInput.value && !validateEmail(webhookEmailCcInput.value)"
                                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                                    Enter a valid Email ID.
                                </div>
                            </div>
                        </form-errors-wrapper>
                        <div class="d-flex flex-wrap mt-12" *ngIf="webhookCcRecipients()?.controls?.length">
                            <div *ngFor="let email of webhookCcRecipients()?.controls; let i = index"
                                class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                                <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                    email.value }}</span>
                                <span *ngIf="!isEditWebhook"
                                    class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                    (click)="removeEmail(i, webhookCcRecipients())"></span>
                            </div>
                        </div>
                        <div class="field-label" *ngIf="!isEditWebhook || webhookBccRecipients()?.controls?.length > 0">
                            BCC Email</div>
                        <form-errors-wrapper label="BCC Email" [control]="webhookMappingForm.controls['bccRecipients']"
                            *ngIf="!isEditWebhook">

                            <div class="flex-between position-relative">
                                <input type="text" placeholder="ex. <EMAIL>" #webhookEmailBccInput
                                    id="webhookEmailBccInput" class="outline-0 padd-r pr-36" autocomplete="off"
                                    [readonly]="isEditWebhook ? true: false "
                                    (keyup.enter)="addInputField(webhookEmailBccInput.value, webhookEmailBccInput, webhookBccRecipients())"
                                    [ngClass]="webhookEmailBccInput.value && !validateEmail(webhookEmailBccInput.value) ? 'border-red-800': ''" />

                                <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                                    [ngClass]="webhookEmailBccInput.value && !validateEmail(webhookEmailBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                                    (click)="addInputField(webhookEmailBccInput.value, webhookEmailBccInput, webhookBccRecipients())">
                                    <span class="icon ic-plus ic-x-xs"></span>
                                </div>
                                <div *ngIf="webhookEmailBccInput.value && !validateEmail(webhookEmailBccInput.value)"
                                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                                    Enter a valid Email ID.
                                </div>
                            </div>
                        </form-errors-wrapper>
                        <div class="d-flex flex-wrap mt-12" *ngIf="webhookBccRecipients()?.controls?.length">
                            <div *ngFor="let email of webhookBccRecipients()?.controls; let i = index"
                                class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                                <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                    email.value }}</span>
                                <span *ngIf="!isEditWebhook"
                                    class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                    (click)="removeEmail(i, webhookBccRecipients())"></span>
                            </div>
                        </div>
                        <ng-container *ngIf="webhookKeys?.length">
                            <div class="field-label-req">Forms</div>
                            <form-errors-wrapper label="form" [control]="webhookMappingForm.controls['formName']"
                                class="mb-12">
                                <ng-select class="bg-white" [virtualScroll]="true" placeholder="select"
                                    bindLabel="displayName" bindValue="displayName" [items]="webhookKeys"
                                    formControlName="formName"></ng-select>
                            </form-errors-wrapper>
                        </ng-container>
                        <ng-container *ngIf="!webhookKeys?.length">
                            <div class="text-red-350 fw-600 text-sm mt-20">Webhook is not Connected
                            </div>
                        </ng-container>
                    </div>
                    <div formArrayName="parameters" *ngIf="webhookMappingForm.controls['formName'].value">
                        <div class="field-label-req">
                            Payload Mapping</div>
                        <div *ngFor="let control of parameters.controls; let i = index">
                            <ng-container [formGroupName]="i">
                                <div class="d-flex mb-16">
                                    <div class="w-50 me-2">
                                        <form-errors-wrapper label="payload key" [control]="control.get('payloadKey')"
                                            class="mb-12">
                                            <ng-select class="bg-white" (change)="onPayloadOptionChanged()"
                                                [virtualScroll]="true" placeholder="key" [clearable]="false"
                                                [ngClass]="(control.get('payloadKey').value === '#Name#' || control.get('payloadKey').value === '#Mobile#')? 'pe-none': ''"
                                                bindLabel="displayName" bindValue="value"
                                                [items]="(control.get('payloadKey').value === '#Name#' || control.get('payloadKey').value === '#Mobile#') ? temp : payloadOptions"
                                                formControlName="payloadKey"></ng-select>
                                            <ng-template ng-option-tmp let-item="item">
                                                <div [ngClass]="{'disabled-option': item.disabled}">
                                                    {{ item.displayName }}
                                                </div>
                                            </ng-template>
                                        </form-errors-wrapper>
                                    </div>
                                    <div class="w-50">
                                        <div
                                            [ngClass]="!(control.get('payloadKey').value === '#Name#' || control.get('payloadKey').value === '#Mobile#') ? 'd-flex' : ''">
                                            <form-errors-wrapper label="payload value"
                                                [control]="control.get('payloadValue')" class="flex-grow-1">
                                                <ng-select class="bg-white" [virtualScroll]="true"
                                                    [multiple]="control.get('payloadKey').value === '#Notes#'  || control.get('payloadKey').value === '#Name#' || control.get('payloadKey').value === '#Project#' ? true : false"
                                                    [closeOnSelect]="control.get('payloadKey').value === '#Notes#' ? false : true"
                                                    placeholder="value" bindLabel="displayName"
                                                    (change)="onPayloadChanged()"
                                                    [bindValue]="control.get('payloadKey').value === '#LeadSource#' ? 'value' : 'displayName'"
                                                    [items]="control.get('payloadKey').value === '#LeadSource#' ? leadSources : payloadfieldkey"
                                                    [addTag]="control.get('payloadKey').value === '#LeadSource#' ? false : true"
                                                    [ngClass]="control.get('payloadKey').value ? '' : 'pe-none'"
                                                    addTagText="Manual Create" formControlName="payloadValue"
                                                    [title]="getPayloadValueTooltip(control)">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index"
                                                        *ngIf="control.get('payloadKey').value === '#Notes#'  || control.get('payloadKey').value === '#Name#' || control.get('payloadKey').value === '#Project#'">
                                                        <div class="checkbox-container"><input type="checkbox"
                                                                id="item-{{index}}" data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>
                                                            <span title="{{item || item.displayName }}"> {{ item ||
                                                                item.displayName }}</span>
                                                        </div>
                                                    </ng-template>
                                                </ng-select>
                                            </form-errors-wrapper>
                                            <div class="align-center"
                                                *ngIf="!(control.get('payloadKey').value === '#Name#' || control.get('payloadKey').value === '#Mobile#')">
                                                <div title="Delete" class="bg-light-red icon-badge"
                                                    (click)="onRemovePayload(i)">
                                                    <span class="icon ic-delete ic-xxxs"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="cursor-pointer align-center fw-700"
                            *ngIf="payloadOptions?.length && parameters?.length < tempVariables?.length">
                            <span class="icon ic-xs ic-add ic-accent-green"></span>
                            <span class="text-accent-green" (click)="onAddPayload()">add additional
                                field</span>
                        </div>
                    </div>
                </div>
                <div class="flex-end p-16 box-shadow-20">
                    <button type="button" class="btn-gray mr-20" (click)="webbookCancel()">
                        {{ 'BUTTONS.cancel' | translate }}</button>
                    <button type="button" class="btn-coal" [ngClass]="{'pe-none opacity-5': !webhookKeys?.length}"
                        (click)="downloadwebhookExcelFile()">
                        {{ isEditWebhook ? 'Save' : 'Add Account' }}
                    </button>
                </div>
            </form>
        </ng-container>
    </ng-template>
    <!-- whatsapp -->
    <ng-template #whatsappSetting>
        <div class="bg-dark w-100 px-16 py-12 text-white flex-between">
            <h3 class="fw-semi-bold">Integration </h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()">
            </div>
        </div>
        <div class="p-16" *ngIf="isShowAddAtListing">
            <u class="field-label header-4">Settings:</u>
            <form class="align-center-col" [formGroup]="settingForm">
                <div class="w-100">
                    <div class="field-label">Personal Whatsapp</div>
                    <ng-select [virtualScroll]="true" [items]="personalList1" [multiple]="true" ResizableDropdown
                        [closeOnSelect]="false" bindValue="id" formControlName="personal" bindLabel="fullName"
                        placeholder="ex. Mounika Pampana" class="bg-white">
                        <ng-template ng-label-tmp let-item="item">
                            {{item.firstName}} {{item.lastName}}
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
                <div class="w-100">
                    <div class="field-label">Integration WhatsApp</div>
                    <ng-select [virtualScroll]="true" [items]="integrationList1" [multiple]="true" ResizableDropdown
                        [closeOnSelect]="false" bindValue="id" formControlName="integration" bindLabel="fullName"
                        placeholder="ex. Mounika Pampana" class="bg-white">
                        <ng-template ng-label-tmp let-item="item">
                            {{item.firstName}} {{item.lastName}}
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </form>

            <div class="flex-end mt-20">
                <button type="button" class="btn-gray mr-20" (click)="modalService.hide()">
                    {{ 'BUTTONS.cancel' | translate }}
                </button>
                <button type="button" class="btn-coal" (click)="updateSettingsList()">Save</button>
            </div>
        </div>
    </ng-template>
    <!-- after account added -->
    <ng-template #addAndListingPage>
        <div class="px-16">
            <div class="align-center px-10 border flex-grow-1 no-validation bg-white">
                <ng-container>
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                    <input (keydown)="onSearch($event)" (input)="isEmptyInput($event)" placeholder="type to search"
                        autocomplete="off" [(ngModel)]="searchTerm" name="search"
                        class="border-0 outline-0 w-100 py-12">
                    <small class="text-muted text-nowrap ph-d-none">
                        ({{ 'LEADS.lead-search-prompt' | translate }})</small>
                </ng-container>
                <div class="show-dropdown-white align-center position-relative ip-br-0">
                    <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                            {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
                    <ng-select [items]="showEntriesSize" [formControl]="pageEntry" (change)="assignPageSize()"
                        [virtualScroll]="true" [placeholder]="PageSize" class="w-150 tb-w-120px" [searchable]="false">
                    </ng-select>
                </div>
            </div>
            <div class="bg-white py-6  scrollbar table-scrollbar tb-w-100-30">
                <table class="table standard-table no-vertical-border"
                    *ngIf="updatedIntegrationList?.length > 0 || !searchTerm">
                    <thead>
                        <tr class="w-100 text-nowrap">
                            <th class="w-40" *ngIf="canBulkAssignment || canBulkReassign">
                                <div class="mb-16">
                                    <label class="checkbox-container justify-center mr-6"><input type="checkbox"
                                            [checked]="isAllSelected()" (change)="selectAllRows($event)">
                                        <span class="checkmark justify-center"></span>
                                    </label>
                                </div>
                            </th>
                            <th class="w-130">Account Name</th>
                            <th class="w-100px"
                                *ngIf="displayName == 'Google ads landing page' || displayName == 'Google ads leads form' || displayName == 'Microsoft Ads'">
                                {{'INTEGRATION.agency-name' | translate }}</th>
                            <th class="w-100px">{{ 'INTEGRATION.leads-count' | translate }}</th>
                            <th class="w-130">Integration Status</th>
                            <th class="w-180">Relationship Manager Email(s)</th>
                            <th class="w-130">Additional Email(s) </th>
                            <th class="w-100px">Bcc Email(s) </th>
                            <th class="w-180">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold max-h-100-280 scrollbar">
                        <ng-container *ngFor="let integration of updatedIntegrationList">
                            <tr class="w-100">
                                <td class="w-40" *ngIf="canBulkAssignment || canBulkReassign">
                                    <div class="mb-16">
                                        <label class="checkbox-container">
                                            <input type="checkbox" [ngModelOptions]="{standalone: true}"
                                                (change)="onCheckboxChange($event)"
                                                [(ngModel)]="integration.isSelected" /><span class="checkmark"></span>
                                        </label>
                                    </div>
                                </td>
                                <td class="w-130">
                                    <div [title]="integration?.accountName" class="text-truncate-1 break-all w-100">
                                        {{ integration.accountName }}</div>
                                </td>
                                <td class="w-100px"
                                    *ngIf="displayName == 'Google ads landing page' || displayName == 'Google ads leads form' || displayName == 'Microsoft Ads'">
                                    <div class="flex-between">
                                        <div [title]="integration.agencyName" class="text-truncate-1 break-all">
                                            {{
                                            integration?.agencyName}}</div>
                                        <div title="Agency Name" class="bg-blue-850 icon-badge"
                                            (click)="openAgentNameModal(agencyModal, integration)">
                                            <span class="icon ic-suitcase m-auto ic-xxs"></span>
                                        </div>
                                    </div>
                                </td>
                                <td class="w-100px">
                                    <div>{{integration?.totalLeadCount}} ({{ integration?.leadCount }})</div>
                                </td>
                                <td class="w-130 fw-600"
                                    [ngClass]="integration?.status == 'InComplete' ? 'text-accent-red': 'text-accent-green'">
                                    {{integration?.status}}</td>
                                <td class="w-180">
                                    <div class="text-truncate-1 break-all" [title]="integration?.toRecipients">
                                        {{integration?.toRecipients}}</div>
                                </td>
                                <td class="w-130">
                                    <div class="text-truncate-1 break-all" [title]="integration?.ccRecipients">
                                        {{integration?.ccRecipients}}</div>
                                </td>
                                <td class="w-100px">
                                    <div class="text-truncate-1 break-all" [title]="integration?.bccRecipients">
                                        {{integration?.bccRecipients}}</div>
                                </td>
                                <td class="w-180">
                                    <div class="align-center">
                                        <div *ngIf="displayName === 'Webhook'" title="Edit"
                                            class="bg-accent-green icon-badge"
                                            (click)="onEditWebhook(addAccount, integration?.accountId, integration.accountName)">
                                            <span class="icon ic-pen ic-xxxs"></span>
                                        </div>
                                        <div *ngIf="displayName != 'Gmail'" title="Download"
                                            class="bg-accent-green icon-badge"
                                            (click)="reDownloadExcel(displayName == 'Google ads leads form' ? integration?.id : integration.accountId)">
                                            <span class="icon ic-download ic-xxxs"></span>
                                        </div>
                                        <div *ngIf="canDelete" title="Delete" class="bg-light-red icon-badge"
                                            (click)="initDeleteIntegration(displayName == 'Google ads leads form' ? integration?.id : integration.accountId, integration.accountName)">
                                            <span class="icon ic-delete ic-xxxs"></span>
                                        </div>
                                        <div *ngIf="canAssign && canViewForFilter" title="Assign To"
                                            class="bg-blue-800 icon-badge" (click)="openAssignmentModal(integration);">
                                            <span class="icon ic-assign-to ic-xxxs"></span>
                                        </div>
                                        <div title="Config" class="bg-dark-red-30 icon-badge"
                                            (click)="openProjectAndLocationModal( ProjectAndLocationModal, integration.accountId, integration?.leadSource, integration.accountName);">
                                            <span class="icon ic-user-setting ic-xxxs"></span>
                                        </div>
                                        <div title="Country Code" class="bg-accent-green icon-badge"
                                            (click)="openCountryCode(CountryCodeModal, integration.accountId, integration?.leadSource, integration.accountName);">
                                            <span class="icon ic-call-sms ic-xxxs"></span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <ng-template #agencyModal>
                                <div class="p-16 border br-8">
                                    <a class="ic-close-secondary ic-close-modal-coal ip-ic-close-modal"
                                        (click)="modalRef.hide()"></a>
                                    <div>
                                        <span class="fw-semi-bold fv-sm-caps">
                                            {{'INTEGRATION.account-name' | translate}}: </span>
                                        <span
                                            class="fw-700 text-large ml-4 text-truncate-1 break-all">{{selectedAccountName}}</span>
                                    </div>
                                    <div class="form-group mb-30">
                                        <div class="field-label">{{ 'INTEGRATION.agency-name' | translate }}
                                        </div>
                                        <ng-select [virtualScroll]="true" [items]="agencyNameList" ResizableDropdown
                                            [addTag]="true" bindLabel="agencyName" bindValue="agencyName"
                                            [formControl]="agencyName" class="bg-white"
                                            addTagText="Create New Agency Name"
                                            placeholder="ex. Mounika pampana"></ng-select>
                                    </div>
                                    <div class="flex-center">
                                        <button class="btn-gray mr-20" (click)="closeModal()">
                                            {{ 'BUTTONS.cancel' | translate }}</button>
                                        <button class="btn-coal" (click)="updateAgentName()">
                                            {{ 'BUTTONS.save' | translate }}</button>
                                    </div>
                                </div>
                            </ng-template>
                        </ng-container>
                    </tbody>
                </table>
            </div>
            <div class="mt-16 flex-end" *ngIf="totalCount">
                <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*PageSize + 1}}
                    {{ 'GLOBAL.to-small' | translate }} {{currOffset*PageSize + rowData?.length}}
                    {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
                <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,PageSize)'
                    (pageChange)="onPageChange($event)">
                </pagination>
            </div>
            <div class="flex-center-col h-100-250 min-h-250" *ngIf="searchTerm && updatedIntegrationList?.length === 0">
                <img src="assets/images/layered-cards.svg" alt="No account found">
                <div class="header-3 fw-600 text-center">No Account Found </div>
            </div>
        </div>
    </ng-template>
    <!-- bulk opertions -->
    <div class="justify-center">
        <div class="position-absolute bg-white bottom-12 br-12 shadow-sm p-10 z-index-2  align-center"
            [ngClass]="{'d-none': selectedCount === 0}">
            <div class="scrollbar ip-max-w-100-130 scroll-hide align-center">
                <div class="align-center">
                    <div class="fw-600 text-coal mr-20  text-nowrap">{{ selectedCount }}
                        {{ selectedCount > 1 ? 'Items' : 'Item' }} {{ 'LEADS.selected' | translate}}
                    </div>
                </div>
                <div class="flex-center">
                    <button *ngIf="canBulkReassign" type="button" class="btn-bulk" id="btnBulkReassign"
                        data-automate-id="btnBulkReassign" (click)="openBulkReassignModel()">
                        Bulk Reassign
                    </button>
                    <button type="button" class="btn-bulk"
                        (click)="openBulkProjectLocationModel(ProjectAndLocationModal)" *ngIf="canBulkAssignment">
                        {{'LEADS.bulk'| translate}} Project & Location
                    </button>
                    <button type="button" class="btn-bulk" (click)="openBulkCountryCodeModal(CountryCodeModal)"
                        *ngIf="canBulkAssignment">Bulk Country
                        Code</button>
                    <ng-container
                        *ngIf="displayName == 'Google ads landing page' || displayName == 'Google ads leads form' || displayName == 'Microsoft Ads'">
                        <button type="button" class="btn-bulk" (click)="openBulkAgencyModal(AgencyModal)"
                            *ngIf="canBulkAssignment">
                            {{'LEADS.bulk'| translate}} Agency
                        </button>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
    <!-- ProjectAndLocation -->
    <ng-template #ProjectAndLocationModal>
        <ng-container *ngIf="isShowProjectAndLocationModal">
            <div class="p-16 min-w-350 max-w-350 h-100-60 scrollbar">
                <img [type]="'leadrat'" [appImage]="image" alt="img" />
                <div class="align-center mt-30">
                    <span class="icon ic-chevron-left ic-xxs ic-black cursor-pointer mr-10"
                        (click)="isShowProjectAndLocationModal = false;isBulkAssignModel=true; modalService.hide()"></span>
                    <h3 class="fw-700">Configuration</h3>
                </div>
                <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
                    <div class="py-20 pl-10">
                        <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel"
                            class="fw-semi-bold fv-sm-caps">
                            Account Name(s)
                        </div>
                        <div *ngIf="isBulkAssignModel" class="fw-semi-bold fv-sm-caps">
                            Account Name
                        </div>
                        <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel">
                            <span class="fw-700 text-small" *ngFor="let name of selectedIntegrations; let last = last">
                                {{ name.accountName }}{{ !last ? ', ' : ' ' }}
                            </span>
                        </div>
                        <div class="fw-700 text-small" *ngIf="isBulkAssignModel">{{ selectedAccountName
                            }}</div>
                    </div>
                </div>
                <div class="field-label fw-semi-bold"> {{'PROJECTS.project' | translate}}
                </div>
                <ng-select [virtualScroll]="true" [items]="allProjectList" class="bg-white" bindLabel="name"
                    bindValue="id" ResizableDropdown placeholder="Select Project" [formControl]="project"></ng-select>
                <div class="field-label fw-semi-bold"> {{'LOCATION.location' | translate}}
                </div>
                <ng-select [virtualScroll]="true" [items]="placesList" class="bg-white" bindLabel="location"
                    bindValue="id" ResizableDropdown placeholder="Select Location" [formControl]="location"></ng-select>
                <ng-container *ngIf="isBulkAssignModel">
                    <div class="field-label fw-semi-bold">{{'LABEL.property' | translate}}
                    </div>
                    <ng-select [virtualScroll]="true" [items]="propertyList" class="bg-white" bindLabel="title"
                        [ngClass]="{'blinking pe-none': propertyListIsLoading}" bindValue="title" ResizableDropdown
                        placeholder="Select Property" [formControl]="property"></ng-select>
                    <div class="field-label fw-semi-bold">{{'REPORTS.agency' | translate}}
                    </div>
                    <ng-select [virtualScroll]="true" [items]="agencyNameList"
                        [ngClass]="{'blinking pe-none': agencyListIsLoading}" class="bg-white" ResizableDropdown
                        placeholder="Select Agency" [formControl]="agencyName"></ng-select>
                    <div class="field-label fw-semi-bold">{{'INTEGRATION.campaign' | translate}}
                    </div>
                    <ng-select [virtualScroll]="true" [items]="campaignList"
                        [ngClass]="{'blinking pe-none': campaignListIsLoading}" class="bg-white" ResizableDropdown
                        placeholder="Select Campaign" [formControl]="campaign"></ng-select>
                    <div class="field-label fw-semi-bold">{{'INTEGRATION.channel-partner' | translate}}
                    </div>
                    <ng-select [virtualScroll]="true" [items]="channelPartnerList"
                        [ngClass]="{'blinking pe-none': channelPartnerListIsLoading}" class="bg-white" ResizableDropdown
                        placeholder="Select Channelpartner" [formControl]="channelPartner"></ng-select>
                </ng-container>
            </div>
            <div class="flex-end p-16 box-shadow-20">
                <button type="button" class="btn-gray mr-20"
                    (click)="isShowProjectAndLocationModal = false;isBulkAssignModel=true; modalService.hide()">
                    {{ 'BUTTONS.cancel' | translate }}</button>
                <button type="button" class="btn-coal" (click)="updateProjectAndLocation()">
                    {{ 'BUTTONS.save' | translate }}</button>
            </div>
        </ng-container>
    </ng-template>
    <!-- agency -->
    <ng-template #AgencyModal>
        <ng-container *ngIf="isShowAgencyModal">
            <div class="p-16 min-w-350 max-w-350 h-100-44 scrollbar">
                <img [type]="'leadrat'" [appImage]="image" alt="img" />
                <div class="align-center mt-30">
                    <span class="icon ic-chevron-left ic-xxs ic-black cursor-pointer mr-10"
                        (click)="isShowAgencyModal = false; isBulkAssignModel=true; modalService.hide()"></span>
                    <h3 class="fw-700">{{ 'LEADS.assignment' | translate }}</h3>
                </div>
                <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
                    <div class="flex-column pt-20 pl-10 pb-20">
                        <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel"
                            class="fw-semi-bold fv-sm-caps">
                            Account Name(s)
                        </div>
                        <div *ngIf="isBulkAssignModel" class="fw-semi-bold fv-sm-caps">
                            Account Name
                        </div>
                        <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel">
                            <span class="fw-700 text-small" *ngFor="let name of selectedIntegrations; let last = last">
                                {{ name.accountName }}{{ !last ? ', ' : ' ' }}
                            </span>
                        </div>
                        <div class="fw-700 text-small" *ngIf="isBulkAssignModel">{{ selectedAccountName
                            }}</div>
                    </div>
                </div>
                <div class="field-label fw-semi-bold"> Agency
                </div>
                <ng-select [virtualScroll]="true" [items]="agencyNameList" class="bg-white" bindLabel="name"
                    bindValue="id" ResizableDropdown placeholder="ex. ABC" [formControl]="agencyName"></ng-select>

                <div class="flex-end p-16 box-shadow-20">
                    <button type="button" class="btn-gray mr-20"
                        (click)="isShowAgencyModal = false;isBulkAssignModel=true; modalService.hide()">
                        {{ 'BUTTONS.cancel' | translate }}</button>
                    <button type="button" class="btn-coal" (click)="updateAgentName()">
                        {{ 'BUTTONS.save' | translate }}</button>
                </div>
            </div>
        </ng-container>
    </ng-template>
    <!-- country -->
    <ng-template #CountryCodeModal>
        <ng-container *ngIf="isShowCountryCodeModal">
            <country-code [image]="image" [isBulkAssignModel]="isBulkAssignModel"
                [selectedAccountName]="selectedAccountName" [selectedIntegrations]="selectedIntegrations"
                [selectedAccountId]="selectedAccountId" [selectedCount]="selectedCount"
                [updatedIntegrationList]="updatedIntegrationList" [displayName]="displayName"
                (isShowCountryCodeModalChanged)="isShowCountryCodeModal = $event; selectedCountReset();"></country-code>
        </ng-container>
    </ng-template>
</form>