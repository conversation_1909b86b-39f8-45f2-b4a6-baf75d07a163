import { Injectable, EventEmitter } from '@angular/core';
import { BehaviorSubject, Subject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ShareDataService {
  selectionChanged: EventEmitter<any> = new EventEmitter<any>();
  fetchNextLeads: EventEmitter<any> = new EventEmitter<any>();
  fetchPreviousLeads: EventEmitter<any> = new EventEmitter<any>();
  dataUpdated: EventEmitter<any> = new EventEmitter<any>();
  currentPageNumber: number = 1;
  cardData:any=[];
  isPrevFetched:boolean;
  totalPages: number = 0;
  private showLeftNavSubject = new BehaviorSubject<boolean>(true);
  showLeftNav$ = this.showLeftNavSubject.asObservable();

  private sharedTabDataSubject = new BehaviorSubject<any>(null);
  sharedTabData$ = this.sharedTabDataSubject.asObservable();

  private projectIdSubject = new BehaviorSubject<number>(null);
  projectId$ = this.projectIdSubject.asObservable();

  private sharedFileSize = new BehaviorSubject<any>(null);
  sharedFileSize$ = this.sharedFileSize.asObservable();

  private stopNavigate = new BehaviorSubject<any>(null);
  stopNavigate$ = this.stopNavigate.asObservable();

  private validateBasicDetail = new BehaviorSubject<any>(null);
  validateBasicDetail$ = this.validateBasicDetail.asObservable();

  private currentURL = new BehaviorSubject<any>(null);
  URL$ = this.currentURL.asObservable();

  private isUnit = new Subject<void>();
  isProjectUnit$ = this.isUnit.asObservable();

  private shareBasicDetailCurrency = new BehaviorSubject<any>(null);
  shareBasicDetailCurrency$ = this.shareBasicDetailCurrency.asObservable();
  private marketingSelectedTab = new BehaviorSubject<any>(null);
  marketingSelectedTab$ = this.marketingSelectedTab.asObservable();
  
  private showWhatsappInComp = new BehaviorSubject<boolean>(true);
  showWhatsappInComp$ = this.showWhatsappInComp.asObservable();

  private gotoOverview = new BehaviorSubject<any>(null); 
  backtoOverview = this.gotoOverview.asObservable();


  // private showWhatsappInComp = new BehaviorSubject<boolean>(true);
  // showWhatsappInComp$ = this.showWhatsappInComp.asObservable();

  private patchMiniBookingForm = new Subject<any>();

  private DuplicateMethodTrigger = new Subject<any>();

  private selectedAppSource = new BehaviorSubject<any>(null);
  selectedApp$ = this.selectedAppSource.asObservable();

  private apiTriggerSubject = new BehaviorSubject<boolean>(false);
  apiTrigger$: Observable<boolean> = this.apiTriggerSubject.asObservable();

  constructor() { }

  stopShareNavigate(data: any) {
    this.stopNavigate.next(data);
  }

  getStopShareNavigate(): any {
    return this.stopNavigate.value;
  }

  updateSharedTabData(data: any) {
    this.sharedTabDataSubject.next(data);
  }

  emitSelectionChanged(data: any) {
    this.selectionChanged.emit(data);
  }

  emitFetchNextLeads(isMobileView: boolean = true): void {
    this.fetchNextLeads.emit(isMobileView);
  }

  emitFetchPreviousLeads(isMobileView: boolean = true): void {
    this.fetchPreviousLeads.emit({
      isMobileView,
      currentPageNumber: this.currentPageNumber,
    });
  }

  emitDataUpdated({ cardData, isPrevFetched }: any): void {
    this.cardData=cardData;
    this.isPrevFetched=isPrevFetched;
    this.dataUpdated.emit({ cardData, isPrevFetched });
  }

  setShowLeftNav(value: boolean): void {
    this.showLeftNavSubject.next(value);
  }

  triggerDuplicateMethod(data: any) {
    this.DuplicateMethodTrigger.next(data);
  }

  getDuplicateMethodTrigger() {
    return this.DuplicateMethodTrigger.asObservable();
  }

  setProjectTitleId(projectId: any) {
    this.projectIdSubject.next(projectId);
  }

  getProjectTitleId(): number {
    return this.projectIdSubject.value;
  }

  shareFileSize(data: any) {
    this.sharedFileSize.next(data);
  }

  getFileSize(): any {
    return this.sharedFileSize.value;
  }

  validateDetail(data: any) {
    this.validateBasicDetail.next(data);
  }

  setCurrentUrl(data: any) {
    this.currentURL.next(data);
  }

  isProjectUnit() {
    this.isUnit.next();
  }

  sendMiniBookingformData(message: any) {
    this.patchMiniBookingForm.next(message);
  }

  getMiniBookingformData(): Observable<any> {
    return this.patchMiniBookingForm.asObservable();
  }

  sendBasicDetailCurrency(data: any) {
    this.shareBasicDetailCurrency.next(data);
  }

  setMarketingSelectedTab(tab: any) {
    this.marketingSelectedTab.next(tab);
  }

  getMarketingSelectedTab(): Observable<any> {
    return this.marketingSelectedTab.asObservable();
  }
  getCardData(){
    return {
      cardData: this.cardData,
      isPrevFetched: this.isPrevFetched
    };
  }

  setShowWhatsappInComp(value: boolean): void {
    this.showLeftNavSubject.next(value);
  }
  // setShowWhatsappInComp(value: boolean): void {
  //   this.showLeftNavSubject.next(value);
  // }

  gotoOverviewTab(value: string) {
    this.gotoOverview.next(value);
  }

  setSelectedApp(app: any) {
    this.selectedAppSource.next(app);
  }

  triggerApiCall(value: any) {
    this.apiTriggerSubject.next(value);
  }
}
