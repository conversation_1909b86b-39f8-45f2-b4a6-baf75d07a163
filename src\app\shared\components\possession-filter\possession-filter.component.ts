import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Subject } from 'rxjs';
import { POSSESSION_DATE_FILTER_LIST } from 'src/app/app.constants';
import { PossessionType } from 'src/app/app.enum';
import { onPickerOpened, setTimeZoneDate } from 'src/app/core/utils/common.util';

@Component({
  selector: 'app-possession-filter',
  templateUrl: './possession-filter.component.html'
})
export class PossessionFilterComponent implements OnInit, OnDestroy, OnChanges {
  @Input() initialPossessionType: number | null = null;
  @Input() initialFromPossessionDate: string | null = null;
  @Input() initialToPossessionDate: string | null = null;
  @Input() userTimeZoneOffset: string | null = null;
  @Input() formControlNames: { possessionType: string; fromDate: string; toDate: string } = {
    possessionType: 'PossesionType',
    fromDate: 'FromPossesionDate',
    toDate: 'ToPossesionDate'
  };
  @Input() customDateFilterList: { displayName: string; value: string }[] | null = null;
  @Output() possessionFilterChange = new EventEmitter<{
    possessionType: number | null;
    fromDate: string | null;
    toDate: string | null;
  }>();
  @ViewChild('dtFromPossession') dtFromPossession: OwlDateTimeComponent<any>;
  @ViewChild('dtToPossession') dtToPossession: OwlDateTimeComponent<any>;

  dateFilterList = POSSESSION_DATE_FILTER_LIST;

  ngOnInit(): void {
    if (this.customDateFilterList) {
      this.dateFilterList = this.customDateFilterList;
    }
    this.initializeForm();
    this.initializeValues();
  }
  isOpenPossessionModal = false;
  selectedPossession: string | null = null;
  customDateValidation = true;
  isSearchClicked = false;
  currentDate = new Date();
  minToPossessionDate: Date | null = null;
  filterForm: FormGroup;
  onPickerOpened = onPickerOpened;
  private stopper = new Subject<void>();

  constructor(private fb: FormBuilder) { }



  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialPossessionType'] ||
      changes['initialFromPossessionDate'] ||
      changes['initialToPossessionDate']) {
      this.initializeValues();
    }
  }

  initializeForm(): void {
    this.filterForm = this.fb.group({
      [this.formControlNames.possessionType]: [null],
      [this.formControlNames.fromDate]: [null],
      [this.formControlNames.toDate]: [null]
    });
  }

  initializeValues(): void {
    if (this.initialPossessionType) {
      const possessionType = PossessionType[this.initialPossessionType] as string;
      if (possessionType) {
        this.selectedPossession = possessionType;
        this.filterForm.patchValue({
          [this.formControlNames.possessionType]: this.initialPossessionType
        });

        if (possessionType === 'Custom Date') {
          if (this.initialFromPossessionDate) {
            this.filterForm.patchValue({
              [this.formControlNames.fromDate]: this.initialFromPossessionDate
            });
          }

          if (this.initialToPossessionDate) {
            this.filterForm.patchValue({
              [this.formControlNames.toDate]: this.initialToPossessionDate
            });
          }
        }
      }
    }
  }

  toggleModal(): void {
    this.isOpenPossessionModal = !this.isOpenPossessionModal;
  }

  handlePossessionRangeChange(value: string): void {
    this.selectedPossession = value;
    const possessionTypeValue = PossessionType[value as keyof typeof PossessionType];
    this.filterForm.patchValue({
      [this.formControlNames.possessionType]: possessionTypeValue
    });
    if (value !== 'Custom Date') {
      this.filterForm.patchValue({
        [this.formControlNames.fromDate]: null,
        [this.formControlNames.toDate]: null
      });
      this.customDateValidation = true;
      setTimeout(() => {
        this.isOpenPossessionModal = false;
        this.emitFilterChange();
      });
    } else {
      this.customDateValidation = this.getFormValue(this.formControlNames.fromDate) !== null &&
        this.getFormValue(this.formControlNames.toDate) !== null;
    }
  }

  fromPossessionDateChange(event: any): void {
    if (event) {
      const fromDate = setTimeZoneDate(
        event,
        this.userTimeZoneOffset
      );
      this.filterForm.patchValue({
        [this.formControlNames.fromDate]: fromDate
      });
      this.customDateValidation = true;
      this.emitFilterChange();
    }
  }

  toPossessionDateChange(event: any): void {
    if (event) {
      const toDate = setTimeZoneDate(
        event,
        this.userTimeZoneOffset
      );
      this.filterForm.patchValue({
        [this.formControlNames.toDate]: toDate
      });
      this.customDateValidation = true;
      setTimeout(() => {
        this.isOpenPossessionModal = false;
        this.emitFilterChange();
      });
    }
  }

  fromMonthChanged(event: any): void {
    const date = new Date(event);
    const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const fromDate = setTimeZoneDate(
      firstDayOfMonth,
      this.userTimeZoneOffset
    );

    this.filterForm.patchValue({
      [this.formControlNames.fromDate]: fromDate
    });
    this.minToPossessionDate = new Date(fromDate);
    this.customDateValidation = true;
    this.dtFromPossession?.close();
    this.emitFilterChange();
  }

  minToDate() {
    if (!this.filterForm.value[this.formControlNames.fromDate]) {
      return null;
    }
    return new Date(this.filterForm.value[this.formControlNames.fromDate]);
  }

  toMonthChanged(event: any): void {
    const date = new Date(event);
    const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const toDate = setTimeZoneDate(
      lastDayOfMonth,
      this.userTimeZoneOffset
    );
    if (date < this.minToDate()) {
      return;
    }
    this.filterForm.patchValue({
      [this.formControlNames.toDate]: toDate
    });
    this.customDateValidation = true;
    this.dtToPossession?.close();
    setTimeout(() => {
      this.isOpenPossessionModal = false;
      this.emitFilterChange();
    });
  }

  possessionCloseModal(): void {
    this.isOpenPossessionModal = false;
    this.isSearchClicked = false;
    if (this.selectedPossession === 'Custom Date' &&
      !this.getFormValue(this.formControlNames.fromDate) &&
      !this.getFormValue(this.formControlNames.toDate)) {
      this.selectedPossession = null;
      this.filterForm.patchValue({
        [this.formControlNames.possessionType]: null
      });
      this.emitFilterChange();
    }
  }

  getSelectedPossessionDisplayName(): string {
    if (!this.selectedPossession) return '';
    const selectedOption = this.dateFilterList.find(option => option.value === this.selectedPossession);
    if (selectedOption) {
      if (this.selectedPossession === 'Custom Date') {
        if (this.getFormValue(this.formControlNames.fromDate) && this.getFormValue(this.formControlNames.toDate)) {
          const fromDate = new Date(this.getFormValue(this.formControlNames.fromDate));
          const toDate = new Date(this.getFormValue(this.formControlNames.toDate));
          const fromFormatted = `${fromDate.toLocaleString('default', { month: 'long' })} ${fromDate.getFullYear()}`;
          const toFormatted = `${toDate.toLocaleString('default', { month: 'long' })} ${toDate.getFullYear()}`;
          return `${fromFormatted} - ${toFormatted}`;
        } else if (this.getFormValue(this.formControlNames.fromDate)) {
          const fromDate = new Date(this.getFormValue(this.formControlNames.fromDate));
          const fromFormatted = `${fromDate.toLocaleString('default', { month: 'long' })} ${fromDate.getFullYear()}`;
          return `From ${fromFormatted}`;
        } else if (this.getFormValue(this.formControlNames.toDate)) {
          const toDate = new Date(this.getFormValue(this.formControlNames.toDate));
          const toFormatted = `${toDate.toLocaleString('default', { month: 'long' })} ${toDate.getFullYear()}`;
          return `To ${toFormatted}`;
        }
      }
      return selectedOption.displayName;
    }
    return this.selectedPossession;
  }

  getFormValue(controlName: string): any {
    return this.filterForm.get(controlName)?.value;
  }

  validateCustomDate(): boolean {
    if (this.selectedPossession === 'Custom Date') {
      this.customDateValidation = this.getFormValue(this.formControlNames.fromDate) !== null &&
        this.getFormValue(this.formControlNames.toDate) !== null;
      return this.customDateValidation;
    }
    return true;
  }

  emitFilterChange(): void {
    this.possessionFilterChange.emit({
      possessionType: this.getFormValue(this.formControlNames.possessionType),
      fromDate: this.getFormValue(this.formControlNames.fromDate),
      toDate: this.getFormValue(this.formControlNames.toDate)
    });
  }

  reset(): void {
    this.selectedPossession = null;
    this.filterForm.patchValue({
      [this.formControlNames.possessionType]: null,
      [this.formControlNames.fromDate]: null,
      [this.formControlNames.toDate]: null
    });
    this.emitFilterChange();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
