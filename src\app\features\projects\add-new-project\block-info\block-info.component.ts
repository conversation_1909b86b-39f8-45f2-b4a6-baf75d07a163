import { AfterViewInit, Component, EventEmitter, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store, select } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subscription, takeUntil } from 'rxjs';

import { MONTHS, PAGE_SIZE } from 'src/app/app.constants';
import { PossessionType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getPages, getProjectSubTypeDisplayName, getTimeZoneDate, onlyNumbers} from 'src/app/core/utils/common.util';
import { AddBlockInfoComponent } from 'src/app/features/projects/add-new-project/block-info/add-block-info/add-block-info.component';
import { BlockInfoActionComponent } from 'src/app/features/projects/add-new-project/block-info/block-info-action/block-info-action.component';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { BulkDeleteProjectBlock, FetchBlockById } from 'src/app/reducers/project/project.action';
import { getBlockData, getCurrOffSet } from 'src/app/reducers/project/project.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'block-info',
  templateUrl: './block-info.component.html',
})

export class BlocksInfoComponent implements OnInit, AfterViewInit {

  @ViewChild('dt1') dt1: OwlDateTimeComponent<any>;

  gridApi: GridApi;
  valCount = 0;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  basicInfoForm: FormGroup;
  areaSizeUnits: Array<any>;
  blockData: any;
  isDataAvailable: boolean = false;
  selectedDataId: any;
  totalCount: number;
  currOffset: number = 0;
  pageSize: number = PAGE_SIZE;
  getPages = getPages;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
  };
  selectedNodes: any;
  onlyNumbers = onlyNumbers;
  userBasicDetails: any;
  currentDate: Date;
  onGridReady(params: any): void {
    this.gridApi = params.api;
  }
  gridOptions: GridOptions = {};
  gridOptions1: GridOptions = {};
  noBlocks: boolean = false;
  rowData: any = [];
  private storeSubscription: Subscription;
  projectSubType: any;
  projectTypeList: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');
  getProjectSubTypeDisplayName = getProjectSubTypeDisplayName
  constructor(
    private modalRef: BsModalRef,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private store: Store<AppState>,
    private sharedDataService: ShareDataService,
    private modalService: BsModalService,
    private fb: FormBuilder,
    private gridOptionsService: GridOptionsService,
    public trackingService: TrackingService) {
    this.basicInfoForm = this.fb.group({
      title: [null, Validators.required],
      area: [null, Validators.required],
      areaUnit: [null, Validators.required],
      startDate: [null],
      endDate: [null],
      possessionDate: [null],
      noOfFloors: 0,

    });
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
      });

      this.sharedDataService.projectSubType$.subscribe((subTypeId: any) => {
        this.projectSubType = this.getProjectSubTypeDisplayName(subTypeId);
      });

    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        maxWidth: 50,
      },
      {
        headerName: (this.projectSubType == 'Plot' ? 'Phase' : 'Block') + ' Info',
        field: 'BlockInfo',
        valueGetter: (params: any) => {
          return `<p class="text-truncate-1">${params?.data?.name}</p>`;
        },
        cellRenderer: (params: any) => {
          return params?.value;
        },
      },
      {
        headerName: 'Start Date',
        field: 'StartDate',
        valueGetter: (params: any) => [
          params.data.startDate
            ? getTimeZoneDate(
              params.data.startDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userBasicDetails?.timeZoneInfo?.timeZoneName && this.userBasicDetails?.shouldShowTimeZone && params.value[0]
              ? '(' + this.userBasicDetails?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'End Date',
        field: 'End Date',
        valueGetter: (params: any) => [
          params.data.endDate
            ? getTimeZoneDate(
              params.data.endDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p>
              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userBasicDetails?.timeZoneInfo?.timeZoneName && this.userBasicDetails?.shouldShowTimeZone && params.value[0]
              ? '(' + this.userBasicDetails?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Possession Date',
        field: 'PossessionDate',
        valueGetter: (params: any) => {
          if (params?.data?.possesionType > 0 && params?.data?.possesionType < 5) {
            return `<p class="text-truncate-1">${PossessionType[params?.data?.possesionType]}</p>`;
          } else if (params?.data?.possessionDate) {
            const formattedDate = getTimeZoneDate(params?.data?.possessionDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset, 'monthYear');
            return `<p class="text-truncate-1">${formattedDate}</p>`;
          } else {
            return '--';
          }
        },
        cellRenderer: (params: any) => {
          return params?.value;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        // maxWidth: 90,
        // minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [this.selectedDataId],
        cellRenderer: BlockInfoActionComponent,
      },
    ];

    this.gridOptions.rowData = this.rowData;
  }

  ngOnInit(): void {

    this.storeSubscription = this.store.pipe(select(getCurrOffSet)).subscribe((data: any) => {
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: 1
      };
    });


    this.store.select(getBlockData).subscribe(data => {
      this.rowData = data[0]?.items ? data[0].items : [];
      this.totalCount = data[0]?.totalCount;
      this.pageSize = this.filtersPayload?.pageSize;
      this.currOffset = this.filtersPayload?.pageNumber - 1;
      if (data.length && data[0]?.items.length === 0 && this.filtersPayload.pageNumber > 1) {
        this.filtersPayload = {
          ...this.filtersPayload,
          pageNumber: this.filtersPayload.pageNumber - 1
        };
        this.currOffset = this.currOffset - 1;
        this.store.dispatch(new FetchBlockById(this.filtersPayload));
      }
    });

    this.store.dispatch(new FetchAreaUnitList());
    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });
    this.sharedDataService.updateSharedTabData(2);
  }

  ngAfterViewInit(): void {
    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.selectedDataId = params.id;
        this.sharedDataService.setProjectTitleId(params.id);
        this.filtersPayload = {
          ...this.filtersPayload,
          id: params.id
        };
        this.store.dispatch(new FetchBlockById(this.filtersPayload));
      }
    });
  }

  saveAndNext() {
    if (this.selectedDataId) {
      this.router.navigate(['/projects/edit-project/amenities/' + this.selectedDataId]);
    }
  }

  onPageChange(e: number) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.store.dispatch(new FetchBlockById(this.filtersPayload));
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.selectedNodes = this.gridApi
      ?.getSelectedNodes()
      .map((project: any) => {
        return project.data;
      });
    let initialState: any = {
      data: this.selectedNodes,
      class: 'right-modal modal-300',
    };
    this.modalRef = this.modalService.show(BulkDeleteModal, initialState);
  }

  bulkDelete(): void {
    const ids = this.gridApi
      ?.getSelectedNodes()
      .map((node: any) => node?.data?.id);
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: 1
    };
    this.store.dispatch(new BulkDeleteProjectBlock(ids, this.filtersPayload));
    this.modalService.hide();
  }

  hideModalAndResetForm() {
    this.basicInfoForm.reset();
  }

  manageProject() {
    this.router.navigate(['/projects/manage-projects']);
  }

  openCenterModal() {
    this.trackingService.trackFeature(`Web.ProjectBlockInfo.Button.AddBlock.Click`)
    let initialState: any = {
      selectedDataId: this.selectedDataId
    };
    this.modalService.show(AddBlockInfoComponent, {
      class: 'ip-modal-unset modal-600',
      initialState
    });
  }

  ngOnDestroy() {
    if (this.storeSubscription) {
      this.storeSubscription.unsubscribe();
    }
    this.stopper.next();
    this.stopper.complete();
  }

}
