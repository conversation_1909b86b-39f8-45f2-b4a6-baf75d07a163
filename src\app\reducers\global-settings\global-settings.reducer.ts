import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import { GlobalSettings } from 'src/app/core/interfaces/global-settings';
import {
  FetchAllSourcesSuccess,
  FetchCurrencyListSuccess,
  FetchGlobalSettingsAnonymousSuccess,
  FetchGlobalSettingsSuccess,
  FetchOTPGlobalSettingsSuccess,
  FetchTempVariablesSuccess,
  GlobalSettingsActionTypes,
} from 'src/app/reducers/global-settings/global-settings.actions';

export type GlobalSettingsState = {
  globalSettings: GlobalSettings;
  globalSettingsAnonymous: GlobalSettings;
  currencyList: any;
  globalAnonymousIsLoading: boolean;
  globalSettingsIsLoading: boolean;
  currencyListLoading: boolean;
  OTPGlobalSettings: any[];
  tempVariables: any;
  allSources: any[];
  allSourcesLoading: boolean;
};

const initialState: GlobalSettingsState = {
  globalSettings: {} as GlobalSettings,
  globalSettingsAnonymous: {} as GlobalSettings,
  currencyList: [],
  currencyListLoading: true,
  globalAnonymousIsLoading: true,
  globalSettingsIsLoading: true,
  OTPGlobalSettings: [],
  tempVariables: {},
  // Source visibility state has been removed
  allSources: [],
  allSourcesLoading: false,
};

export function globalSettingsReducer(
  state: GlobalSettingsState = initialState,
  action: Action
): GlobalSettingsState {
  switch (action.type) {
    case GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS:
      return {
        ...state,
        globalSettingsIsLoading: true
      };
    case GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_SUCCESS:
      return {
        ...state,
        globalSettings: (action as FetchGlobalSettingsSuccess).response,
        globalSettingsIsLoading: false
      };
    case GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_ANONYMOUS:
      return {
        ...state,
        globalAnonymousIsLoading: true,
      };
    case GlobalSettingsActionTypes.FETCH_GLOBAL_SETTINGS_ANONYMOUS_SUCCESS:
      return {
        ...state,
        globalSettingsAnonymous: (action as FetchGlobalSettingsAnonymousSuccess).response,
        globalAnonymousIsLoading: false,
      };
    case GlobalSettingsActionTypes.FETCH_CURRENCY_LIST:
      return {
        ...state,
        currencyListLoading: true,
      };
    case GlobalSettingsActionTypes.FETCH_CURRENCY_LIST_SUCCESS:
      return {
        ...state,
        currencyList: (action as FetchCurrencyListSuccess).response,
        currencyListLoading: false,
      };
    case GlobalSettingsActionTypes.OTP_GLOBAL_SETTINGS_SUCCESS:
      return {
        ...state,
        OTPGlobalSettings: (action as FetchOTPGlobalSettingsSuccess).resp,
      };
    case GlobalSettingsActionTypes.FETCH_TEMP_VARIABLES_SUCCESS:
      return {
        ...state,
        tempVariables: (action as FetchTempVariablesSuccess).resp,
      };
    case GlobalSettingsActionTypes.FETCH_ALL_SOURCES:
      return {
        ...state,
        allSourcesLoading: true,
      };
    case GlobalSettingsActionTypes.FETCH_ALL_SOURCES_SUCCESS:
      const sources = (action as FetchAllSourcesSuccess).payload;
      return {
        ...state,
        allSources: sources || [],
        allSourcesLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.globalSettings;

export const getGlobalSettings = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.globalSettings
);

export const getGlobalSettingsIsLoading = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.globalSettingsIsLoading
);

export const getGlobalSettingsAnonymous = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.globalSettingsAnonymous
);

export const getGlobalAnonymousIsLoading = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.globalAnonymousIsLoading
);

export const getDuplicateSettings = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.globalSettingsAnonymous?.duplicateFeatureInfo
);

export const getDuplicateSettingsAnonymous = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.globalSettingsAnonymous?.duplicateFeatureInfo
);

export const getCurrencyList = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.currencyList
);

export const getCurrencyListIsLoading = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.currencyListLoading
);

export const getOTPGlobalSettings = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.OTPGlobalSettings
);

export const getTempVariables = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.tempVariables
);

// Source visibility selectors have been removed

export const getAllSources = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.allSources
);

export const getAllSourcesLoading = createSelector(
  selectFeature,
  (state: GlobalSettingsState) => state.allSourcesLoading
);