import { Action } from "@ngrx/store";

export enum FilterActionTypes {
    SAVE_FILTER = '[Filter] Save Filter',
    SAVE_FILTER_SUCCESS = '[Filter] Save Filter Success',
    FETCH_FILTER = '[Filter] Fetch Filter',
    FETCH_FILTER_SUCCESS = '[Filter] Fetch Filter Success',
    DELETE_FILTER = '[Filter] Delete Filter',
    DELETE_FILTER_SUCCESS = '[Filter] Delete Filter Success',
    UPDATE_SAVED_FILTER = '[Filter] Update Saved Filter',
    UPDATE_SAVED_FILTER_SUCCESS = '[Filter] Update Saved Filter Success',
    FILTER_EXIST = '[Filter] Filter Exist',
    FILTER_EXIST_SUCCESS = '[Filter] Filter Exist Success',
}

export class SaveFilter implements Action {
    readonly type: string = FilterActionTypes.SAVE_FILTER;
    constructor(public payload: any) { }
}

export class SaveFilterSuccess implements Action {
    readonly type: string = FilterActionTypes.SAVE_FILTER_SUCCESS;
    constructor(public response: any) { }
}

export class FetchFilter implements Action {
    readonly type: string = FilterActionTypes.FETCH_FILTER;
    constructor() { }
}

export class FetchFilterSuccess implements Action {
    readonly type: string = FilterActionTypes.FETCH_FILTER_SUCCESS;
    constructor(public response: any) { }
}

export class DeleteFilter implements Action {
    readonly type: string = FilterActionTypes.DELETE_FILTER;
    constructor(public payload: any) { }
}

export class DeleteFilterSuccess implements Action {
    readonly type: string = FilterActionTypes.DELETE_FILTER_SUCCESS;
    constructor(public response: any) { }
}

export class UpdateSavedFilter implements Action {
    readonly type: string = FilterActionTypes.UPDATE_SAVED_FILTER;
    constructor(public payload: any) { }
}

export class UpdateSavedFilterSuccess implements Action {
    readonly type: string = FilterActionTypes.UPDATE_SAVED_FILTER_SUCCESS;
    constructor(public response: any) { }
}

export class FilterExist implements Action {
    readonly type: string = FilterActionTypes.FILTER_EXIST;
    constructor(public name: any, public module: any) { }
}

export class FilterExistSuccess implements Action {
    readonly type: string = FilterActionTypes.FILTER_EXIST_SUCCESS;
    constructor(public response: any) { }
}