import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DeleteFilter } from 'src/app/reducers/filter/filter.action';
import { UserConfirmationComponent } from '../user-confirmation/user-confirmation.component';

@Component({
  selector: 'saved-filter',
  templateUrl: './saved-filter.component.html',
})
export class SavedFilterComponent implements OnInit {

  @Input() filters: any;
  @Input() isMobileView: boolean = false;
  @Input() showFilters: boolean = false;
  @Output() editFilter = new EventEmitter<any>();
  @Output() selectFilter = new EventEmitter<any>();
  @Input() showCommunicationCount: boolean = false;
  @Input() showFilterCount: boolean = false;

  @Output() closeFilter = new EventEmitter<void>();
  filteredFilters: any[] = [];
  searchTerm: string = '';
  constructor(private _store: Store<any>, public modalService: BsModalService, public modalRef: BsModalRef) { }

  ngOnInit(): void {
    this.filteredFilters = this.filters;
  }

  onSearch(): void {
    const lowerCaseTerm = this.searchTerm.toLowerCase().replace(/\s+/g, '');
    this.filteredFilters = this.filters.filter((filter: any) =>
      filter.name.toLowerCase().replace(/\s+/g, '').includes(lowerCaseTerm)
    );
  }

  onEditFilter(event: any, filter: any) {
    event.stopPropagation();
    const parsedFilterCriteria = filter?.filterCriteria ? JSON.parse(filter.filterCriteria) : {};
    const filterWithParsedCriteria = {
      ...filter,
      filterCriteria: parsedFilterCriteria,
      id: filter?.id,
      name: filter?.name,
    };
    this.editFilter.emit(filterWithParsedCriteria);
  }

  onSelect(event: any, filter: any) {
    event.stopPropagation();
    const parsedFilterCriteria = filter?.filterCriteria ? JSON.parse(filter.filterCriteria) : {};
    const filterWithParsedCriteria = {
      ...filter,
      filterCriteria: {
        ...parsedFilterCriteria,
        showCommunicationCount: this.showCommunicationCount,
        showFilterCount: this.showFilterCount,
        dontCallApi: false,
      },
      id: filter?.id,
      name: filter?.name,
    };
    this.selectFilter.emit(filterWithParsedCriteria);
  }

  onDeleteFilter(event: any, filter: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: filter?.name,
      fieldType: 'filter',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-350 top-modal',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this._store.dispatch(new DeleteFilter(filter?.id));
          this.onClose(event);
        }
      });
    }
  }

  onClose(event: any) {
    event.stopPropagation()
    this.closeFilter.emit();
  }
}
