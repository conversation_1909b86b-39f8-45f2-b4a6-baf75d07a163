import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import {
  DomSanitizer,
  SafeResourceUrl,
  Title,
} from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getTenantName } from 'src/app/core/utils/common.util';
import { getUserProfile } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'this-applications',
  templateUrl: './this-applications.component.html',
})
export class ThisApplicationsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('iframeElement') iframeElement: ElementRef<HTMLIFrameElement>;
  safeUrl: SafeResourceUrl;
  applicationUrl: string = '';
  userDetails: any;
  productsList: any;
  applicationDetails: any;
  isLoading: boolean = true;
  private currentAppId: string = '';
  private loadingTimeout: any;

  constructor(
    private store: Store<AppState>,
    private sanitizer: DomSanitizer,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    public shareDataService: ShareDataService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getUserProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.userDetails = item;
      });

    this.shareDataService.selectedApp$
      .pipe(takeUntil(this.stopper))
      .subscribe((app) => {
        if (app && app.id !== this.currentAppId) {
          // Clear any existing loading timeout
          if (this.loadingTimeout) {
            clearTimeout(this.loadingTimeout);
          }

          this.currentAppId = app.id;
          this.applicationDetails = app;
          this.applicationUrl = app?.applicationUrl;
          this.metaTitle.setTitle(`CRM | ${app?.description || app?.name}`);
          this.headerTitle.setLangTitle(`${app?.description || app?.name}`);

          if (this.applicationUrl) {
            this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
              this.applicationUrl
            );

            // Refresh iframe if it exists
            if (this.iframeElement?.nativeElement) {
              this.refreshIframe();
            }
          } else {
            // Clear the safe URL if applicationUrl is invalid
            this.safeUrl = null;
            this.isLoading = false;
          }
        }
      });
  }

  ngAfterViewInit() {
    this.initIframe();
  }

  onIframeLoad() {
    // Clear loading timeout if it exists
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    this.isLoading = false;

    if (this.iframeElement?.nativeElement && this.applicationDetails) {
      const iframe = this.iframeElement.nativeElement;
      this.applicationDetails = {
        ...this.applicationDetails,
        tenantId: getTenantName(),
        isAdmin: localStorage.getItem('isAdmin') === 'true',
      }

      console.log('Application Details:', this.applicationDetails);

      iframe.contentWindow?.postMessage(
        {
          applicationDetails: this.applicationDetails,
          // userDetails: JSON.parse(localStorage.getItem('userDetails') || '{}'),
        },
        '*'
      );
    }
  }

  initIframe() {
    const iframe = this.iframeElement?.nativeElement;
    if (iframe && this.applicationUrl) {
      this.isLoading = true;
      iframe.onload = () => {
        // Managed timeout for faster loading
        this.loadingTimeout = setTimeout(() => this.onIframeLoad(), 300);
      };

      // Set the src to trigger iframe load
      iframe.src = this.applicationUrl;
    } else {
      this.isLoading = false;
    }
  }

  refreshIframe() {
    const iframe = this.iframeElement?.nativeElement;
    if (iframe && this.applicationUrl) {
      this.isLoading = true;

      // Optimized iframe refresh - direct src change for faster switching
      iframe.src = this.applicationUrl;

      // Add onload handler for the new src
      iframe.onload = () => {
        this.loadingTimeout = setTimeout(() => this.onIframeLoad(), 300);
      };
    } else {
      this.isLoading = false;
    }
  }

  ngOnDestroy() {
    // Clear any pending timeouts
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    this.stopper.next();
    this.stopper.complete();
  }
}
