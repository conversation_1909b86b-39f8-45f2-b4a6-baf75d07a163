import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { ColDef } from 'ag-grid-community';
import { BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';

import {
  ATTENDANCE_FILTERS_KEY_LABEL,
  MONTHS,
  PAGE_SIZE,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';

import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getISODate,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  onPickerOpened,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { ClockInOutComponent } from 'src/app/features/attendance/clock-in-out/clock-in-out.component';
import { UserDetailsComponent } from 'src/app/features/attendance/user-details/user-details.component';
import {
  FetchAttendanceExportStatus,
  FetchAttendanceExportStatusSuccess,
  FetchAttendanceList,
  UpdateFilterPayload,
} from 'src/app/reducers/attendance/attendance.actions';
import {
  getAttendanceList,
  getAttendanceLoading,
  getFiltersPayload,
} from 'src/app/reducers/attendance/attendance.reducer';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';

import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchDepartmentsList,
  FetchDesignationsList,
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getDepartmentsList,
  getDepartmentsListIsLoading,
  getDesignationsList,
  getDesignationsListIsLoading,
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportAttendanceTrackerComponent } from 'src/app/shared/components/export-attendance-tracker/export-attendance-tracker.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'attendance',
  templateUrl: './attendance.component.html',
})
export class AttendanceComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  columnDefs: ColDef[] = [];
  rowData: Array<any> = [];

  searchTerm: string;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  filtersPayload: any;
  gridColumnApi: any;
  gridApi: any;
  gridOptions: any;
  columnDropDown: any;
  appliedFilter: any;
  frameworkComponents = {
    dateCellRenderer: ClockInOutComponent,
    usernameCellRenderer: UserDetailsComponent,
  };
  getPages = getPages;
  totalCount: any;
  // currentMonth: Date = new Date();
  // selectedMonth: any = new Date().getMonth() + 1;
  // selectedYear: any = new Date().getFullYear();
  canViewAll: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  canExportAllUsers: boolean = false;
  isPresentMonth: boolean;
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  allUsers: Array<any> = [];
  usersList: Array<any> = [];
  reportees: Array<any> = [];
  departmentList: any[] = [];
  designationList: any[] = [];
  newDesignation: string = '';
  newDepartment: string = '';
  showLeftNav: boolean = true;
  users: Array<any> = [];
  // selectedMonthAndYear: any;
  maxDate: Date;
  @ViewChild('dt1') dt1: any;
  onlyReportees: Array<any> = [];
  isAttendanceLoading: boolean;
  isDesignationLoading: boolean;
  isUserListLoading: boolean;
  isDepartmentLoading: boolean;
  isReporteesWithInactiveLoading: boolean;
  canAttendanceExportAllUsers: boolean;
  canAttendanceExportReportees: boolean;
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  showFilters: boolean = false;
  attendanceFiltersKeyLabel = ATTENDANCE_FILTERS_KEY_LABEL;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  filterDate: any;
  dateRangeError: string = '';

  constructor(
    private headerTitle: HeaderTitleService,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    private gridOptionsService: GridOptionsService,
    public metaTitle: Title,
    private shareDataService: ShareDataService,
    private cdr: ChangeDetectorRef,
    public trackingService: TrackingService
  ) {
    super();
    this.metaTitle.setTitle('CRM | Attendance');
    this.headerTitle.setLangTitle('SIDEBAR.attendance');
    this._store.dispatch(new FetchDesignationsList());
    this._store.dispatch(new FetchDepartmentsList());
    this._store.dispatch(new FetchUsersListForReassignment());

    const now = this.currentDate;
    this.maxDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isUserListLoading = data));

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getDesignationsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isDesignationLoading = data));

    this._store
      .select(getDepartmentsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isDepartmentLoading = data));

    this._store
      .select(getOnlyReporteesWithInactiveIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (data: boolean) => (this.isReporteesWithInactiveLoading = data)
      );

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.users = data;
        this.usersList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.usersList = assignToSort(this.usersList, '');
        this.allUsers = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUsers = assignToSort(this.allUsers, '');
      });
    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.reportees = data;
        this.onlyReportees = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.onlyReportees = assignToSort(this.onlyReportees, '');
      });

    this._store
      .select(getAttendanceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items || [];
        this.totalCount = data.totalCount;
      });
    this._store
      .select(getDesignationsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.designationList = data
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this._store
      .select(getDepartmentsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.departmentList = data
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });
    this._store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        if (this.filtersPayload?.FromDate === undefined) {
          const now = this.currentDate;          
          const firstDay = setTimeZoneDate(new Date(now.getFullYear(), now.getMonth(), 1), this.userData?.baseUTcOffset);
          const lastDay = setTimeZoneDate(new Date(now.getFullYear(), now.getMonth() + 1, 0), this.userData?.baseUTcOffset);
          this.filterDate = [firstDay, lastDay];
        } else {
          this.filterDate = [this.filtersPayload.FromDate, this.filtersPayload.ToDate];
        }
        
        this.pageSize = this.filtersPayload?.pageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          // month: this.filtersPayload?.Month,
          // year: this.filtersPayload?.Year,
          userName: this.filtersPayload?.UserName,
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          userStatus: this.filtersPayload?.UserStatus,
          department: this.filtersPayload?.DepartmentIds,
          designation: this.filtersPayload?.DesignationIds,
          reportsTo: this.filtersPayload?.ReportsTo,
        };
      });
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterAttendanceList();
    });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this._store
      .select(getAttendanceLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isAttendanceLoading = data;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Attendance.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Attendance.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Attendance.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Attendance.ViewReportees'
        );
        this.canAttendanceExportAllUsers = permissionsSet.has(
          'Permissions.Attendance.ExportAllUsers'
        );
        this.canAttendanceExportReportees = permissionsSet.has(
          'Permissions.Attendance.ExportReportees'
        );

        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
        this.filterAttendanceList();
      });
    this.generateCalendarHeaders();
  }

  ngOnInit() {
    this.selectedPageSize = 10;
    this.cdr.detectChanges();
    this.trackingService.trackFeature(`Web.Attendance.Page.Attendance.Visit`);
  }

  generateCalendarHeaders() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    const fromDate = new Date(this.filterDate[0]);
    const toDate = new Date(this.filterDate[1]);
    const days = [];
    for (
      let date = new Date(fromDate); date <= toDate; date.setDate(date.getDate() + 1)
    ) {
      days.push(new Date(date));
    }
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        minWidth: 240,
        valueGetter: (params: any) => [params.data],
        cellRenderer: UserDetailsComponent,
      },
      {
        headerName: 'No.of Days',
        field: 'No.of Days',
        filter: false,
        minWidth: 95,
        maxWidth: 95,
        resizable: false,
        valueGetter: (params: any) => [params.data?.totalLoggedInDays],
        cellRenderer: (params: any) => {
          return `<p class="pl-40">${params.value}</p>`;
        },
      },
    ];
    for (const date of days) {
      const headerName = `${date.toLocaleString('default', { month: 'short' })} ${date.getDate()}`;
      const field = `day_${date.getDate()}`;
      this.gridOptions.columnDefs.push({
        headerName,
        field,
        cellRenderer: ClockInOutComponent,
        minWidth: 120,
        maxWidth: 120,
        resizable: false,
        valueGetter: (params: any) => [
          params.data
            ? {
              date: getISODate(date),
              data: params.data,
              pastDate: date <= this.currentDate,
            }
            : '',
        ],
      });
    }
  }

  // previousMonth() {
  //   const previousMonth = new Date(
  //     this.currentMonth.getFullYear(),
  //     this.currentMonth.getMonth() - 1,
  //     1
  //   );
  //   const today = new Date(this.currentDate); // Get the current date
  //   this.checkCurrentMonth();
  //   this.trackingService.trackFeature(`Web.Attendance.PreviousMonthFilter.Click`);
  //   if (previousMonth < today) {
  //     this.currentMonth = previousMonth;
  //     this.selectedMonth = this.currentMonth.getMonth() + 1;
  //     this.selectedYear = this.currentMonth.getFullYear();
  //     this.generateCalendarHeaders();
  //   }
  //   this.filtersPayload = {
  //     ...this.filtersPayload,
  //     Month: this.selectedMonth,
  //     Year: this.selectedYear,
  //   };
  //   this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
  //   this._store.dispatch(new FetchAttendanceList());
  //   this.generateCalendarHeaders();
  // }

  // nextMonth() {
  //   const nextMonth = new Date(
  //     this.currentMonth.getFullYear(),
  //     this.currentMonth.getMonth() + 1,
  //     1
  //   );
  //   const today = new Date(this.currentDate); // Get the current date
  //   this.checkCurrentMonth();
  //   this.trackingService.trackFeature(`Web.Attendance.NextMonthFilter.Click`);
  //   if (nextMonth <= today) {
  //     this.currentMonth = nextMonth;
  //     this.selectedMonth = this.currentMonth.getMonth() + 1;
  //     this.selectedYear = this.currentMonth.getFullYear();
  //     this.generateCalendarHeaders();
  //   }
  //   this.filtersPayload = {
  //     ...this.filtersPayload,
  //     Month: this.selectedMonth,
  //     Year: this.selectedYear,
  //   };
  //   this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
  //   this._store.dispatch(new FetchAttendanceList());
  //   this.generateCalendarHeaders();
  // }

  openAttendanceTracker() {
    let payload: any = {
      trackerPermission: this.canAttendanceExportAllUsers ? 0 : 1,
    };
    this.trackingService.trackFeature(`Web.Attendance.Button.ExportTracker.Click`);
    this._store.dispatch(new FetchAttendanceExportStatus(payload, 1, 10));
    this.modalService.show(ExportAttendanceTrackerComponent, {
      class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
    });
  }
  
  // monthChanged(event: any) {
  //   if (new Date(event) > new Date()) {
  //     this.dt1.close();
  //     return;
  //   }
  //   this.selectedMonthAndYear = event;
  //   this.onDateSelection();
  //   this.dt1.close();
  // }

  // onDateSelection() {
  //   this.currentMonth = this.selectedMonthAndYear;
  //   this.selectedMonth =
  //     MONTHS.indexOf(this.selectedMonthAndYear.toString().slice(4, 7)) + 1;
  //   this.selectedYear = this.selectedMonthAndYear.toString().slice(11, 15);
  //   this.checkCurrentMonth();
  //   const today = new Date(this.currentDate); // Get the current date
  //   if (this.selectedMonthAndYear <= today) {
  //     // Remove below 3 lines if not needed
  //     this.currentMonth = this.selectedMonthAndYear;
  //     this.selectedMonth =
  //       MONTHS.indexOf(this.selectedMonthAndYear.toString().slice(4, 7)) + 1;
  //     this.selectedYear = this.selectedMonthAndYear.toString().slice(11, 15);
  //     this.generateCalendarHeaders();
  //   }
  //   this.filtersPayload = {
  //     ...this.filtersPayload,
  //     Month: this.selectedMonth,
  //     Year: this.selectedYear,
  //   };
  //   this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
  //   this._store.dispatch(new FetchAttendanceList());
  //   this.generateCalendarHeaders();
  // }

  // checkCurrentMonth() {
  //   const currentDate = new Date(this.currentDate); // Get the current date
  //   const currentYear = currentDate.getFullYear();
  //   const currentMonth = currentDate.getMonth();
  //   this.isPresentMonth =
  //     +this.selectedYear === +currentYear &&
  //     +this.currentMonth.getMonth() === +currentMonth;
  //   this.selectedMonthAndYear = new Date(
  //     `${this.selectedYear}-${this.currentMonth.getMonth() + 1}-01`
  //   );
  // }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
    this.trackingService.trackFeature(`Web.Attendance.Button.AdvancedFilter.Click`);
  }

  filterAttendanceList() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      // Month: this.selectedMonth,
      // Year: this.selectedYear,
      UserName: this.searchTerm,
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      DepartmentIds: this.appliedFilter.department,
      DesignationIds: this.appliedFilter.designation,
      UserStatus: this.appliedFilter.userStatus,
      ReportsTo: this.appliedFilter.reportsTo,
      AttendancePermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      FromDate: this.filterDate?.[0],
      ToDate: this.filterDate?.[1],
    };
    if (
      this.appliedFilter?.users?.length ||
      this.appliedFilter?.department?.length ||
      this.appliedFilter?.designation?.length ||
      this.appliedFilter?.reportsTo?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchAttendanceList());
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      [
        // 'year',
        'withTeam',
        'userStatus',
        'pageNumber',
        'pageSize',
        // 'month',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    return values?.toString()?.split(',');
  }

  onRemoveFilter(key: string, value: string) {
    this.appliedFilter[key] = this.appliedFilter[key]?.filter(
      (item: any) => item !== value
    );
    this.filterAttendanceList();
    this.generateCalendarHeaders();
  }

  getReportsName(id: string) {
    let propertyType = '';
    this.users?.forEach((type: any) => {
      if (type.id === id) {
        propertyType = type.firstName + ' ' + type.lastName;
      }
    });
    return propertyType;
  }

  getDepartmentName(id: string) {
    let propertyType = '';
    this.departmentList.forEach((type: any) => {
      if (type.id === id) {
        propertyType = type.name;
      }
    });
    return propertyType;
  }

  getDesignationName(id: string) {
    let propertyType = '';
    this.designationList.forEach((type: any) => {
      if (type.id === id) {
        propertyType = type.name;
      }
    });
    return propertyType;
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.trackingService.trackFeature(`Web.Attendance.Option.${this.pageSize}.Click`);
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchAttendanceList());
    this.currOffset = 0;
  }

  applyFilter() {
    this.filterAttendanceList();
    this.modalService.hide();
  }

  reset() {
    const now = this.currentDate;
    const firstDay = setTimeZoneDate(new Date(now.getFullYear(), now.getMonth(), 1), this.userData?.baseUTcOffset);
    const lastDay = setTimeZoneDate(new Date(now.getFullYear(), now.getMonth() + 1, 0), this.userData?.baseUTcOffset);
    this.filterDate = [firstDay, lastDay];
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterAttendanceList();
    this.generateCalendarHeaders();
  }

  currentVisibility(visibility: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    this.filterAttendanceList();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.trackingService.trackFeature(`Web.Attendance.Menu.Active.Click`);
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.trackingService.trackFeature(`Web.Attendance.Menu.Inactive.Click`);
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.trackingService.trackFeature(`Web.Attendance.Menu.All.Click`);
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchAttendanceList());
  }

  exportAttendanceReport() {
    this._store.dispatch(new FetchAttendanceExportStatusSuccess([]));
    this.filterAttendanceList();
    this.trackingService.trackFeature(`Web.Attendance.Button.Export.Click`);
    let initialState: any = {
      payload: this.filtersPayload,
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.trackingService.trackFeature(`Web.Attendance.DataEntry.Search.DataEntry`);
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  onDateRangeChange(dates: [Date, Date]) {
    const fromDate = setTimeZoneDate(dates[0], this.userData?.timeZoneInfo?.baseUTcOffset);
    const toDate = setTimeZoneDate(dates[1], this.userData?.timeZoneInfo?.baseUTcOffset);
    const fromDateObj = typeof fromDate === 'string' ? new Date(fromDate) : fromDate;
    const toDateObj = typeof toDate === 'string' ? new Date(toDate) : toDate;

    // Calculate the difference in days
    const diffInMs = toDateObj.getTime() - fromDateObj.getTime();
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24) + 1;

    if (diffInDays < 10) {
      this.dateRangeError = 'Please select a date range of at least 10 days.';
      return;
    } else {
      this.dateRangeError = '';
    }

    this.filterDate = [fromDate, toDate];
    this.appliedFilter = {
      ...this.appliedFilter,
      FromDate: fromDate,
      ToDate: toDate,
    };
    this.applyFilter();
    this.generateCalendarHeaders();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
