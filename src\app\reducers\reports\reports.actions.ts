import { Action } from '@ngrx/store';
import { FetchResponse } from 'src/app/reducers/lead/lead.reducer';

export enum ReportsActionTypes {
    FETCH_REPORTS_USER = '[REPORTS] Fetch User',
    FETCH_REPORTS_USER_SUCCESS = '[REPORTS] Fetch User Success',
    FETCH_REPORTS_USER_TOTAL_COUNT = '[REPORTS] Fetch User Reports Total Count',
    FETCH_REPORTS_USER_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch User Reports Total Count Success',
    FETCH_REPORTS_CUSTOM_USER = '[REPORTS] Fetch Custom User',
    FETCH_REPORTS_CUSTOM_USER_SUCCESS = '[REPORTS] Fetch Custom User Success',
    FETCH_REPORTS_CUSTOM_USER_TOTAL_COUNT = '[REPORTS] Fetch Custom User Reports Total Count',
    FETCH_REPORTS_CUSTOM_USER_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Custom User Reports Total Count Success',

    UPDATE_USER_FILTER_PAYLOAD = '[REPORTS] Update User Filter Payload',
    FETCH_REPORTS_USER_EXPORT = '[REPORTS] Fetch User Export',
    FETCH_REPORTS_USER_EXPORT_SUCCESS = '[REPORTS] Fetch User Export Success',
    EXPORT_USER_STATUS = '[REPORTS] Export Users Status through excel',
    EXPORT_USER_STATUS_SUCCESS = '[REPORTS] Export Users Status through excel Success',

    FETCH_REPORTS_PROJECT = '[REPORTS] Fetch Project',
    FETCH_REPORTS_PROJECT_SUCCESS = '[REPORTS] Fetch Project Success',
    FETCH_REPORTS_PROJECT_TOTAL_COUNT = '[REPORTS] Fetch Project Reports Total Count',
    FETCH_REPORTS_PROJECT_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Project Reports Total Count Success',
    UPDATE_PROJECT_FILTER_PAYLOAD = '[REPORTS] Update Project Filter Payload',
    FETCH_REPORTS_PROJECT_EXPORT = '[REPORTS] Fetch Project Export',
    FETCH_REPORTS_PROJECT_EXPORT_SUCCESS = '[REPORTS] Fetch Project Export Success',
    EXPORT_PROJECT_STATUS = '[REPORTS] Export Projects Status through excel',
    EXPORT_PROJECT_STATUS_SUCCESS = '[REPORTS] Export Projects Status through excel Success',

    FETCH_REPORTS_SOURCES = '[REPORTS] Fetch Sources ',
    FETCH_REPORTS_SOURCES_SUCCESS = '[REPORTS] Fetch Sources Success',
    FETCH_REPORTS_SOURCE_TOTAL_COUNT = '[REPORTS] Fetch Source Reports Total Count',
    FETCH_REPORTS_SOURCE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Source Reports Total Count Success',
    FETCH_REPORTS_CUSTOM_SOURCES = '[REPORTS] Fetch Custom Sources ',
    FETCH_REPORTS_CUSTOM_SOURCES_SUCCESS = '[REPORTS] Fetch Custom Sources Success',
    FETCH_REPORTS_CUSTOM_SOURCE_TOTAL_COUNT = '[REPORTS] Fetch Custom Source Reports Total Count',
    FETCH_REPORTS_CUSTOM_SOURCE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Custom Source Reports Total Count Success',
    UPDATE_SOURCES_FILTER_PAYLOAD = '[REPORTS] Update Sources Filter Payload',
    FETCH_REPORTS_SOURCE_EXPORT = '[REPORTS] Fetch Source Export',
    FETCH_REPORTS_SOURCE_EXPORT_SUCCESS = '[REPORTS] Fetch Source Export Success',
    EXPORT_SOURCE_STATUS = '[REPORTS] Export Sources Status through excel',
    EXPORT_SOURCE_STATUS_SUCCESS = '[REPORTS] Export Sources Status through excel Success',

    FETCH_REPORTS_SUB_SOURCES = '[REPORTS] Fetch Sub Sources ',
    FETCH_REPORTS_SUB_SOURCES_SUCCESS = '[REPORTS] Fetch Sub Sources Success',
    FETCH_REPORTS_SUB_SOURCE_TOTAL_COUNT = '[REPORTS] Fetch Sub Source Reports Total Count',
    FETCH_REPORTS_SUB_SOURCE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Sub Source Reports Total Count Success',
    FETCH_REPORTS_CUSTOM_SUB_SOURCES = '[REPORTS] Fetch Custom Sub Sources ',
    FETCH_REPORTS_CUSTOM_SUB_SOURCES_SUCCESS = '[REPORTS] Fetch Custom Sub Sources Success',
    FETCH_REPORTS_CUSTOM_SUB_SOURCE_TOTAL_COUNT = '[REPORTS] Fetch Custom Sub Source Reports Total Count',
    FETCH_REPORTS_CUSTOM_SUB_SOURCE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Custom Sub Source Reports Total Count Success',

    UPDATE_SUB_SOURCES_FILTER_PAYLOAD = '[REPORTS] Update Sub Sources Filter Payload',
    FETCH_REPORTS_SUB_SOURCE_EXPORT = '[REPORTS] Fetch Sub Source Export',
    FETCH_REPORTS_SUB_SOURCE_EXPORT_SUCCESS = '[REPORTS] Fetch Sub Source Export Success',
    EXPORT_SUB_SOURCE_STATUS = '[REPORTS] Export Sub Sources Status through excel',
    EXPORT_SUB_SOURCE_STATUS_SUCCESS = '[REPORTS] Export Sub Sources Status through excel Success',

    FETCH_REPORTS_AGENCY = '[REPORTS] Fetch Agency ',
    FETCH_REPORTS_AGENCY_SUCCESS = '[REPORTS] Fetch Agency Success',
    FETCH_REPORTS_AGENCY_CUSTOM = '[REPORTS] Fetch Custom Agency',
    FETCH_REPORTS_AGENCY_CUSTOM_SUCCESS = '[REPORTS] Fetch Custom Agency Success',
    FETCH_REPORTS_AGENCY_TOTAL_COUNT = '[REPORTS] Fetch Agency Reports Total Count',
    FETCH_REPORTS_AGENCY_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Agency Reports Total Count Success',
    FETCH_REPORTS_AGENCY_CUSTOM_TOTAL_COUNT = '[REPORTS] Fetch Custom Agency Reports Total Count',
    FETCH_REPORTS_AGENCY_CUSTOM_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Custom Agency Reports Total Count Success',
    UPDATE_AGENCY_FILTER_PAYLOAD = '[REPORTS] Update Agency Filter Payload',
    FETCH_REPORTS_AGENCY_EXPORT = '[REPORTS] Fetch Agency Export',
    FETCH_REPORTS_AGENCY_EXPORT_SUCCESS = '[REPORTS] Fetch Agency Export Success',
    EXPORT_AGENCY_STATUS = '[REPORTS] Export Agency Status through excel',
    EXPORT_AGENCY_STATUS_SUCCESS = '[REPORTS] Export Agency Status through excel Success',

    CLEAN_MEETING_VISIT_REPORT_LIST = '[REPORTS] Clean Meeting Visit Report List',
    FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1 = '[REPORTS] Fetch Meeting Site visit Reports Level1',
    FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1_SUCCESS = '[REPORTS] Fetch Meeting Site visit Reports Level1 Success',
    FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2 = '[REPORTS] Fetch Meeting Site visit Reports Level2',
    FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2_SUCCESS = '[REPORTS] Fetch Meeting Site visit Reports Level2 Success',
    FETCH_REPORTS_MEETING_SITE_VISIT_TOTAL_COUNT = '[REPORTS] Fetch Meeting Site visit Reports Total Count',
    FETCH_REPORTS_MEETING_SITE_VISIT_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Meeting Site visit Reports Total Count Success',
    UPDATE_MEETING_SITE_VISIT_FILTER_PAYLOAD = '[REPORTS] Update Meeting Site visit Filter Payload',
    FETCH_REPORTS_MEETING_SITE_VISIT_EXPORT = '[REPORTS] Fetch Meeting Site visit Export',
    FETCH_REPORTS_MEETING_SITE_VISIT_EXPORT_SUCCESS = '[REPORTS] Fetch Meeting Site visit Export Success',
    EXPORT_MEETING_SITE_VISIT_STATUS = '[REPORTS] Export Meeting Site visit Status through excel',
    EXPORT_MEETING_SITE_VISIT_STATUS_SUCCESS = '[REPORTS] Export Meeting Site visit Status through excel Success',

    FETCH_REPORTS_ACTIVITY = '[REPORTS] Fetch Activity',
    FETCH_REPORTS_ACTIVITY_SUCCESS = '[REPORTS] Fetch Activity Success',
    FETCH_REPORTS_ACTIVITY1 = '[REPORTS] Fetch Activity 1',
    FETCH_REPORTS_ACTIVITY1_SUCCESS = '[REPORTS] Fetch Activity 1 Success',
    FETCH_REPORTS_ACTIVITY2 = '[REPORTS] Fetch Activity 2',
    FETCH_REPORTS_ACTIVITY2_SUCCESS = '[REPORTS] Fetch Activity 2 Success',
    FETCH_REPORTS_ACTIVITY3 = '[REPORTS] Fetch Activity 3',
    FETCH_REPORTS_ACTIVITY3_SUCCESS = '[REPORTS] Fetch Activity 3 Success',
    FETCH_REPORTS_ACTIVITY4 = '[REPORTS] Fetch Activity 4',
    FETCH_REPORTS_ACTIVITY4_SUCCESS = '[REPORTS] Fetch Activity 4 Success',
    FETCH_REPORTS_ACTIVITY5 = '[REPORTS] Fetch Activity 5',
    FETCH_REPORTS_ACTIVITY5_SUCCESS = '[REPORTS] Fetch Activity 5 Success',
    FETCH_REPORTS_ACTIVITY6 = '[REPORTS] Fetch Activity 6',
    FETCH_REPORTS_ACTIVITY6_SUCCESS = '[REPORTS] Fetch Activity 6 Success',
    FETCH_REPORTS_ACTIVITY7 = '[REPORTS] Fetch Activity 7',
    FETCH_REPORTS_ACTIVITY7_SUCCESS = '[REPORTS] Fetch Activity 7 Success',
    FETCH_REPORTS_ACTIVITY9 = '[REPORTS] Fetch Activity 9',
    FETCH_REPORTS_ACTIVITY9_SUCCESS = '[REPORTS] Fetch Activity 9 Success',
    FETCH_REPORTS_ACTIVITY10 = '[REPORTS] Fetch Activity 10',
    FETCH_REPORTS_ACTIVITY10_SUCCESS = '[REPORTS] Fetch Activity 10 Success',
    FETCH_REPORTS_ACTIVITY11 = '[REPORTS] Fetch Activity 11',
    FETCH_REPORTS_ACTIVITY11_SUCCESS = '[REPORTS] Fetch Activity 11 Success',
    FETCH_REPORTS_ACTIVITY12 = '[REPORTS] Fetch Activity 12',
    FETCH_REPORTS_ACTIVITY12_SUCCESS = '[REPORTS] Fetch Activity 12 Success',

    FETCH_REPORTS_ACTIVITY_TOTAL_COUNT = '[REPORTS] Fetch Activity Reports Total Count',
    FETCH_REPORTS_ACTIVITY_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Activity Reports Total Count Success',
    UPDATE_ACTIVITY_FILTER_PAYLOAD = '[REPORTS] Update Activity Filter Payload',
    UPDATE_ALL_ACTIVITY_FILTER_PAYLOAD = '[REPORTS] Update All Activity Filter Payload',
    FETCH_REPORTS_ACTIVITY_EXPORT = '[REPORTS] Fetch Activity Export',
    FETCH_REPORTS_ACTIVITY_EXPORT_SUCCESS = '[REPORTS] Fetch Activity Export Success',
    EXPORT_ACTIVITY = '[REPORTS] Export Activity through excel',
    EXPORT_ACTIVITY_SUCCESS = '[REPORTS] Export Activity through excel Success',

    FETCH_REPORTS_SUB_STATUS = '[REPORTS] Fetch Sub Status',
    FETCH_REPORTS_SUB_STATUS_SUCCESS = '[REPORTS] Fetch Sub Status Success',
    FETCH_REPORTS_SUB_STATUS_TOTAL_COUNT = '[REPORTS] Fetch Sub Status Reports Total Count',
    FETCH_REPORTS_SUB_STATUS_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Sub Status Reports Total Count Success',
    UPDATE_SUB_STATUS_FILTER_PAYLOAD = '[REPORTS] Update Sub Status Filter Payload',
    FETCH_REPORTS_SUB_STATUS_EXPORT = '[REPORTS] Fetch Sub Status Export',
    FETCH_REPORTS_SUB_STATUS_EXPORT_SUCCESS = '[REPORTS] Fetch Sub Status Export Success',
    EXPORT_SUB_STATUS = '[REPORTS] Export Sub Status through excel',
    EXPORT_SUB_STATUS_SUCCESS = '[REPORTS] Export Sub Status through excel Success',

    FETCH_SUB_REPORT = '[REPORTS] Fetch Substatus vs Subsource',
    FETCH_SUB_REPORT_SUCCESS = '[REPORTS] Fetch Substatus vs Subsource Success',
    FETCH_REPORTS_SUB_TOTAL_COUNT = '[REPORTS] Fetch Substatus vs Subsource Reports Total Count',
    FETCH_REPORTS_SUB_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Substatus vs Subsource Reports Total Count Success',
    UPDATE_SUB_REPORT_FILTER_PAYLOAD = '[REPORTS] Update Substatus vs Subsource Filter Payload',
    FETCH_SUB_REPORT_EXPORT = '[REPORTS] Fetch Substatus vs Subsource Export',
    FETCH_SUB_REPORT_EXPORT_SUCCESS = '[REPORTS] Fetch Substatus vs Subsource Export Success',
    EXPORT_SUB_REPORT = '[REPORTS] Export Substatus vs Subsource through excel',
    EXPORT_SUB_REPORT_SUCCESS = '[REPORTS] Export Substatus vs Subsource through excel Success',

    FETCH_REPORTS_PROJECT_SUB_STATUS = '[REPORTS] Fetch Project Substatus',
    FETCH_REPORTS_PROJECT_SUB_STATUS_SUCCESS = '[REPORTS] Fetch Project Substatus Success',
    FETCH_REPORTS_PROJ_SUB_STATUS_TOTAL_COUNT = '[REPORTS] Fetch Project Substatus Reports Total Count',
    FETCH_REPORTS_PROJ_SUB_STATUS_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Project Substatus Reports Total Count Success',
    UPDATE_PROJECT_SUB_STATUS_FILTER_PAYLOAD = '[REPORTS] Update Project Substatus Filter Payload',
    FETCH_REPORTS_PROJECT_SUB_STATUS_EXPORT = '[REPORTS] Fetch Project Substatus Export',
    FETCH_REPORTS_PROJECT_SUB_STATUS_EXPORT_SUCCESS = '[REPORTS] Fetch Project Substatus Export Success',
    EXPORT_PROJECT_SUB_STATUS = '[REPORTS] Export Projects Substatus through excel',
    EXPORT_PROJECT_SUB_STATUS_SUCCESS = '[REPORTS] Export Projects Substatus through excel Success',

    FETCH_REPORTS_CALL = '[REPORTS] Fetch Call ',
    FETCH_REPORTS_CALL_SUCCESS = '[REPORTS] Fetch Call Success',
    FETCH_REPORTS_CALL_TOTAL_COUNT = '[REPORTS] Fetch Call Reports Total Count',
    FETCH_REPORTS_CALL_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Call Reports Total Count Success',
    UPDATE_CALL_FILTER_PAYLOAD = '[REPORTS] Update Call Filter Payload',
    FETCH_REPORTS_CALL_EXPORT = '[REPORTS] Fetch Call Export',
    FETCH_REPORTS_CALL_EXPORT_SUCCESS = '[REPORTS] Fetch Call Export Success',
    EXPORT_CALL_STATUS = '[REPORTS] Export Call Status through excel',
    EXPORT_CALL_STATUS_SUCCESS = '[REPORTS] Export Call Status through excel Success',

    FETCH_REPORTS_RECEIVED_DATE = '[REPORTS] Fetch Received Date Reports',
    FETCH_REPORTS_RECEIVED_DATE_SUCCESS = '[REPORTS] Fetch Received Date Reports Success',
    FETCH_REPORTS_RECEIVED_DATE_TOTAL_COUNT = '[REPORTS] Fetch Received Date Reports Total Count',
    FETCH_REPORTS_RECEIVED_DATE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Received Date Reports Total Count Success',
    UPDATE_RECEIVED_DATE_FILTER_PAYLOAD = '[REPORTS] Update Received Date Filter Payload',
    FETCH_REPORTS_RECEIVED_DATE_EXPORT = '[REPORTS] Fetch Received Date Export',
    FETCH_REPORTS_RECEIVED_DATE_EXPORT_SUCCESS = '[REPORTS] Fetch Received Date Export Success',
    EXPORT_RECEIVED_DATE_STATUS = '[REPORTS] Export Received Date Status through excel',
    EXPORT_RECEIVED_DATE_STATUS_SUCCESS = '[REPORTS] Export Received Date Status through excel Success',

    FETCH_EXPORT_TRACKER = '[REPORTS] Fetch Export Tracker List',
    FETCH_EXPORT_TRACKER_SUCCESS = '[REPORTS] Fetch Export Tracker List Success',

    FETCH_FLAG_COUNT = '[REPORTS] Fetch Flag count List',
    FETCH_FLAG_COUNT_SUCCESS = '[REPORTS] Fetch Flag count List Success',

    FETCH_REPORTS_USER_SOURCE = '[REPORTS] Fetch User Source',
    FETCH_REPORTS_USER_SOURCE_SUCCESS = '[REPORTS] Fetch User Source Success',
    UPDATE_USER_SOURCE_FILTER_PAYLOAD = '[REPORTS] Update User Source Filter Payload',
    FETCH_REPORTS_USER_SOURCE_TOTAL_COUNT = '[REPORTS] Fetch User Source Reports Total Count',
    FETCH_REPORTS_USER_SOURCE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch User Source Reports Total Count Success',
    FETCH_REPORTS_USER_SOURCE_EXPORT = '[REPORTS] Fetch User Source Export',
    FETCH_REPORTS_USER_SOURCE_EXPORT_SUCCESS = '[REPORTS] Fetch User Source Export Success',
    EXPORT_USER_SOURCE_REPORT = '[REPORTS] Export User Source',
    EXPORT_USER_SOURCE_REPORT_SUCCESS = '[REPORTS] Export User Source Success',

    FETCH_REPORTS_USER_SUB_SOURCE = '[REPORTS] Fetch User Sub-Source',
    FETCH_REPORTS_USER_SUB_SOURCE_SUCCESS = '[REPORTS] Fetch User Sub-Source Success',
    UPDATE_USER_SUB_SOURCE_FILTER_PAYLOAD = '[REPORTS] Update User Sub-Source Filter Payload',
    FETCH_REPORTS_USER_SUB_SOURCE_TOTAL_COUNT = '[REPORTS] Fetch User Sub-Source Reports Total Count',
    FETCH_REPORTS_USER_SUB_SOURCE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch User Sub-Source Reports Total Count Success',
    FETCH_REPORTS_USER_SUB_SOURCE_EXPORT = '[REPORTS] Fetch User Sub-Source Export',
    FETCH_REPORTS_USER_SUB_SOURCE_EXPORT_SUCCESS = '[REPORTS] Fetch User Sub-Source Export Success',
    EXPORT_USER_SUB_SOURCE_REPORT = '[REPORTS] Export User Sub Source',
    EXPORT_USER_SUB_SOURCE_REPORT_SUCCESS = '[REPORTS] Export User Sub Source Success',

    FETCH_REPORTS_CUSTOM_PROJECTS = '[REPORTS] Fetch Custom Projects ',
    FETCH_REPORTS_CUSTOM_PROJECTS_SUCCESS = '[REPORTS] Fetch Custom Projects Success',
    FETCH_REPORTS_CUSTOM_PROJECTS_TOTAL_COUNT = '[REPORTS] Fetch Custom Projects Reports Total Count',
    FETCH_REPORTS_CUSTOM_PROJECTS_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch Custom Projects Reports Total Count Success',

    FETCH_REPORTS_USER_MEETING_SITE = '[REPORTS] Fetch User Meeting-Site',
    FETCH_REPORTS_USER_MEETING_SITE_SUCCESS = '[REPORTS] Fetch Meeting-Site Success',
    UPDATE_USER_MEETING_SITE_FILTER_PAYLOAD = '[REPORTS] Update User Meeting-Site Filter Payload',
    FETCH_REPORTS_USER_MEETING_SITE_TOTAL_COUNT = '[REPORTS] Fetch User Meeting-Site Reports Total Count',
    FETCH_REPORTS_USER_MEETING_SITE_TOTAL_COUNT_SUCCESS = '[REPORTS] Fetch User Meeting-Site Reports Total Count Success',
    FETCH_REPORTS_USER_MEETING_SITE_EXPORT = '[REPORTS] Fetch User Meeting-Site Export',
    FETCH_REPORTS_USER_MEETING_SITE_EXPORT_SUCCESS = '[REPORTS] Fetch User Meeting-Site Export Success',
    EXPORT_USER_MEETING_SITE_REPORT = '[REPORTS] Export User Meeting-Site',
    EXPORT_USER_MEETING_SITE_REPORT_SUCCESS = '[REPORTS] Export User Meeting-Site Success',
    FETCH_CITY_REPORTS = '[REPORTS] Fetch City Reports',
    FETCH_CITY_REPORTS_SUCCESS = '[REPORTS] Fetch City Reports Success',
    FETCH_CITY_REPORTS_COUNT = '[REPORTS] Fetch City Reports Count',
    FETCH_CITY_REPORTS_COUNT_SUCCESS = '[REPORTS] Fetch City Reports Count Success',
    UPDATE_CITY_FILTER_PAYLOAD = '[REPORTS] Update City Filter Payload',
    EXPORT_CITY_REPORTS = '[REPORTS] Export City Reports',

    FETCH_REPORT_AUTOMATION = '[REPORTS] Fetch Report Automation',
    FETCH_REPORT_AUTOMATION_SUCCESS = '[REPORTS] Fetch Report Automation Success',
    EXIST_REPORT_AUTOMATION = '[REPORTS] Exists Report Automation',
    EXIST_REPORT_AUTOMATION_SUCCESS = '[REPORTS] Exists Report Automation Success',
    FETCH_EXPORT_REPORT_AUTOMATION_STATUS = '[REPORTS] Fetch Report Automation Export Status List',
    FETCH_EXPORT_REPORT_AUTOMATION_STATUS_SUCCESS = '[REPORTS] Fetch Report Automation Export Status List Success',
    FETCH_REPORT_AUTOMATION_TYPE = '[REPORTS] Fetch Report Automation Type',
    FETCH_REPORT_AUTOMATION_TYPE_SUCCESS = '[REPORTS] Fetch Report Automation Type Success',
    UPDATE_REPORT_AUTOMATION_PAYLOAD = '[REPORTS] Update Report Automation Payload',
    ADD_REPORT_AUTOMATION = '[REPORTS] Add Report Automation',
    ADD_REPORT_AUTOMATION_SUCCESS = '[REPORTS] Add Report Automation Success',
    UPDATE_REPORT_AUTOMATION = '[REPORTS] Update Report Automation',
    UPDATE_REPORT_AUTOMATION_SUCCESS = '[REPORTS] Update Report Automation Success',
    DELETE_REPORT_AUTOMATION = '[REPORTS] Delete Report Automation',
    REPORT_AUTOMATION_EXCEL_UPLOADED_LIST = '[REPORTS] Report Automation Excel Uploaded List',
    REPORT_AUTOMATION_EXCEL_UPLOADED_LIST_SUCCESS = '[REPORTS] Report Automation Excel Uploaded List Success',
    DISABLE_REPORT_AUTOMATION = '[REPORTS] Disable Report Automation',
    DISABLE_REPORT_AUTOMATION_SUCCESS = '[REPORTS] Disable Report Automation Success',
    FETCH_USER_WITH_ROLE = '[REPORTS] Fetch User With Role',
    FETCH_USER_WITH_ROLE_SUCCESS = '[REPORTS] Fetch User With Role Success',
    UPLOAD_REPORT_PDF = '[REPORTS] Upload Report PDF',
    UPLOAD_REPORT_PDF_SUCCESS = '[REPORTS] Upload Report PDF Success',
}
export class FetchReportsUser implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER;
    constructor() { }
}
export class FetchReportsUserSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsUserTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsUserTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class FetchReportsCustomUser implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER;
    constructor() { }
}
export class FetchReportsCustomUserSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsCustomUserTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsCustomUserTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateUserFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_USER_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchUserExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_EXPORT;
    constructor(public payload: any) { }
}
export class FetchUserExportSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportUserStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_STATUS;
    constructor(public payload: any) { }
}
export class ExportUserStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsProject implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_PROJECT;
    constructor() { }
}
export class FetchReportsProjectSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_PROJECT_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsProjectTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_PROJECT_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsProjectTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJECT_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateProjectFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_PROJECT_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchProjectExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_PROJECT_EXPORT;
    constructor(public payload: any) { }
}
export class FetchProjectExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJECT_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportProjectStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_PROJECT_STATUS;
    constructor(public payload: any) { }
}
export class ExportProjectStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_PROJECT_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsSources implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SOURCES;
    constructor() { }
}
export class FetchReportsSourcesSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SOURCES_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsSourceTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SOURCE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsSourceTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class FetchReportsCustomSources implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCES;
    constructor() { }
}
export class FetchReportsCustomSourcesSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCES_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsCustomSourceTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsCustomSourceTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateSourcesFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_SOURCES_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchSourceExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SOURCE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchSourceExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SOURCE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportSourceStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SOURCE_STATUS;
    constructor(public payload: any) { }
}
export class ExportSourceStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SOURCE_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsSubSources implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_SOURCES;
    constructor() { }
}
export class FetchReportsSubSourcesSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_SOURCES_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsSubSourceTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsSubSourceTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchReportsCustomSubSources implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCES;
    constructor() { }
}
export class FetchReportsCustomSubSourcesSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCES_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsCustomSubSourceTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsCustomSubSourceTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateSubSourcesFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_SUB_SOURCES_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchSubSourceExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchSubSourceExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportSubSourceStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SUB_SOURCE_STATUS;
    constructor(public payload: any) { }
}
export class ExportSubSourceStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SUB_SOURCE_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsAgency implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY;
    constructor() { }
}
export class FetchReportsAgencySuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsAgencyCustom implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM;
    constructor() { }
}
export class FetchReportsAgencyCustomSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsAgencyTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsAgencyCustomTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsAgencyTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_AGENCY_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class FetchReportsAgencyCustomTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateAgencyFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_AGENCY_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchAgencyExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_AGENCY_EXPORT;
    constructor(public payload: any) { }
}
export class FetchAgencyExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_AGENCY_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportAgencyStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_AGENCY_STATUS;
    constructor(public payload: any) { }
}
export class ExportAgencyStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_AGENCY_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsMeetingSiteVisitLevel1 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1;
    constructor() { }
}

export class CleanMeetingVisitReportList implements Action {
    readonly type: string =
        ReportsActionTypes.CLEAN_MEETING_VISIT_REPORT_LIST;
    constructor() { }
}

export class FetchReportsMeetingSiteVisitLevel1Success implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1_SUCCESS;
    constructor(public response: any = []) { }
}

export class FetchReportsMeetingSiteVisitLevel2 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2;
    constructor() { }
}
export class FetchReportsMeetingSiteVisitLevel2Success implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2_SUCCESS;
    constructor(public response: any = []) { }
}
export class FetchReportsMeetingSiteVisitTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsMeetingSiteVisitTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateMeetingSiteVisitFilterPayload implements Action {
    readonly type: string =
        ReportsActionTypes.UPDATE_MEETING_SITE_VISIT_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchMeetingSiteVisitExport implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_EXPORT;
    constructor(public payload: any) { }
}
export class FetchMeetingSiteVisitExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportMeetingSiteVisitStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_MEETING_SITE_VISIT_STATUS;
    constructor(public payload: any) { }
}
export class ExportMeetingSiteVisitStatusSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.EXPORT_MEETING_SITE_VISIT_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsActivity1 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY1;
    constructor() { }
}
export class FetchReportsActivity1Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY1_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity2 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY2;
    constructor() { }
}
export class FetchReportsActivity2Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY2_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity3 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY3;
    constructor() { }
}
export class FetchReportsActivity3Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY3_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class FetchReportsActivity4 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY4;
    constructor(public section: String = '') { }
}
export class FetchReportsActivity4Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY4_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class FetchReportsActivity5 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY5;
    constructor() { }
}
export class FetchReportsActivity5Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY5_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity6 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY6;
    constructor() { }
}
export class FetchReportsActivity6Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY6_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity7 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY7;
    constructor() { }
}
export class FetchReportsActivity7Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY7_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}

export class FetchReportsActivity9 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY9;
    constructor() { }
}
export class FetchReportsActivity9Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY9_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity10 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY10;
    constructor() { }
}
export class FetchReportsActivity10Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY10_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity11 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY11;
    constructor() { }
}
export class FetchReportsActivity11Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY11_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivity12 implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY12;
    constructor() { }
}
export class FetchReportsActivity12Success implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY12_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsActivityTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_ACTIVITY_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsActivityTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_ACTIVITY_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateActivityFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_ACTIVITY_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class UpdateAllActivityFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_ALL_ACTIVITY_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchActivityExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_ACTIVITY_EXPORT;
    constructor(public payload: any) { }
}
export class FetchActivityExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_ACTIVITY_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportActivity implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_ACTIVITY;
    constructor(public payload: any) { }
}
export class ExportActivitySuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_ACTIVITY_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsSubStatus implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_STATUS;
    constructor() { }
}
export class FetchReportsSubStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsSubStatusTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsSubStatusTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateSubStatusFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_SUB_STATUS_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchSubStatusExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_EXPORT;
    constructor(public payload: any) { }
}
export class FetchSubStatusExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportSubStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SUB_STATUS;
    constructor(public payload: any) { }
}
export class ExportSubStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SUB_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchSubReport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_SUB_REPORT;
    constructor() { }
}
export class FetchSubReportSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_SUB_REPORT_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsSubTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_SUB_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsSubTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_SUB_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateSubReportFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_SUB_REPORT_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchSubReportExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_SUB_REPORT_EXPORT;
    constructor(public payload: any) { }
}
export class FetchSubReportExportSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_SUB_REPORT_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportSubReport implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SUB_REPORT;
    constructor(public payload: any) { }
}
export class ExportSubReportSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_SUB_REPORT_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsProjectSubstatus implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS;
    constructor() { }
}
export class FetchReportsProjectSubstatusSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsProjSubTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJ_SUB_STATUS_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsProjSubTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJ_SUB_STATUS_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateProjectSubstatusFilterPayload implements Action {
    readonly type: string =
        ReportsActionTypes.UPDATE_PROJECT_SUB_STATUS_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchProjectSubstatusExport implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS_EXPORT;
    constructor(public payload: any) { }
}
export class FetchProjectSubstatusExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportProjectSubstatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_PROJECT_SUB_STATUS;
    constructor(public payload: any) { }
}
export class ExportProjectSubstatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_PROJECT_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsCall implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CALL;
    constructor() { }
}
export class FetchReportsCallSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CALL_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsCallTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CALL_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsCallTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CALL_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateCallFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_CALL_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchCallExport implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CALL_EXPORT;
    constructor(public payload: any) { }
}
export class FetchCallExportSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CALL_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportCallStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_CALL_STATUS;
    constructor(public payload: any) { }
}
export class ExportCallStatusSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_CALL_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportExportTracker implements Action {
    readonly type: string = ReportsActionTypes.FETCH_EXPORT_TRACKER;
    constructor(public payload: any, public pageNumber: number, public pageSize: number) { }
}
export class FetchReportExportTrackerSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_EXPORT_TRACKER_SUCCESS;
    constructor(public response: any[] = []) { }
}
export class FetchReportsReceivedDate implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE;
    constructor() { }
}
export class FetchReportsReceivedDateSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsReceivedDateTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsReceivedDateTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateReceivedDateFilterPayload implements Action {
    readonly type: string =
        ReportsActionTypes.UPDATE_RECEIVED_DATE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchReceivedDateExport implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchReceivedDateExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportReceivedDateStatus implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_RECEIVED_DATE_STATUS;
    constructor(public payload: any) { }
}
export class ExportReceivedDateStatusSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.EXPORT_RECEIVED_DATE_STATUS_SUCCESS;
    constructor(public resp: string = '') { }
}
export class FetchReportFlagCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_FLAG_COUNT;
    constructor(public payload: any) { }
}
export class FetchReportFlagCountSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_FLAG_COUNT_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class FetchReportsUserSource implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_SOURCE;
    constructor() { }
}
export class FetchReportsUserSourceSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsUserSourceTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsUserSourceTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateUserSourceFilterPayload implements Action {
    readonly type: string =
        ReportsActionTypes.UPDATE_USER_SOURCE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchUserSourceExport implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchUserSourceExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}
export class ExportUserSourceReport implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_SOURCE_REPORT;
    constructor(public payload: any) { }
}
export class ExportUserSourceReportSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_SOURCE_REPORT_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsUserSubSource implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE;
    constructor() { }
}
export class FetchReportsUserSubSourceSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsUserSubSourceTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsUserSubSourceTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateUserSubSourceFilterPayload implements Action {
    readonly type: string =
        ReportsActionTypes.UPDATE_USER_SUB_SOURCE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchUserSubSourceExport implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchUserSubSourceExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class ExportUserSubSourceReport implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_SUB_SOURCE_REPORT;
    constructor(public payload: any) { }
}
export class ExportUserSubSourceReportSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_SUB_SOURCE_REPORT_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchReportsCustomProjects implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS;
    constructor() { }
}
export class FetchReportsCustomProjectsSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsCustomProjectsTotalCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsCustomProjectsTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}

export class FetchReportsUserMeetingSite implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE;
    constructor() { }
}
export class FetchReportsUserMeetingSiteSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_SUCCESS;
    constructor(public response: FetchResponse = {}) { }
}
export class FetchReportsUserMeetingSiteTotalCount implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_TOTAL_COUNT;
    constructor() { }
}
export class FetchReportsUserMeetingSiteTotalCountSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_TOTAL_COUNT_SUCCESS;
    constructor(public response: number = 0) { }
}
export class UpdateUserMeetingSiteFilterPayload implements Action {
    readonly type: string =
        ReportsActionTypes.UPDATE_USER_MEETING_SITE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}
export class FetchUserMeetingSiteExport implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_EXPORT;
    constructor(public payload: any) { }
}
export class FetchUserMeetingSiteExportSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class ExportUserMeetingSiteReport implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_MEETING_SITE_REPORT;
    constructor(public payload: any) { }
}
export class ExportUserMeetingSiteReportSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_USER_MEETING_SITE_REPORT_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchCityReports implements Action {
    readonly type: string = ReportsActionTypes.FETCH_CITY_REPORTS;
    constructor() { }
}

export class FetchCityReportsSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_CITY_REPORTS_SUCCESS;
    constructor(public resp: any = {}) { }
}
export class FetchCityReportsCount implements Action {
    readonly type: string = ReportsActionTypes.FETCH_CITY_REPORTS_COUNT;
    constructor() { }
}

export class FetchCityReportsCountSuccess implements Action {
    readonly type: string = ReportsActionTypes.FETCH_CITY_REPORTS_COUNT_SUCCESS;
    constructor(public resp: any) { }
}

export class UpdateCityFilterPayload implements Action {
    readonly type: string = ReportsActionTypes.UPDATE_CITY_FILTER_PAYLOAD;
    constructor(public payload: any) { }

}

export class ExportCityReport implements Action {
    readonly type: string = ReportsActionTypes.EXPORT_CITY_REPORTS;
    constructor(public payload: any) { }
}

// ------------------------------------- REPORT AUTOMATION ----------------------------
export class FetchReportAutomationList implements Action {
    readonly type = ReportsActionTypes.FETCH_REPORT_AUTOMATION;
    constructor(public payload: any) { }
}
export class FetchReportAutomationListSuccess implements Action {
    readonly type = ReportsActionTypes.FETCH_REPORT_AUTOMATION_SUCCESS;
    constructor(public response: any) { }
}

export class ExistReportAutomation implements Action {
    readonly type: string = ReportsActionTypes.EXIST_REPORT_AUTOMATION;
    constructor(public reportName: string) { }
}
export class ExistReportAutomationSuccess implements Action {
    readonly type: string = ReportsActionTypes.EXIST_REPORT_AUTOMATION_SUCCESS;
    constructor(public response: boolean) { }
}

export class FetchExportReportAutomationStatus implements Action {
    readonly type: string = ReportsActionTypes.FETCH_EXPORT_REPORT_AUTOMATION_STATUS;
    constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchExportReportAutomationStatusSuccess implements Action {
    readonly type: string =
        ReportsActionTypes.FETCH_EXPORT_REPORT_AUTOMATION_STATUS_SUCCESS;
    constructor(public response: any[] = []) { }
}
export class FetchReportAutomationTypeList implements Action {
    readonly type = ReportsActionTypes.FETCH_REPORT_AUTOMATION_TYPE;
    constructor() { }
}
export class FetchReportAutomationTypeListSuccess implements Action {
    readonly type = ReportsActionTypes.FETCH_REPORT_AUTOMATION_TYPE_SUCCESS;
    constructor(public response: any) { }
}
export class UpdateReportAutomationsFiltersPayload implements Action {
    readonly type = ReportsActionTypes.UPDATE_REPORT_AUTOMATION_PAYLOAD;
    constructor(public payload: any) { }
}
export class AddReportAutomation implements Action {
    readonly type = ReportsActionTypes.ADD_REPORT_AUTOMATION;
    constructor(public payload: any) { }
}
export class AddReportAutomationSuccess implements Action {
    readonly type = ReportsActionTypes.ADD_REPORT_AUTOMATION_SUCCESS;
    constructor(public response: any) { }
}
export class UpdateReportAutomation implements Action {
    readonly type = ReportsActionTypes.UPDATE_REPORT_AUTOMATION;
    constructor(public payload: any) { }
}
export class UpdateReportAutomationSuccess implements Action {
    readonly type = ReportsActionTypes.UPDATE_REPORT_AUTOMATION_SUCCESS;
    constructor(public response: any) { }
}
export class DeleteReportAutomation implements Action {
    readonly type = ReportsActionTypes.DELETE_REPORT_AUTOMATION;
    constructor(public id: any) { }
}

export class ReportAutomationExcelUploadedList implements Action {
    readonly type: string = ReportsActionTypes.REPORT_AUTOMATION_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}
export class ReportAutomationExcelUploadedListSuccess implements Action {
    readonly type: string = ReportsActionTypes.REPORT_AUTOMATION_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class DisableReportAutomation implements Action {
    readonly type = ReportsActionTypes.DISABLE_REPORT_AUTOMATION;
    constructor(public payload: any) { }
}
export class DisableReportAutomationSuccess implements Action {
    readonly type = ReportsActionTypes.DISABLE_REPORT_AUTOMATION_SUCCESS;
    constructor(public response: any) { }
}

export class FetchUserWithRole implements Action {
    readonly type = ReportsActionTypes.FETCH_USER_WITH_ROLE;
    constructor() { }
}
export class FetchUserWithRoleSuccess implements Action {
    readonly type = ReportsActionTypes.FETCH_USER_WITH_ROLE_SUCCESS;
    constructor(public response: any) { }
}

export class UploadReportPdf implements Action {
    readonly type = ReportsActionTypes.UPLOAD_REPORT_PDF;
    constructor(public payload: { file: Blob, fileName: string, reportType: string }) {}
}
export class UploadReportPdfSuccess implements Action {
    readonly type = ReportsActionTypes.UPLOAD_REPORT_PDF_SUCCESS;
    constructor(public s3Url: string) {}
}
