import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class CustomFormService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'customform';
  }

  addCustomForm(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}`, payload);
  }

  getCustomForm() {
    return this.http.get(`${this.serviceBaseUrl}`);
  }

  deleteCustomForm(id: any) {
    return this.http.delete(`${this.serviceBaseUrl}/${id}`);
  }

  getCustomFieldExist(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/field/exist`, payload);
  }

   getCustomFormField() {
    return this.http.get(`${this.serviceBaseUrl}/customfields`);
  }
}