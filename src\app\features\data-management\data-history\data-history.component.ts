import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  convertUrlsToLinks,
  getBedsDisplay,
  getBHKDisplay,
  getBRDisplay,
  getTimeZoneDate,
  groupBy,
  istFormat,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'data-history',
  templateUrl: './data-history.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DataHistoryComponent implements OnInit, OnChanges {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() history: any;
  @Input() formDirty: boolean = false;
  prospectHistoryData: any;
  filteredHistoryList: any[];
  noDocument: AnimationOptions = { path: 'assets/animations/no-document.json' };
  istFormat = istFormat;
  getBHKDisplay = getBHKDisplay;
  getBRDisplay = getBRDisplay;
  getTimeZoneDate = getTimeZoneDate;
  getBedsDisplay = getBedsDisplay;
  userData: any;
  convertUrlsToLinks = convertUrlsToLinks;
  globalSettingsDetails: any;
  isLoading: boolean = true;
  loader: AnimationOptions = { path: 'assets/animations/circle-loader.json' };

  constructor(
    private _store: Store<AppState>,
    private sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.cdr.markForCheck();
      });
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        this.cdr.markForCheck();
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.formDirty) {
      this.formDirty = changes?.formDirty?.currentValue;
    }
    if (changes?.history) {
      this.prospectHistoryData = Object.entries(this.history).map(
        (item: any) => {
          return {
            date: item[0],
            data: [Object.entries(groupBy(item[1], 'modifiedOn'))],
          };
        }
      );

      this.filteredHistoryList = this.prospectHistoryData;
    }
  }

  isValidDate(value: string): boolean {
    const date = new Date(value);
    return !isNaN(date.getTime());
  }

  jsonFormat(data: any) {
    return Object?.entries(JSON.parse(data));
  }

  retrieveMessageFromBackend(msg: any) {
    return this.sanitizer.bypassSecurityTrustHtml(msg.replace(/\n/g, '<br>'));
  }

  DataCallFormate(data: string) {
    if (!data) return null;
    if (data.includes('->')) {
      const callParts = data.split('->').map(part => part.trim());
      if (callParts.length >= 3) {
        const result: any = {
          callType: callParts[0],
          status: callParts[1],
          duration: callParts[2].replace(', CallRecordingUrl', '').trim()
        };
        if (callParts[3]) {
          result.url = callParts[3];
        }
        return result;
      }
    }
  }

  decodeAudioUrl(url: string): string | null {
    try {
      return decodeURIComponent(url);
    } catch {
      return url;
    }
  }

  pauseOtherAudio(audioPlayer: any) {
    let audioElements = document.getElementsByTagName('audio');
    for (let i = 0; i < audioElements.length; i++) {
      if (audioElements[i] !== audioPlayer) {
        audioElements[i].pause();
      }
    }
  }
}
