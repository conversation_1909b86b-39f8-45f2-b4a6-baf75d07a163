<div class="flex-between">
  <div>
    <h4 class="fw-600">{{ 'AUTH.login' | translate }}</h4>
    <div class="mt-4">{{ 'AUTH.username-password' | translate }}</div>
  </div>
  <ng-lottie [options]='frontLaptop' width="110px"></ng-lottie>
</div>
<div class="field-label">{{ 'AUTH.subdomain' | translate }} {{ 'PROFILE.name' | translate }}</div>
<div class="border-gray bg-light-slate br-5 w-100 align-center text-coal">
  <div class="w-70 p-12 ph-w-60">{{subDomain}}</div>
  <div class="border-right h-40"></div>
  <div class="w-30 p-12 ph-w-40">{{getEnvDetails()}}</div>
</div>
<form [formGroup]="loginForm">
  <div class="field-label-req">{{ 'AUTH.user-name' | translate }}</div>
  <form-errors-wrapper label="Username" [control]="loginForm.controls['username']">
    <input type="text" id="inpLoginName" data-automate-id="inpLoginName" formControlName="username"
      placeholder="Enter your username" required (keyup.enter)="focusableLogin.click()">
  </form-errors-wrapper>
  <div class="field-label-req">{{ 'AUTH.password' | translate }}</div>
  <form-errors-wrapper label="Password" [control]="loginForm.controls['password']">
    <input [type]="isShowPassword ? 'text' : 'password'" id="inpLoginPassword" data-automate-id="inpLoginPassword"
      formControlName="password" placeholder="Enter your password" required (keyup.enter)="focusableLogin.click()">
    <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
      [ngClass]="isShowPassword ? 'ic-eye-slash' : 'ic-eye-solid'" id="passwordhide" data-automate-id="passwordhide"
      (click)="isShowPassword = !isShowPassword"></a>
  </form-errors-wrapper>
</form>
<h4 #focusableLogin
  [ngClass]="isAccountLocked || isPasswordResetRequired ? 'btn-red-750 btn-accent-green-xl mt-20 pe-none' : 'btn-accent-green-xl mt-20'"
  (click)="!isLoginLoading && !isAccountLocked && !isPasswordResetRequired ? login(): '';trackingService.trackFeature('Web.Login.Button.Login.Click')">
  <span *ngIf="!isLoginLoading && !isAccountLocked && !isPasswordResetRequired else buttonContent">{{ 'AUTH.login' |
    translate }}</span>
</h4>
<div class="justify-end">
  <h5 class="fw-semi-bold mt-10 cursor-pointer"
    (click)="navigateToForgotPassword();trackingService.trackFeature('Web.Login.Button.ForgotPassword.Click')">{{
    'AUTH.forgot-password' |
    translate }}?</h5>
</div>
<ng-template #buttonContent>
  <div *ngIf="isLoginLoading; else lockoutContent" class="container flex-center py-8">
    <ng-container *ngFor="let dot of [1,2,3]">
      <div class="dot-falling dot-white"></div>
    </ng-container>
  </div>
  <ng-template #lockoutContent>
    <span *ngIf="isPasswordResetRequired; else timerContent">
      Click forgot password to reset
    </span>
    <ng-template #timerContent>
      <span *ngIf="isAccountLocked">
        Try to login again in {{ lockoutMinutes }}:{{ lockoutSeconds.toString().padStart(2, '0') }} min
      </span>
    </ng-template>
  </ng-template>
</ng-template>