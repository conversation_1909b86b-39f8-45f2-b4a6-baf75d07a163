import { Component, EventEmitter, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'alert',
  templateUrl: './alert.component.html',
})
export class AlertComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  userData: any;
  currentDate: any;
  getTimeZoneDate = getTimeZoneDate
  constructor(private store: Store<AppState>,
    public modalRef: BsModalRef,
    private router: Router) {
  }

  ngOnInit(): void {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });
  }

  contactNow(){
    location.href = 'tel:' + '91 8971883827';
  }

}
