import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CellClickedEvent } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, firstValueFrom, skipWhile, take, takeUntil } from 'rxjs';

import { NotificationsService } from 'angular2-notifications';
import {
  MANAGE_USER_FILTERS_KEY_LABEL,
  PAGE_SIZE,
  PROP_DATE_TYPE,
  SHOW_ENTRIES,
  USER_VISIBILITY_IMAGE,
} from 'src/app/app.constants';
import { PropertyDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { UserFilter } from 'src/app/core/interfaces/property.interface';
import {
  assignToSort,
  changeCalendar,
  getAppName,
  getAssignedToDetails,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  onFilterChanged,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { ManageUserActionsComponent } from 'src/app/features/teams/manage-user/manage-user-actions/manage-user-actions.component';
import { UserAssignmentComponent } from 'src/app/features/teams/manage-user/user-assignment/user-assignment.component';
import { FetchAttendanceSetting } from 'src/app/reducers/attendance/attendance.actions';
import { getAttendanceSettings } from 'src/app/reducers/attendance/attendance.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchSubscription,
  FetchTimeZoneInfo,
} from 'src/app/reducers/profile/profile.actions';
import {
  getSubscription,
  getTimeZoneInfo,
} from 'src/app/reducers/profile/profile.reducers';
import {
  BulkToggleUserStatus,
  BulkUpdatePermissions,
  FetchDeletedUserList,
  FetchDepartmentsList,
  FetchDesignationsList,
  FetchGeneralManagerList,
  FetchManageUserRolesList,
  FetchUserExcelUploadedList,
  FetchUserExportTracker,
  FetchUsersList,
  FetchUsersListForReassignment,
  UpdateBulkUsers,
  UpdateFilterPayload,
} from 'src/app/reducers/teams/teams.actions';
import {
  getDepartmentsList,
  getDepartmentsListIsLoading,
  getDesignationsList,
  getDesignationsListIsLoading,
  getFiltersPayload,
  getGeneralManagerList,
  getGeneralManagerListIsLoading,
  getRolesList,
  getUpdateBulkUsersIsLoading,
  getUserBasicDetails,
  getUsersCount,
  getUsersCountIsLoading,
  getUsersList,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
  getUsersListIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { TeamsService } from 'src/app/services/controllers/teams.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { DeletedUsersTrackerComponent } from 'src/app/shared/components/deleted-users-tracker/deleted-users-tracker.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { ExportUsersTrackerComponent } from 'src/app/shared/components/export-users-tracker/export-users-tracker.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment } from 'src/environments/environment';
import { ExcelUploadedStatusComponent } from '../../leads/excel-uploaded-status/excel-uploaded-status.component';
import { GeoFencingComponent } from './geo-fencing/geo-fencing.component';

@Component({
  selector: 'manage-user',
  templateUrl: './manage-user.component.html',
})
export class ManageUserComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy, DoCheck {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  gridOptions: any;
  rowData: any = [];
  columnDropDown: { field: string; hide: boolean }[] = [];
  pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  dateTypeList: Array<string> = PROP_DATE_TYPE.slice(0, 3);
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: UserFilter;
  appliedFilter: any;
  usersTotalCount: number;
  usersItemCount: number;
  getPages = getPages;
  getAppName = getAppName;
  private filtered_list: Array<any> = [];
  selectedPageSize: number;
  canView: boolean = false;
  canAdd: boolean = false;
  canEdit: boolean = false;
  canDelete: boolean = false;
  canExport: boolean = false;
  canSearch: boolean = false;
  canViewRoles: boolean = false;
  selectedNodes: any = {};
  selectedUsers: any[] = [];
  allUsers: any[];
  reportsTo: any[];
  generalManager: any[];
  usersData: any;
  userBulkUpdateForm: FormGroup;
  designationsList: any[];
  departmentList: any[];
  visibilityList: Array<Object> = USER_VISIBILITY_IMAGE.slice(0, 3);
  baseFilterCount: Object;
  changeReportsTo: boolean = false;
  changeGM: boolean = false;
  usersListIsLoading: boolean = true;
  isUsersListForReassignmentLoading: boolean = true;
  isDesignationsListLoading: boolean = true;
  isDepartmentsListLoading: boolean = true;
  isGeneralManagerLoading: boolean = true;
  isUpdateBulkUsersLoading: boolean = false;
  isUsersCountLoading: boolean = true;

  sameAll: boolean;
  selectedUserIds: any;
  selectedToggleUsers: any;
  showLeftNav: boolean = true;
  remainingUsers: number;
  subscription: any;
  showFilters: boolean = false;
  usersFiltersKeyLabel = MANAGE_USER_FILTERS_KEY_LABEL;
  rolesList: any;
  noRoleSelected: boolean = false;
  selectedRoles: any[] = [];
  selectedUserInfo: any;
  selectedUserId: any;
  columns: any[];
  defaultColumns: any[];
  selectedTrackerOption: string;
  selectedOption: string;

  onFilterChanged = onFilterChanged;
  userData: any;
  currentDate: Date = new Date();
  allUsersList: any;
  toDate: any = new Date();
  fromDate: any = new Date();
  timeZoneList: any;
  activeUsers: any[];
  onPickerOpened = onPickerOpened;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  canBulkUpdate: boolean = false
  canBulkRoleUpdate: boolean = false
  canBulkDeactive: boolean = false
  canMakeDefaultPassword: boolean = false
  isGeoFenceEnabled: boolean;
  canGeoFence: boolean = false;
  constructor(
    public router: Router,
    private headerTitle: HeaderTitleService,
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    public metaTitle: Title,
    private bulkUpdateModalRef: BsModalRef,
    private bulkDeactiveModalRef: BsModalRef,
    public modalService: BsModalService,
    private formBuilder: FormBuilder,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef,
    private sanitizer: DomSanitizer,
    public trackingService: TrackingService,
    private teamsService: TeamsService,
    private _notificationService: NotificationsService,
  ) {
    super();
    this.metaTitle.setTitle('CRM | Users');
    this._store.dispatch(new FetchManageUserRolesList());
  }

  async ngOnInit() {
    const permission = await firstValueFrom(
      this._store.select(getPermissions).pipe(
        takeUntil(this.stopper),
        skipWhile((data) => !data?.length)
      )
    );
    if (!permission?.includes('Permissions.Users.View')) {
      const userDetails = localStorage.getItem('userDetails');
      const userId = JSON.parse(userDetails)?.sub;
      this.router.navigateByUrl(`/teams/user-details/${userId}`);
      return
    }

    this._store
      .select(getRolesList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data && data.items) {
          this.rolesList = data.items
            .slice()
            .sort((a: any, b: any) => a.name.localeCompare(b.name));
        }
      });

    this._store
      .select(getUsersList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this._store.dispatch(new FetchTimeZoneInfo());
    this._store
      .select(getTimeZoneInfo)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.timeZoneList = data;
      });

    this._store
      .select(getUsersListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.usersListIsLoading = isLoading;
      });

    this.headerTitle.setLangTitle('SIDEBAR.manage-users');
    this._store.select(getUsersCount).subscribe((count: any) => {
      this.usersTotalCount = count.totalCount;
      this.usersItemCount = count.itemsCount;
      this.usersData = count.usersData;
    });
    this._store
      .select(getUsersCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isUsersCountLoading = isLoading;
      });

    this._store.dispatch(new FetchAttendanceSetting());
    this._store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
        this.initializeGridSettings();
      })

    this._store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.pageSize = this.filtersPayload?.pageSize;
        this.selectedPageSize = this.filtersPayload?.pageSize;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.filterManageUser();
        this.searchTerm = this.filtersPayload?.userSearch;
      });
    this._store.dispatch(new FetchUsersListForReassignment());
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsersList = data;
        this.allUsers = this.allUsersList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = this.allUsers?.filter((user: any) => user?.isActive);
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.allUsers = assignToSort(this.allUsers, '');
        this.reportsTo = assignToSort(this.allUsers, '');
      });
    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isUsersListForReassignmentLoading = isLoading;
      });
    this._store.dispatch(new FetchDesignationsList());
    this._store
      .select(getDesignationsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.designationsList = data;
      });
    this._store
      .select(getDesignationsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isDesignationsListLoading = isLoading;
      });
    this._store.dispatch(new FetchGeneralManagerList());
    this._store
      .select(getGeneralManagerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.generalManager = data;
        this.generalManager = this.generalManager?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.generalManager = assignToSort(this.generalManager, '');
      });
    this._store
      .select(getGeneralManagerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGeneralManagerLoading = isLoading;
      });
    this._store.dispatch(new FetchDepartmentsList());
    this._store
      .select(getDepartmentsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.departmentList = data;
      });
    this._store
      .select(getDepartmentsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isDepartmentsListLoading = isLoading;
      });
    this._store.dispatch(new FetchSubscription(this.userData?.timeZoneInfo));
    this._store
      .select(getSubscription)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subscription = data;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canSearch = permissionsSet.has('Permissions.Users.Search');
        this.canExport = permissionsSet.has('Permissions.Users.Export');
        this.canView = permissionsSet.has('Permissions.Users.View');
        this.canAdd = permissionsSet.has('Permissions.Users.Create');
        this.canEdit = permissionsSet.has('Permissions.Users.Update');
        this.canDelete = permissionsSet.has('Permissions.Users.Delete');
        this.canViewRoles = permissionsSet.has('Permissions.Roles.View');
        this.canBulkUpdate = permissionsSet.has('Permissions.Users.BulkUpdate');
        this.canBulkDeactive = permissionsSet.has('Permissions.Users.BulkDeactive');
        this.canBulkRoleUpdate = permissionsSet.has('Permissions.Users.BulkRoleUpdate');
        this.canMakeDefaultPassword = permissionsSet.has('Permissions.Users.DefaultPassword');
        this.canGeoFence = permissionsSet.has('Permissions.Users.SetGeoFence');
        this.initializeGridSettings();
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });
    this.filterFunction();
    this.trackingService.trackFeature(`Web.TeamUser.Page.TeamUser.Visit`)
  }

  ngDoCheck() {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
      this.selectedToggleUsers = this.selectedNodes?.map(
        (node: any) => node?.data?.isActive
      );
      this.sameAll = this.selectedToggleUsers?.every(
        (v: any) => v === this.selectedToggleUsers[0]
      );
    }
  }

  filterManageUser() {
    this.appliedFilter = {
      ...this.appliedFilter,
      department: this.filtersPayload?.Departments,
      designation: this.filtersPayload?.Designations,
      reportsTo: this.filtersPayload?.ReportsToIds,
      generalManager: this.filtersPayload?.generalManagerIds,
      users: this.filtersPayload?.UserIds,
      pageNumber: this.filtersPayload?.pageNumber,
      pageSize: this.filtersPayload?.pageSize,
      userSearch: this.filtersPayload?.userSearch,
      userStatus: this.filtersPayload?.userStatus !== undefined ? this.filtersPayload?.userStatus : true,
      ShouldShowReportees: this.filtersPayload?.ShouldShowReportees,
      date: [
        patchTimeZoneDate(
          this.filtersPayload?.FromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        patchTimeZoneDate(
          this.filtersPayload?.ToDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
      ],
      DateType: PropertyDateType[this.filtersPayload?.DateType],
      timezone: this.filtersPayload?.TimezoneIds,
    };
    if (
      this.appliedFilter?.department?.length ||
      this.appliedFilter?.designation?.length ||
      this.appliedFilter?.reportsTo?.length ||
      this.appliedFilter?.generalManager?.length ||
      this.appliedFilter?.users?.length ||
      this.appliedFilter?.DateType ||
      this.appliedFilter?.date?.[0] ||
      this.appliedFilter?.timezone?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      [
        'pageSize',
        'pageNumber',
        'ShouldShowReportees',
        'userSearch',
        'userStatus',
      ].includes(key) ||
      values?.length === 0 ||
      (key == 'maxPrice' && this.appliedFilter?.maxPrice == 0)
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (key === 'designation' || key === 'department') return values;
    return values?.toString()?.split(',');
  }

  currentVisibility(visibility: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    this.filterFunction();
    this.trackingService.trackFeature(`Web.TeamUser.Menu.${visibility === null ? 'All' : visibility === true ? 'Active' : 'InActive'}.Click`)
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Full Name',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data.firstName,
          params.data.lastName,
          params.data.userName,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all fw-600">${params.value[0]} ${params.value[1]}</p>
          <p class="text-truncate-1 break-all text-sm">${params.value[2]}</p>`;
        },
      },
      {
        headerName: 'Designation',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [params.data.designation?.name],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Department',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [params.data.department?.name],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'General Manager',
        cellClass: 'cursor-pointer',
        hide: true,
        minWidth: 125,
        valueGetter: (params: any) => [params.data.generalManager?.name],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Broker Number',
        cellClass: 'cursor-pointer',
        hide: true,
        minWidth: 125,
        valueGetter: (params: any) => [params.data?.licenseNo ?? '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Reporting To',
        cellClass: 'cursor-pointer',
        hide: true,
        minWidth: 125,
        valueGetter: (params: any) => [params.data.reportsTo?.name],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Time Zone Details',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data?.timeZoneInfo],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 text-sm">Zone: ${params.value[0]?.timeZoneDisplay || '--'
            }</p>
      <p class="text-truncate-1 text-sm">Zone Name: ${params.value[0]?.timeZoneName || '--'
            }</p>`;
        },
      },
      {
        headerName: 'Roles',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data.userRoles
            ?.filter((role: any) => role.name !== 'Default')
            .map((role: any) => role.name)
            .join(', '),
        ],
        cellRenderer: (params: any) => {
          const truncatedText = `<p class="text-truncate-1">${params.value}</p>`;
          return `<div title="(${params?.value?.[0]?.split(',')?.length}) :  ${params.value
            }">${truncatedText}</div>`;
        },
      },
      {
        headerName: 'Created',
        cellClass: 'cursor-pointer',
        hide: true,
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.createdBy,
            this.allUsersList,
            true
          ) || '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap text-truncate-1 break-all">${params.value[0]
            }</p>
            <p class="text-nowrap text-truncate-1 break-all">${params.value[1]
            }</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Modified',
        cellClass: 'cursor-pointer',
        hide: true,
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.allUsersList,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap text-truncate-1 break-all">${params.value[0]
            }</p>
            <p class="text-nowrap text-truncate-1 break-all">${params.value[1]
            }</p>
                        <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Assigned Leads',
        minWidth: 90,
        maxWidth: 125,
        filter: false,
        hide: true,
        valueGetter: (params: any) => ['Assigned Leads', params.data.userId],
        cellRenderer: ManageUserActionsComponent,
      },
      {
        headerName: 'Lead Assignment',
        minWidth: 90,
        maxWidth: 130,
        filter: false,
        valueGetter: (params: any) => [params.data.isAutomationEnabled],
        cellRenderer: UserAssignmentComponent,
      },
    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    if (this.canBulkDeactive || this.canBulkRoleUpdate || this.canBulkUpdate) {
      this.gridOptions.columnDefs.unshift({
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        resizable: false,
        maxWidth: 50,
      });
    }
    if (this.isGeoFenceEnabled && this.canGeoFence) {
      this.gridOptions.columnDefs.push({
        headerName: 'Enable Geo Fence',
        minWidth: 90,
        maxWidth: 130,
        filter: false,
        valueGetter: (params: any) => [params.data?.isGeoFencingEnabled],
        cellRenderer: GeoFencingComponent,
      });
    }
    if (this.canEdit || this.canDelete) {
      this.gridOptions.columnDefs.push({
        headerName: 'Actions',
        maxWidth: 160,
        filter: false,
        valueGetter: (params: any) => ['Actions'],
        cellRenderer: ManageUserActionsComponent,
      });
    }

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.columns = this.gridColumnApi.getColumns();
    this.columns = this.columns.map((column: any) => {
      return {
        label: column.getColDef().headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(3, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myUserColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('manage-user-columns')?.split(',');
    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnMoved(params: any) {
    var columnState = JSON.stringify(params.columnApi.getColumnState());
    localStorage.setItem('myUserColumnState', columnState);
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
    this.trackingService.trackFeature(`Web.TeamUser.Button.Default.Click`)
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-user-columns', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem('myUserColumnState', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchUsersList());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.trackingService.trackFeature(`Web.TeamUser.Option.${this.pageSize}.Click`)
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchUsersList());
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    if (this.filtered_list.length > 0) {
      this.gridApi.applyTransaction({ add: this.filtered_list });
    } else {
      this.gridApi.applyTransaction({ add: this.rowData });
    }
    this.currOffset = 0;
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this.trackingService.trackFeature(`Web.TeamUser.Button.AdvancedFilter.Click`)
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  onClearAllFilters() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.selectedPageSize,
      department: [],
      designation: [],
      reportsTo: [],
      generalManager: [],
      users: [],
      withTeam: false,
      timezone: [],
      userStatus: true,
    };
    this.filterFunction();
    this.gridOptionsService.data = [];
  }

  applyAdvancedFilter() {
    this.appliedFilter = { ...this.appliedFilter, pageNumber: 1 };
    this.filterFunction();
    this.modalService.hide();
  }

  filterFunction() {
    this.filtersPayload = {
      ...this.filtersPayload,
      path: 'user/getallusers',
      pageNumber: this.appliedFilter?.pageNumber,
      Departments: this.appliedFilter.department,
      ReportsToIds: this.appliedFilter.reportsTo,
      generalManagerIds: this.appliedFilter?.generalManager,
      UserIds: this.appliedFilter.users,
      Designations: this.appliedFilter.designation,
      pageSize: this.pageSize,
      userSearch: this.searchTerm,
      userStatus: this.appliedFilter.userStatus,
      ShouldShowReportees: this.appliedFilter.ShouldShowReportees,
      DateType: PropertyDateType[this.appliedFilter?.DateType],
      FromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      TimezoneIds: this.appliedFilter?.timezone,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchUsersList());
  }

  onCellClicked(event: CellClickedEvent) {
    const headerName = event.colDef.headerName;
    if (
      headerName === 'Full Name' ||
      headerName === 'Designation' ||
      headerName === 'Department' ||
      headerName === 'Reporting To' ||
      headerName === 'Roles'
    ) {
      this.router.navigate(['teams/user-details', event.data.userId], {
        state: { userData: event.data },
      });
    }
  }

  openBulkUpdateModal(BulkUpdateModal: TemplateRef<any>): void {
    this.userBulkUpdateForm = this.formBuilder.group({
      reportingTo: null,
      generalManager: null,
      department: null,
      designation: null,
      changeReportsTo: null,
      changeGM: null,
    });
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    let initialState: any = {
      data: Object.values(this.selectedNodes),
      class: 'right-modal modal-700 ip-modal-unset',
    };
    this.selectedUsers = this.selectedNodes.map((node: any) => node?.data);
    this.bulkUpdateModalRef = this.modalService.show(
      BulkUpdateModal,
      initialState
    );
  }

  openBulkDeactiveModal(BulkDeactiveModal: TemplateRef<any>): void {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    let initialState: any = {
      data: Object.values(this.selectedNodes),
      class: 'right-modal modal-700 ip-modal-unset',
    };
    this.selectedUsers = this.selectedNodes.map((node: any) => node?.data);
    this.selectedUserIds = this.selectedNodes.map(
      (node: any) => node?.data?.userId
    );
    this.bulkDeactiveModalRef = this.modalService.show(
      BulkDeactiveModal,
      initialState
    );
  }

  handleUserStatus(remainingUsersModal: any) {
    const selectedNodesLength = this.gridApi?.getSelectedNodes()?.length || 0;
    if (this.selectedToggleUsers?.[0] === false) {
      if (selectedNodesLength > 0 && this.subscription) {
        const { licenseBought, activeUsers } = this.subscription;
        const remainingUsers = licenseBought - activeUsers;
        this.remainingUsers = remainingUsers;
        if (selectedNodesLength > remainingUsers) {
          this.modalRef = this.modalService.show(remainingUsersModal, {
            class: 'modal-600 top-modal ip-modal-unset',
            initialState: {
              remainingUsers: remainingUsers,
            },
          });
        } else {
          this.deleteConfirm();
        }
      }
    } else {
      this.deleteConfirm();
    }
  }

  deleteConfirm() {
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;

    let payload: any = {
      activateUser: !this.selectedToggleUsers[0],
      userIds: this.selectedUserIds,
      timeZoneInfo: this.userData?.timeZoneInfo,
      currentUserId: userId,
    };
    this._store.dispatch(
      new BulkToggleUserStatus(
        payload,
        this.selectedToggleUsers?.[0] ? 'Deactive' : 'Active'
      )
    );
    this.modalService.hide();
  }

  updateBulkUsers(): void {
    const data = this.userBulkUpdateForm.value;
    const payload: any[] = [];
    this.selectedUsers.forEach((user) => {
      payload.push({
        departmentId: data?.department || user?.department?.id,
        designationId: data?.designation || user?.designation?.id,
        userId: user.userId,
        reportsTo: data.changeReportsTo
          ? data?.reportingTo
          : user?.reportsTo?.id,
        generalManager: data.changeGM
          ? data?.generalManager
          : user?.generalManager?.id,
      });
    });
    this.gridOptions?.api?.deselectAll();
    this._store.dispatch(
      new UpdateBulkUsers({
        updateUsers: payload,
      })
    );
    this.isUpdateBulkUsersLoading = true;
    this._store
      .select(getUpdateBulkUsersIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.isUpdateBulkUsersLoading = isLoading;
        this.selectedUsers = [];
        this.selectedNodes = {};
        this.userBulkUpdateForm.reset();
        this.modalService.hide();
      });
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.trackingService.trackFeature(`Web.TeamUser.DataEntry.Search.DataEntry`)
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  onRemoveFilter(key: string, value: string) {
    if (
      typeof this.appliedFilter[key] === 'string' ||
      typeof this.appliedFilter[key] === 'number' ||
      typeof this.appliedFilter[key] === 'boolean'
    ) {
      this.appliedFilter[key] = null;
    } else if (['DateType', 'date'].includes(key)) {
      this.appliedFilter[key] = null;
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    }
    this.filterFunction();
  }

  getReportsName(id: string) {
    let propertyType = '';
    this.allUsers.forEach((type: any) => {
      if (type.id === id) {
        propertyType = type.firstName + ' ' + type.lastName;
      }
    });
    return propertyType;
  }

  getTimeZoneName(id: string) {
    let timeZoneName = '';
    this.timeZoneList?.forEach((type: any) => {
      if (type?.ianaZoneId === id) {
        timeZoneName = type?.displayName;
      }
    });
    return timeZoneName;
  }

  closeModal() {
    this.modalService.hide();
  }

  reset() {
    for (const prop in this.appliedFilter) {
      if (this.appliedFilter.hasOwnProperty(prop)) {
        this.appliedFilter[prop] = null;
      }
    }
    this.appliedFilter.pageNumber = 1;
    this.appliedFilter.userStatus = true;
  }

  openUpdateBulkRoles(updateBulkRoles: any) {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
      this.selectedUsers = this.selectedNodes?.map((node: any) => node?.data);
      this.selectedUserIds = this.selectedNodes?.map(
        (node: any) => node?.data?.userId
      );
      let initialState: any = {
        selectedUsers: this.selectedUsers,
      };
      this.modalRef = this.modalService.show(updateBulkRoles, {
        class: 'right-modal modal-550 ip-modal-unset',
      });
    }
  }

  updateBulkRole() {
    let filteredRoles = this.selectedRoles.filter((item) => item.roleId);
    let payload = {
      userRoles: filteredRoles.map((data: any) => ({
        roleId: data.roleId,
        roleName: data.roleName,
        enabled: true,
      })),
      userIds: this.selectedUserIds,
    };
    this._store.dispatch(new BulkUpdatePermissions(payload));
    this.selectedRoles = [];
    this.modalService.hide();
  }

  assignRole(isChecked: boolean, role: any): void {
    if (!role || !role.id) {
      return;
    }
    const roleAlreadySelected = this.selectedRoles.some(
      (item) => item.roleId === role.id
    );
    if (isChecked && !roleAlreadySelected) {
      // Add the role only if it's checked and not already selected
      let userRole = {
        roleId: role.id,
        description: role.description,
        enabled: true,
        roleName: role.name,
      };
      this.selectedRoles.push(userRole);
      this.noRoleSelected = false;
    } else if (!isChecked && roleAlreadySelected) {
      // Remove the role if it's unchecked and already selected
      this.selectedRoles = this.selectedRoles.filter(
        (item) => item.roleId !== role.id
      );
    }

    this.rolesList = this.rolesList
      .slice()
      .sort((a: any, b: any) => a.name.localeCompare(b.name));
  }

  getSanitizedHtml(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  removeUser(id: string): void {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((user: any) => user?.data?.userId === id);

    this.gridApi?.deselectNode(node?.[0]);

    this.selectedUsers = this.selectedUsers?.filter(
      (user: any) => user?.userId !== id
    );

    if (this.selectedUsers?.length <= 0) {
      this.bulkUpdateModalRef.hide();
      this.bulkDeactiveModalRef.hide();
    }
  }

  openConfirmDeleteModal(userName: string, id: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: userName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeUser(id);
        }
      });
    }
  }

  navigateToAddUser() {
    this.trackingService.trackFeature(`Web.TeamUser.Button.AddNewUser.Click`)
    this.router.navigate(['teams/add-user']);
  }

  openBulkUserUpload() {
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['teams/bulk-upload']);
    }
    this.trackingService.trackFeature(`Web.TeamUser.Button.BulkUpload.Click`)
    this.selectedOption = '';
  }

  openUserTracker() {
    if (this.selectedTrackerOption === 'delete') {
      let initialState: any = {
        fieldType: 'user',
      };
      this._store.dispatch(new FetchDeletedUserList(1, 10));
      this.modalService.show(DeletedUsersTrackerComponent, {
        class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedTrackerOption === 'export') {
      this._store.dispatch(new FetchUserExportTracker(1, 10));
      this.modalService.show(ExportUsersTrackerComponent, {
        class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
      });
    } else if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: 'user',
      };
      this._store.dispatch(new FetchUserExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState
      });
    }
    let Tracker: any = {
      delete: 'Delete',
      bulkUpload: 'BulkUpload',
      export: 'Export'
    }
    this.trackingService.trackFeature(`Web.TeamUser.Button.Tracker${Tracker[this.selectedTrackerOption]}.Click`)
    this.selectedTrackerOption = '';
  }

  exportUserReport() {
    this.trackingService.trackFeature(`Web.TeamUser.Button.Export.Click`)
    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  // navigateToRoleModal(saveDataModal: TemplateRef<any>) {
  //   this.modalRef.hide();
  //   // this.modalRef = this.modalService.show(saveDataModal, {
  //   //   class: 'modal-350 modal-dialog-centered',
  //   // });

  //   this.modalRef = this.modalService.show(AddRoleComponent, {
  //     class: 'right-modal modal-350',
  //   });
  // }
  bulkResetPassword() {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi.getSelectedNodes();
      this.selectedUserIds = this.selectedNodes?.map(
        (node: any) => node?.data?.userId
      );
      let initialState: any = {
        message: 'GLOBAL.user-confirmation',
        confirmType: 'reset',
        fieldType: "to 'Default Password'",
      };
      this.modalRef = this.modalService.show(
        UserConfirmationComponent,
        Object.assign(
          {},
          {
            class: 'modal-300 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
      if (this.modalRef?.onHide) {
        this.modalRef.onHide.subscribe((reason: string) => {
          if (reason == 'confirmed') {
            this.teamsService.resetPassword(this.selectedUserIds).subscribe((res: any) => {
              if (res?.succeeded) this._notificationService.success('The password has been reset to the default successfully.');
            })
          }
        });
      }
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
