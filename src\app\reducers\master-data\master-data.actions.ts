import { Action } from '@ngrx/store';
import {
  LeadSourceType,
  MasterAreaUnitType,
  MasterBuilderInfoType,
  MasterLeadStatusType,
  MasterPropertyAmenityListType,
  MasterPropertyAttributeType,
  MasterPropertyType,
  MasterUserServiceType,
} from 'src/app/core/interfaces/master-data.interface';

export enum masterDataActionTypes {
  FETCH_AMENITY_LIST = '[MASTER_DATA] Fetch Amenity List',
  FETCH_AMENITY_LIST_SUCCESS = '[MASTER_DATA] Fetch Amenity List Success',
  FETCH_ATTRIBUTE_LIST = '[MASTER_DATA] Fetch Property Attribute List',
  FETCH_ATTRIBUTE_LIST_SUCCESS = '[MASTER_DATA] Fetch Property Attribute List Success',
  FETCH_ANONYMOUS_ATTRIBUTE_LIST = '[MASTER_DATA] Fetch Property Anonymous Attribute List',
  FETCH_ANONYMOUS_ATTRIBUTE_LIST_SUCCESS = '[MASTER_DATA] Fetch Property Anonymous Attribute List Success',
  FETCH_LEAD_SOURCE_LIST = '[MASTER_DATA] Fetch Lead Source List',
  FETCH_LEAD_SOURCE_LIST_SUCCESS = '[MASTER_DATA] Fetch Lead Source List Success',
  FETCH_PROPERTY_TYPES_LIST = '[MASTER_DATA] Fetch Property Types List',
  FETCH_PROPERTY_TYPES_LIST_SUCCESS = '[MASTER_DATA] Fetch Property Types List Success',
  FETCH_QR_PROPERTY_TYPES_LIST = '[MASTER_DATA] Fetch QR Code Property Types List',
  FETCH_QR_PROPERTY_TYPES_LIST_SUCCESS = '[MASTER_DATA] Fetch QR Code Property Types List Success',
  FETCH_LEAD_STATUS_LIST = '[MASTER_DATA] Fetch Lead Status List',
  FETCH_LEAD_STATUS_LIST_SUCCESS = '[MASTER_DATA] Fetch Lead Status List Success',
  FETCH_AREA_UNIT_LIST = '[MASTER_DATA] Fetch Area Unit List',
  FETCH_AREA_UNIT_LIST_SUCCESS = '[MASTER_DATA] Fetch Area Unit List Success',
  FETCH_QR_AREA_UNIT_LIST = '[MASTER_DATA] Fetch QR Code Area Unit List',
  FETCH_QR_AREA_UNIT_LIST_SUCCESS = '[MASTER_DATA] Fetch QR Code Area Unit List Success',
  FETCH_USER_SERVICES_LIST = '[MASTER_DATA] Fetch User Services List',
  FETCH_USER_SERVICES_LIST_SUCCESS = '[MASTER_DATA] Fetch User Services List Success',
  FETCH_BRANDS_LIST = '[MASTER_DATA] Fetch Brands List',
  FETCH_BRANDS_LIST_SUCCESS = '[MASTER_DATA] Fetch Brands List Success',
  FETCH_PROJECT_TYPE = '[MASTER_DATA] Fetch Project Type',
  FETCH_PROJECT_TYPE_SUCCESS = '[MASTER_DATA] Fetch Project Type Success',
  FETCH_PROJECT_ATTRIBUTES = '[MASTER_DATA] Fetch Project Attributes',
  FETCH_PROJECT_ATTRIBUTES_SUCCESS = '[MASTER_DATA] Fetch Project Attributes Success',
  FETCH_PROJECT_AMENITIES = '[MASTER_DATA] Fetch Project Amenities',
  FETCH_PROJECT_AMENITIES_SUCCESS = '[MASTER_DATA] Fetch Project Amenities Success',
  FETCH_ASSOCIATE_BANK = '[MASTER_DATA] Fetch Associate Bank',
  FETCH_ASSOCIATE_BANK_SUCCESS = '[MASTER_DATA] Fetch Associate Bank Success',
  FETCH_MODIFIED_DATES_LIST = '[MASTER_DATA] Fetch Modified Dates List',
  FETCH_MODIFIED_DATES_LIST_SUCCESS = '[MASTER_DATA] Fetch Modified Dates List Success',
  FETCH_MODIFIED_DATES_LIST_LOADING = '[MASTER_DATA] Fetch Modified Dates List Loading',
}

export class FetchPropertyAmenityList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_AMENITY_LIST;
  constructor(public ApiType?: string) { }
}


export class FetchPropertyAmenityListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_AMENITY_LIST_SUCCESS;
  constructor(
    public data: MasterPropertyAmenityListType = {} as MasterPropertyAmenityListType
  ) { }
}

export class FetchAnonymousAttributeList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_ANONYMOUS_ATTRIBUTE_LIST;
  constructor() { }
}

export class FetchAnonymousAttributeListSuccess implements Action {
  readonly type: string =
    masterDataActionTypes.FETCH_ANONYMOUS_ATTRIBUTE_LIST_SUCCESS;
  constructor(public data: MasterPropertyAttributeType[] = []) { }
}
export class FetchLeadSourceList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_LEAD_SOURCE_LIST;
}
export class FetchPropertyTypesList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_PROPERTY_TYPES_LIST;
  constructor(public ApiType?: string) { }
}
export class FetchPropertyTypesListSuccess implements Action {
  readonly type: string =
    masterDataActionTypes.FETCH_PROPERTY_TYPES_LIST_SUCCESS;
  constructor(public data: MasterPropertyType[] = []) { }
}
export class FetchQRPropertyTypesList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_QR_PROPERTY_TYPES_LIST;
  constructor(public ApiType?: string) { }
}
export class FetchQRPropertyTypesListSuccess implements Action {
  readonly type: string =
    masterDataActionTypes.FETCH_QR_PROPERTY_TYPES_LIST_SUCCESS;
  constructor(public data: MasterPropertyType[] = []) { }
}
export class FetchLeadSourceListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_LEAD_SOURCE_LIST_SUCCESS;
  constructor(public data: LeadSourceType[] = []) { }
}
export class FetchLeadStatusList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_LEAD_STATUS_LIST;
  constructor() { }
}

export class FetchLeadStatusListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_LEAD_STATUS_LIST_SUCCESS;
  constructor(
    public data: MasterLeadStatusType[] = JSON.parse(
      localStorage.getItem('masterleadstatus')
    ) || []
  ) { }
}
export class FetchAreaUnitList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_AREA_UNIT_LIST;
  constructor() { }
}

export class FetchProjectAttributes implements Action {
  readonly type: string = masterDataActionTypes.FETCH_PROJECT_ATTRIBUTES;
  constructor() { }
}

export class FetchProjectAttributesSuccess implements Action {
  readonly type: string =
    masterDataActionTypes.FETCH_PROJECT_ATTRIBUTES_SUCCESS;
  constructor(public data: MasterAreaUnitType[] = []) { }
}

export class FetchAreaUnitListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_AREA_UNIT_LIST_SUCCESS;
  constructor(public data: MasterAreaUnitType[] = []) { }
}

export class FetchQRAreaUnitList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_QR_AREA_UNIT_LIST;
  constructor() { }
}

export class FetchQRAreaUnitListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_QR_AREA_UNIT_LIST_SUCCESS;
  constructor(public data: MasterAreaUnitType[] = []) { }
}
export class FetchUserServicesList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_USER_SERVICES_LIST;
  constructor() { }
}

export class FetchUserServicesListSuccess implements Action {
  readonly type: string =
    masterDataActionTypes.FETCH_USER_SERVICES_LIST_SUCCESS;
  constructor(public data: MasterUserServiceType[] = []) { }
}

export class FetchBrandsList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_BRANDS_LIST;
  constructor() { }
}

export class FetchBrandsListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_BRANDS_LIST_SUCCESS;
  constructor(public data: MasterBuilderInfoType[] = []) { }
}

export class FetchProjectAmenities implements Action {
  readonly type: string = masterDataActionTypes.FETCH_PROJECT_AMENITIES;
  constructor() { }
}

export class FetchFetchProjectAmenitiesSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_PROJECT_AMENITIES_SUCCESS;
  constructor(public data: MasterAreaUnitType[] = []) { }
}

export class FetchProjectTypes implements Action {
  readonly type: string = masterDataActionTypes.FETCH_PROJECT_TYPE;
  constructor() { }
}

export class FetchProjectProjectTypesSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_PROJECT_TYPE_SUCCESS;
  constructor(public data: MasterAreaUnitType[] = []) { }
}

export class FetchAssociatedBanks implements Action {
  readonly type: string = masterDataActionTypes.FETCH_ASSOCIATE_BANK;
  constructor() { }
}

export class FetchAssociatedBanksSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_ASSOCIATE_BANK_SUCCESS;
  constructor(public data: MasterAreaUnitType[] = []) { }
}

export class FetchModifiedDatesList implements Action {
  readonly type: string = masterDataActionTypes.FETCH_MODIFIED_DATES_LIST;
  constructor() { }
}

export class FetchModifiedDatesListSuccess implements Action {
  readonly type: string = masterDataActionTypes.FETCH_MODIFIED_DATES_LIST_SUCCESS;
  constructor(public data: any = {}) { }
}

export class FetchModifiedDatesListLoading implements Action {
  readonly type: string = masterDataActionTypes.FETCH_MODIFIED_DATES_LIST_LOADING;
  constructor() { }
}


