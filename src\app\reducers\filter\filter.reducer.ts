import { Action, createSelector } from "@ngrx/store";
import { AppState } from "src/app/app.reducer";
import { FetchFilterSuccess, FilterActionTypes, FilterExistSuccess, SaveFilterSuccess, UpdateSavedFilterSuccess } from "./filter.action";

export type FilterState = {
    getFilter: any;
    getSavedFilter: any;
    isFilterExist: boolean;
    loaders: {
        getFilter: boolean;
    };
}

const initialState: FilterState = {
    getFilter: null,
    getSavedFilter: null,
    isFilterExist: false,
    loaders: {
        getFilter: false,
    },
}

const updateLoader = (state: FilterState, key: keyof FilterState["loaders"], value: boolean) => ({
    ...state.loaders,
    [key]: value,
});

export function filterReducer(
    state: FilterState = initialState,
    action: Action
) {
    switch (action.type) {
        case FilterActionTypes.FETCH_FILTER:
            return {
                ...state,
                loaders: updateLoader(state, "getFilter", true),
            }
        case FilterActionTypes.FETCH_FILTER_SUCCESS:
            return {
                ...state,
                getFilter: (action as FetchFilterSuccess).response,
                loaders: updateLoader(state, "getFilter", false),
            }
        case FilterActionTypes.SAVE_FILTER_SUCCESS:
            return {
                ...state,
                getSavedFilter: (action as SaveFilterSuccess).response,
            }
        case FilterActionTypes.UPDATE_SAVED_FILTER_SUCCESS:
            return {
                ...state,
                getFilter: state?.getFilter?.map((filter: any) => {
                    return filter.id === (action as UpdateSavedFilterSuccess).response.id
                        ? {
                            ...filter,
                            ...(action as UpdateSavedFilterSuccess).response
                        }
                        : filter;
                }),
            };
        case FilterActionTypes.FILTER_EXIST_SUCCESS:
            return {
                ...state,
                isFilterExist: (action as FilterExistSuccess).response,
            }
        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.filter;

export const getFilterLoaders = createSelector(
    selectFeature,
    (state: FilterState) => state.loaders
)

export const getFilter = createSelector(
    selectFeature,
    (state: FilterState) => state.getFilter
);

export const getSavedFilter = createSelector(
    selectFeature,
    (state: FilterState) => state.getSavedFilter
);

export const getFilterExist = createSelector(
    selectFeature,
    (state: FilterState) => state.isFilterExist
);