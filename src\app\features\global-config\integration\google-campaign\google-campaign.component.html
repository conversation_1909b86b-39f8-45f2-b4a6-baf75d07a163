<div routerLink="/global-config"
  class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 ml-4 cursor-pointer">
</div>
<ng-container *ngIf="canView && !isAccountsLoading else loader">
  <div class="w-100 px-30 pt-20 h-100-46">
    <div class="flex-end  tb-flex-center-unset tb-flex-col">
      <div class="flex-end flex-wrap w-50 tb-w-100">
        <!-- <a class="bg-coal p-8 br-6 mr-12 text-sm text-white tb-mt-10" *ngIf="canAdd"
          (click)="openCustomGoogleCampaign()">Custom</a> -->
        <div (click)="login()" class="border br-8 bg-white p-8 tb-mt-10 cursor-pointer" id="login">
          <img height="15" class="mr-10 " src="../../../../../assets/images/integration/googleads.svg"
            alt="Google Logo" />
        </div>
      </div>
    </div>

    <div class="mt-20">
      <ng-container *ngIf="!filteredAccounts?.length else addAndListingPage">
        <div class="flex-center">
          <div class="w-100 m-20 min-w-350 max-w-350 ph-modal-unset">
            <div class="mt-40 justify-center-col">
              <div class="d-flex">
                <div class="align-center-col">
                  <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                    <span class="icon ic-download ic-lg ic-coal"></span>
                  </div>
                  <div class="border-left-dotted h-60"></div>
                </div>
                <p class="text-coal fw-600 ml-20 mt-20">Click Login With Google ads</p>
              </div>
              <div class="d-flex">
                <div class="align-center-col">
                  <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                    <span class="icon ic-share ic-lg ic-coal"></span>
                  </div>
                  <div class="border-left-dotted h-60"></div>
                </div>
                <div class="text-gray ml-20 fw-semi-bold">Share your
                  <span class="fw-600 text-coal"> Google Ads</span>
                  account with
                  <span class="fw-600 text-coal"> Google Ads</span>
                  to sync leads with {{getAppName()}}
                  automatically.
                </div>
              </div>
              <div class="d-flex">
                <div class="dot dot-x-xxl bg-pearl cursor-pointer">
                  <span class="icon ic-connection ic-lg ic-coal"></span>
                </div>
                <div class="text-gray ml-20">
                  <p>Connection will be established automatically.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #addAndListingPage>
        <ng-container *ngFor="let account of filteredAccounts">
          <div class="align-center border-bottom bg-white p-8 fw-600 scrollbar scroll-hide ip-w-100-40">
            <div class="flex-grow-1 mr-10">
              <div class="text-xs fv-sm-caps mb-10">Account Name</div>
              <h5 class="text-light-gray fw-600 text-truncate mt-4">{{ account.accountName }}</h5>
            </div>
            <div class="w-150 mr-10">
              <div class="text-xs fv-sm-caps mb-10">Account ID</div>
              <h5 class="text-light-gray fw-600 text-truncate mt-4">{{ account.customerId }}</h5>
            </div>
            <div class="w-60 mr-20">
              <div class="text-xs fv-sm-caps mb-10 text-nowrap">Leads Count</div>
              <h5 class="text-light-gray fw-600 text-truncate mt-4">{{ account.leadCount }}</h5>
            </div>
            <div class="w-110 mr-30">
              <div class="text-xs fv-sm-caps mb-6">Actions</div>
              <div class="align-center">
                <div *ngIf="canDelete" title="Delete" class="bg-light-red icon-badge"
                  (click)="initDeleteIntegration(account.id, account.accountName)">
                  <span class="icon ic-delete ic-xxxs"></span>
                </div>
              </div>
            </div>
            <div class="w-115 align-center mr-40">
              <div class="flex-center cursor-pointer ml-20" (click)="toggleExpand(account, 'isCampaignsExpanded')">
                <span [ngClass]="{'text-accent-green fw-700 header-5' : account.isCampaignsExpanded}">
                  Campaigns</span>
                <span [ngClass]="{ 'rotate-180 ic-accent-green' : account.isCampaignsExpanded}"
                  class="icon ic-triangle-down ic-xs ic-coal ml-8"></span>
              </div>
              <!-- <div class="flex-center cursor-pointer" (click)="toggleExpand(account, 'isAdsExpanded')">
                <span [ngClass]="{'text-accent-green fw-700 header-5' : account.isAdsExpanded}">
                  Ads</span>
                <span [ngClass]="{ 'rotate-180 ic-accent-green' : account.isAdsExpanded}"
                  class="icon ic-triangle-down ic-xs ic-coal ml-8"></span>
              </div> -->
            </div>
          </div>
          <div *ngIf="account.isAdsExpanded" class="bg-white w-100">
            <div class="flex-between border w-100">
              <div class="no-validation w-100 border-end position-relative mr-12">
                <div class="align-center px-10 py-12">
                  <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                  <input type="text" (input)="isEmptyInput()" placeholder="type to search" [(ngModel)]="searchTerm"
                    (keydown.enter)="onSearch()" [ngModelOptions]="{standalone: true}"
                    class="border-0 outline-0 w-100" />
                </div>
                <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                  (Press enter to search)</small>
              </div>
              <form class="d-flex mr-12 position-relative" [formGroup]="cplTrackForm">
                <div class="flex-center bg-violet-600 p-6 cursor-pointer" (click)="showCplFilter = !showCplFilter">
                  <span class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4"></span>
                  <span
                    class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{cplTrackForm.controls['cplTrackRange'].value}}</span>
                  <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                </div>
                <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001" *ngIf="showCplFilter">
                  <div class="d-flex">
                    <div class="w-100 bg-light-pearl">
                      <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                        Range
                      </div>
                      <ng-container *ngFor="let type of cplDateFilterList">
                        <div class="form-check form-check-inline p-6">
                          <input type="radio" id="inpcplTrack{{type.value}}" name="cplTrackRange"
                            formControlName="cplTrackRange" [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                          <label class="text-dark-gray cursor-pointer text-large text-sm"
                            for="inpcplTrack{{type.value}}">{{type.displayName}}</label>
                        </div>
                      </ng-container>
                      <div class="position-relative dashboard-filter form-group m-6 mb-16"
                        [ngClass]="{'pe-none disabled' : cplTrackForm.controls['cplTrackRange'].value !== 'Custom'}">
                        <form-errors-wrapper [control]="cplTrackForm.controls['cplTrackDate']" label="Date">
                          <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                            [selectMode]="'range'" formControlName="cplTrackDate" placeholder="Select date" />
                          <owl-date-time [pickerType]="'calendar'" #dt1
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </form-errors-wrapper>
                      </div>
                    </div>
                  </div>
                  <div class="flex-end p-6">
                    <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                      (click)="showCplFilter = false">
                      Cancel</h6>
                    <div class="btn-coal" (click)="onCplTrackChange()">Apply</div>
                  </div>
                </div>
              </form>
            </div>
            <!-- Ads Date Filter Display -->
            <div class="bg-white px-4 py-12" *ngIf="account.isAdsExpanded && showFilters">
              <ng-container
                *ngIf="appliedFilter.adsDateRange || (appliedFilter.adsCustomDate && appliedFilter.adsCustomDate.length > 0)">
                <div class="bg-secondary flex-between">
                  <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                    <div class="d-flex">
                      <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                        *ngIf="appliedFilter.adsDateRange">
                        Ads Date: {{appliedFilter.adsDateRange}}
                        <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                          (click)="onRemoveFilter('adsDateRange', appliedFilter.adsDateRange)"></span>
                      </div>
                      <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                        *ngIf="appliedFilter.adsCustomDate && appliedFilter.adsCustomDate.length > 0">
                        Ads Custom Date: {{appliedFilter.adsCustomDate[0] | date:'dd/MM/yyyy'}} -
                        {{appliedFilter.adsCustomDate[1] | date:'dd/MM/yyyy'}}
                        <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                          (click)="onRemoveFilter('adsDateRange', null)"></span>
                      </div>
                    </div>
                  </drag-scroll>
                  <div
                    class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                    (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
                </div>
              </ng-container>
            </div>
          </div>
          <ng-container *ngIf="account.isAdsExpanded">
            <div *ngIf="account.isAdsLoading" class="flex-center p-20">
              <application-loader></application-loader>
            </div>
            <ng-container *ngIf="!account.isAdsLoading">
              <ng-container *ngIf="account.ads?.length > 0; else noAdsFound">
                <div class="manage-leads">
                  <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
                    [rowData]="account.paginatedAds" [suppressPaginationPanel]="true"
                    [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                    (filterChanged)="onFilterChanged($event)">
                  </ag-grid-angular>
                </div>
                <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
                  *ngIf="account.paginatedAds?.length && account.adsTotalCount > 0">
                  <div class="mr-10 ip-mt-10">
                    Showing {{((account.adsPageNumber - 1) * account.adsPageSize) + 1}} to
                    {{((account.adsPageNumber - 1) * account.adsPageSize) + account.adsPageSize > account.adsTotalCount
                    ? account.adsTotalCount :
                    ((account.adsPageNumber - 1) * account.adsPageSize) + account.adsPageSize}} of
                    {{account.adsTotalCount}} entries
                  </div>
                  <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                    <div class="flex-center">
                      <span class="mr-10">Entries per page</span>
                      <ng-select [virtualScroll]="true" [placeholder]="account.adsPageSize" bindValue="id"
                        [searchable]="false" ResizableDropdown class="w-80" (change)="assignAdsCount(account)"
                        [(ngModel)]="account.adsPageSize" [dropdownPosition]="'top'">
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                          {{pageSize}}
                        </ng-option>
                      </ng-select>
                    </div>
                    <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                    <pagination [offset]="account.adsPageNumber-1" [limit]="1" [range]="1"
                      [size]="getPages(account.adsTotalCount, account.adsPageSize)"
                      (pageChange)="onAdsPageChange($event, account)" [isV2Pagination]="true">
                    </pagination>
                  </div>
                </div>
              </ng-container>
              <ng-template #noAdsFound>
                <div class="flex-col flex-center h-100-270">
                  <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
                  <div class="fw-semi-bold text-xl text-mud">No Ads Found!</div>
                </div>
              </ng-template>
            </ng-container>
          </ng-container>
          <div *ngIf="account.isCampaignsExpanded" class="bg-white w-100">
            <div class="flex-between border w-100">
              <div class="no-validation w-100 border-end position-relative mr-12">
                <div class="align-center px-10 py-12">
                  <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                  <input type="text" (input)="isEmptyCampaignsInput()" placeholder="type to search"
                    [(ngModel)]="campaignsSearchTerm" (keydown.enter)="onCampaignsSearch()"
                    [ngModelOptions]="{standalone: true}" class="border-0 outline-0 w-100" />
                </div>
                <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                  (Press enter to search)</small>
              </div>
              <form class="d-flex mr-12 position-relative" [formGroup]="campaignsCplTrackForm">
                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                  (click)="showCampaignsCplFilter = !showCampaignsCplFilter">
                  <span class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4"></span>
                  <span
                    class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{campaignsCplTrackForm.controls['cplTrackRange'].value}}</span>
                  <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                </div>
                <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001"
                  *ngIf="showCampaignsCplFilter">
                  <div class="d-flex">
                    <div class="w-100 bg-light-pearl">
                      <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                        Range
                      </div>
                      <ng-container *ngFor="let type of cplDateFilterList">
                        <div class="form-check form-check-inline p-6">
                          <input type="radio" id="inpcampaignsCplTrack{{type.value}}" name="cplTrackRange"
                            formControlName="cplTrackRange" [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                          <label class="text-dark-gray cursor-pointer text-large text-sm"
                            for="inpcampaignsCplTrack{{type.value}}">{{type.displayName}}</label>
                        </div>
                      </ng-container>
                      <div class="position-relative dashboard-filter form-group m-6 mb-16"
                        [ngClass]="{'pe-none disabled' : campaignsCplTrackForm.controls['cplTrackRange'].value !== 'Custom'}">
                        <form-errors-wrapper [control]="campaignsCplTrackForm.controls['cplTrackDate']" label="Date">
                          <input type="text" readonly [owlDateTimeTrigger]="dt2" [owlDateTime]="dt2"
                            [selectMode]="'range'" formControlName="cplTrackDate" placeholder="Select date" />
                          <owl-date-time [pickerType]="'calendar'" #dt2
                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </form-errors-wrapper>
                      </div>
                    </div>
                  </div>
                  <div class="flex-end p-6">
                    <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                      (click)="showCampaignsCplFilter = false">
                      Cancel</h6>
                    <div class="btn-coal" (click)="onCampaignsCplTrackChange()">Apply</div>
                  </div>
                </div>
              </form>
            </div>
            <!-- Campaigns Date Filter Display -->
            <div class="bg-white px-4 py-12" *ngIf="account.isCampaignsExpanded && showFilters">
              <ng-container
                *ngIf="appliedFilter.campaignsDateRange || (appliedFilter.campaignsCustomDate && appliedFilter.campaignsCustomDate.length > 0)">
                <div class="bg-secondary flex-between">
                  <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                    <div class="d-flex">
                      <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                        *ngIf="appliedFilter.campaignsDateRange">
                        Campaigns Date: {{appliedFilter.campaignsDateRange}}
                        <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                          (click)="onRemoveFilter('campaignsDateRange', appliedFilter.campaignsDateRange)"></span>
                      </div>
                      <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                        *ngIf="appliedFilter.campaignsCustomDate && appliedFilter.campaignsCustomDate.length > 0">
                        Campaigns Custom Date: {{appliedFilter.campaignsCustomDate[0] | date:'dd/MM/yyyy'}} -
                        {{appliedFilter.campaignsCustomDate[1] | date:'dd/MM/yyyy'}}
                        <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                          (click)="onRemoveFilter('campaignsDateRange', null)"></span>
                      </div>
                    </div>
                  </drag-scroll>
                  <div
                    class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                    (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
                </div>
              </ng-container>
            </div>
          </div>
          <ng-container *ngIf="account.isCampaignsExpanded">
            <div *ngIf="account.isCampaignsLoading" class="flex-center p-20">
              <application-loader></application-loader>
            </div>
            <div *ngIf="!account.isCampaignsLoading" class="scrollbar ip-w-100-40 mb-30">
              <ng-container *ngIf="account.campaigns?.length > 0; else noCampaignsFound">
                <div class="manage-leads">
                  <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptionsCampaigns"
                    (gridReady)="onCampaignsGridReady($event)" [rowData]="account.paginatedCampaigns"
                    [suppressPaginationPanel]="true" [alwaysShowHorizontalScroll]="true"
                    [alwaysShowVerticalScroll]="true" (filterChanged)="onFilterChanged($event)">
                  </ag-grid-angular>
                </div>
                <div class="flex-between ip-col-reverse ip-flex-end p-16 ip-px-4"
                  *ngIf="account.campaignsTotalCount > 0">
                  <div class="mr-10 ip-mt-10">
                    Showing {{((account.campaignsPageNumber - 1) * account.campaignsPageSize) + 1}} to
                    {{((account.campaignsPageNumber - 1) * account.campaignsPageSize) + account.campaignsPageSize >
                    account.campaignsTotalCount ? account.campaignsTotalCount :
                    ((account.campaignsPageNumber - 1) * account.campaignsPageSize) + account.campaignsPageSize}} of
                    {{account.campaignsTotalCount}} entries
                  </div>
                  <div class="show-dropdown-white flex-center ph-flex-col ph-flex-end">
                    <div class="flex-center">
                      <span class="mr-10">Entries per page</span>
                      <ng-select [virtualScroll]="true" [placeholder]="account.campaignsPageSize" bindValue="id"
                        [searchable]="false" ResizableDropdown class="w-80" (change)="assignCampaignsCount(account)"
                        [(ngModel)]="account.campaignsPageSize" [dropdownPosition]="'top'">
                        <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                          {{pageSize}}
                        </ng-option>
                      </ng-select>
                    </div>
                    <div class="mx-8 my-4 border-right h-16 ph-d-none"></div>
                    <pagination [offset]="account.campaignsPageNumber-1" [limit]="1" [range]="1"
                      [size]="getPages(account.campaignsTotalCount, account.campaignsPageSize)"
                      (pageChange)="onCampaignsPageChange($event, account)" [isV2Pagination]="true">
                    </pagination>
                  </div>
                </div>
              </ng-container>
              <ng-template #noCampaignsFound>
                <div class="flex-col flex-center h-100-270">
                  <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
                  <div class="fw-semi-bold text-xl text-mud">No Campaigns Found!</div>
                </div>
              </ng-template>
            </div>
          </ng-container>
        </ng-container>
      </ng-template>
    </div>
  </div>
</ng-container>
<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>