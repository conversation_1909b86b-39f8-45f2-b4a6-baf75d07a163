import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  LeadSourceType,
  MasterAreaUnitType,
  MasterBuilderInfoType,
  MasterLeadStatusType,
  MasterPropertyAmenityListType,
  MasterPropertyAttributeType,
  MasterPropertyType,
  MasterUserServiceType,
} from 'src/app/core/interfaces/master-data.interface';
import {
  FetchAnonymousAttributeListSuccess,
  FetchAreaUnitListSuccess,
  FetchAssociatedBanksSuccess,
  FetchBrandsListSuccess,
  FetchFetchProjectAmenitiesSuccess,
  FetchLeadSourceListSuccess,
  FetchLeadStatusListSuccess,
  FetchModifiedDatesListSuccess,
  FetchProjectAttributesSuccess,
  FetchProjectProjectTypesSuccess,
  FetchPropertyAmenityListSuccess,
  FetchPropertyTypesListSuccess,
  FetchQRAreaUnitListSuccess,
  FetchQRPropertyTypesListSuccess,
  FetchUserServicesListSuccess,
  masterDataActionTypes,
} from 'src/app/reducers/master-data/master-data.actions';

export type MasterDataState = {
  amenities?: MasterPropertyAmenityListType;
  isAmenitiesLoading: boolean;
  isAttributesLoading: boolean;
  attrAnonymous?: MasterPropertyAttributeType[];
  isAttrAnonymousLoading: boolean;
  leadSource?: LeadSourceType[];
  propertyTypes?: MasterPropertyType[];
  qrPropertyTypes?: MasterPropertyType[];
  leadStatus?: MasterLeadStatusType[];
  lastModifiedList?: any;
  isLastModifiedListLoading: boolean;
  areaUnits?: MasterAreaUnitType[];
  qrAreaUnits?: MasterAreaUnitType[];
  userServicesList?: MasterUserServiceType[];
  brandsList?: MasterBuilderInfoType[];
  areaUnitsIsLoading: boolean;
  isLeadStatusLoading: boolean;
  isLeadSourceLoading: boolean;
  projectType: any[];
  projectAttributes: any[];
  ProjectAmenities: any;
  isProjectAmenitiesLoading: boolean;
  ProjectTypes: any[];
  AssociateBanks: any[];
  fetchModifiedDatesList: any;
};

const initialState: MasterDataState = {
  amenities: {} as MasterPropertyAmenityListType,
  isAmenitiesLoading: true,
  isAttributesLoading: true,
  attrAnonymous: [],
  isAttrAnonymousLoading: true,
  leadSource: [],
  propertyTypes: [],
  qrPropertyTypes: [],
  leadStatus: [],
  lastModifiedList: {},
  isLastModifiedListLoading: false,
  areaUnits: [],
  qrAreaUnits: [],
  userServicesList: [],
  brandsList: [],
  projectType: [],
  projectAttributes: [],
  ProjectAmenities: {},
  isProjectAmenitiesLoading: true,
  ProjectTypes: [],
  AssociateBanks: [],
  areaUnitsIsLoading: true,
  isLeadStatusLoading: true,
  isLeadSourceLoading: true,
  fetchModifiedDatesList: {},
};

export function masterDataReducer(
  state: MasterDataState = initialState,
  action: Action
): MasterDataState {
  switch (action.type) {
    case masterDataActionTypes.FETCH_AMENITY_LIST:
      return {
        ...state,
        isAmenitiesLoading: true,
      };
    case masterDataActionTypes.FETCH_AMENITY_LIST_SUCCESS:
      return {
        ...state,
        amenities:
          (action as FetchPropertyAmenityListSuccess).data ||
          ({} as MasterPropertyAmenityListType),
        isAmenitiesLoading: false,
      };
    case masterDataActionTypes.FETCH_ATTRIBUTE_LIST:
      return {
        ...state,
        isAttributesLoading: true,
      };
    case masterDataActionTypes.FETCH_ANONYMOUS_ATTRIBUTE_LIST:
      return {
        ...state,
        isAttrAnonymousLoading: true,
      };
    case masterDataActionTypes.FETCH_ANONYMOUS_ATTRIBUTE_LIST_SUCCESS:
      return {
        ...state,
        attrAnonymous: (action as FetchAnonymousAttributeListSuccess).data || [],
        isAttrAnonymousLoading: false,
      };
    case masterDataActionTypes.FETCH_LEAD_SOURCE_LIST:
      return {
        ...state,
        isLeadSourceLoading: true,
      };
    case masterDataActionTypes.FETCH_LEAD_SOURCE_LIST_SUCCESS:
      return {
        ...state,
        leadSource: (action as FetchLeadSourceListSuccess).data || [],
        isLeadSourceLoading: false,
      };
    case masterDataActionTypes.FETCH_LEAD_STATUS_LIST:
      return {
        ...state,
        isLeadStatusLoading: true,
      };
    case masterDataActionTypes.FETCH_LEAD_STATUS_LIST_SUCCESS:
      return {
        ...state,
        leadStatus: (action as FetchLeadStatusListSuccess).data || [],
        isLeadStatusLoading: false,
      };
    case masterDataActionTypes.FETCH_AREA_UNIT_LIST:
      return {
        ...state,
        areaUnitsIsLoading: true,
      };
    case masterDataActionTypes.FETCH_AREA_UNIT_LIST_SUCCESS:
      return {
        ...state,
        areaUnits: (action as FetchAreaUnitListSuccess).data || [],
        areaUnitsIsLoading: false,
      };
    case masterDataActionTypes.FETCH_QR_AREA_UNIT_LIST_SUCCESS:
      return {
        ...state,
        qrAreaUnits: (action as FetchQRAreaUnitListSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_PROPERTY_TYPES_LIST_SUCCESS:
      return {
        ...state,
        propertyTypes: (action as FetchPropertyTypesListSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_QR_PROPERTY_TYPES_LIST_SUCCESS:
      return {
        ...state,
        qrPropertyTypes: (action as FetchQRPropertyTypesListSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_USER_SERVICES_LIST_SUCCESS:
      return {
        ...state,
        userServicesList: (action as FetchUserServicesListSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_BRANDS_LIST_SUCCESS:
      return {
        ...state,
        brandsList: (action as FetchBrandsListSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_PROJECT_ATTRIBUTES_SUCCESS:
      return {
        ...state,
        projectAttributes: (action as FetchProjectAttributesSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_PROJECT_AMENITIES:
      return {
        ...state,
        isProjectAmenitiesLoading: true,
      };
    case masterDataActionTypes.FETCH_PROJECT_AMENITIES_SUCCESS:
      return {
        ...state,
        ProjectAmenities:
          (action as FetchFetchProjectAmenitiesSuccess).data || [],
        isProjectAmenitiesLoading: false,
      };
    case masterDataActionTypes.FETCH_PROJECT_TYPE_SUCCESS:
      return {
        ...state,
        ProjectTypes: (action as FetchProjectProjectTypesSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_ASSOCIATE_BANK_SUCCESS:
      return {
        ...state,
        AssociateBanks: (action as FetchAssociatedBanksSuccess).data || [],
      };
    case masterDataActionTypes.FETCH_MODIFIED_DATES_LIST_LOADING:
      return {
        ...state,
        isLastModifiedListLoading: true,
      };
    case masterDataActionTypes.FETCH_MODIFIED_DATES_LIST_SUCCESS:
      return {
        ...state,
        fetchModifiedDatesList: (action as FetchModifiedDatesListSuccess).data || {},
        isLastModifiedListLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.masterData;

export const getAreaUnits = createSelector(
  selectFeature,
  (state: MasterDataState) => state.areaUnits
)

export const getAreaUnitIsLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.areaUnitsIsLoading
);

export const getStatusIsLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.isLeadStatusLoading
);

export const getLeadSource = createSelector(
  selectFeature,
  (state: MasterDataState) => state.leadSource
)

export const getLeadSourceIsLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.isLeadSourceLoading
);

export const getAmenities = createSelector(
  selectFeature,
  (state: MasterDataState) => state.amenities
)

export const getIsAmenitiesLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.isAmenitiesLoading
)


export const getIsAttributesLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.isAttributesLoading
);

export const getAttrAnonymousMasterData = createSelector(
  selectFeature,
  (state: MasterDataState) => state.attrAnonymous
);

export const getStatusMasterData = createSelector(
  selectFeature,
  (state: MasterDataState) => state.leadStatus
);

export const getProjectUnitAttributes = createSelector(
  selectFeature,
  (state: MasterDataState) => state.projectAttributes
);

export const getProjectAmenities = createSelector(
  selectFeature,
  (state: MasterDataState) => state.ProjectAmenities
);

export const getProjectAmenitiesIsLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.isProjectAmenitiesLoading
);

export const getProjectTypes = createSelector(
  selectFeature,
  (state: MasterDataState) => state.ProjectTypes
);

export const getAssociatedBanks = createSelector(
  selectFeature,
  (state: MasterDataState) => state.AssociateBanks
);

export const getPropertyTypes = createSelector(
  selectFeature,
  (state: MasterDataState) => state.propertyTypes
)

export const getQrPropertyTypes = createSelector(
  selectFeature,
  (state: MasterDataState) => state.qrPropertyTypes
)

export const getQrAreaUnits = createSelector(
  selectFeature,
  (state: MasterDataState) => state.qrAreaUnits
)

export const getLastModifiedList = createSelector(
  selectFeature,
  (state: MasterDataState) => state.lastModifiedList
)

export const getLastModifiedListIsLoading = createSelector(
  selectFeature,
  (state: MasterDataState) => state.isLastModifiedListLoading
)

export const getFetchModifiedDatesList = createSelector(
  selectFeature,
  (state: MasterDataState) => {
    return {
      data: state.fetchModifiedDatesList,
      isLoading: state.isLastModifiedListLoading
    }
  }
)
