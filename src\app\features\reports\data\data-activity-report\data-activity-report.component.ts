import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { Subject, switchMap, takeUntil } from 'rxjs';

import {
  PAGE_SIZE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  FetchDataActivityExportSuccess,
  FetchDataReportsActivityCommunication,
  UpdateDataActivityFilterPayload,
} from 'src/app/reducers/data-reports/data-reports.action';
import {
  getActivityCommunicationIsLoading,
  getActivityIsLoading,
  getDataReportsActivityList,
  getReportsActivityCommunicationList,
} from 'src/app/reducers/data-reports/data-reports.reducers';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { UpdateAllActivityFilterPayload } from 'src/app/reducers/reports/reports.actions';
import { getAllActivityFilterPayload } from 'src/app/reducers/reports/reports.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'data-activity-report',
  templateUrl: './data-activity-report.component.html',
})
export class DataActivityReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: Array<any> = [];
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  getPages = getPages;
  appliedFilter: any;
  filtersPayload: any;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  isDateFilter: string = 'today';
  dataActivityTotalCount: number;
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  isAllUsersLoading: boolean = true;

  isActivityLoading: boolean;
  isActivityCommunicationLoading: boolean;
  showFilters: boolean = false;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  userData: any;
  onPickerOpened = onPickerOpened;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  s3BucketUrl: string = environment.s3ImageBucketURL;
  filteredColumnDefsCache: any[] = [];

  @ViewChild('reportsGraph') reportsGraph: any;
  
  get isGraphLoading(): boolean {
    return (
      this.isActivityCommunicationLoading ||
      this.isActivityLoading
    );
  }
  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef
  ) {
    this.headerTitle.setTitle('Data - Activity Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.filtersPayload = {
          fromDate: setTimeZoneDate(
            new Date(this.currentDate),
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          toDate: setTimeZoneDate(
            new Date(this.currentDate),
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
        };
      });

    this._store
      .select(getAllActivityFilterPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = {
          ...this.filtersPayload,
          ...data,
          isNavigatedFromReports: true,
        };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          userStatus: userStatus,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          visibility: this.filtersPayload?.userStatus,
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
        };

        this.activeDate();
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        this.isOnlyReporteesLoading = false;
        if (!isLoading) {
          this.currentVisibility(1);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');

          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        this.isAllUsersLoading = false;
        if (!isLoading) {
          this.currentVisibility(1);
        }
      });

    this._store
      .select(getReportsActivityCommunicationList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items2;
      });

    this._store
      .select(getDataReportsActivityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.dataActivityTotalCount = data.totalCount;
        this.rowData = this.rowData?.map?.((row: any) => {
          const matchingData = data.items?.find(
            (item: any) => item.userId === row.userId
          );
          return { ...row, ...matchingData };
        });
        this.rowData = getTotalCountForReports(this.rowData);
      });

    this._store
      .select(getActivityCommunicationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isActivityCommunicationLoading = isLoading;
      });

    this._store
      .select(getActivityIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isActivityLoading = isLoading;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Reports.ExportAllUsers')) {
          this.canExportAllUsers = true;
        }
        if (permissions?.includes('Permissions.Reports.ExportReportees')) {
          this.canExportReportees = true;
        }

        if (permissions?.includes('Permissions.Reports.ViewAllUsers')) {
          this.canViewAllUsers = true;
          this._store.dispatch(new FetchUsersListForReassignment());
        } else if (permissions?.includes('Permissions.Reports.ViewReportees')) {
          this.canViewReportees = true;
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.initializeGridSettings();
    this.initializeGraphData();
  }

  ngOnInit() {
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });
  }

  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'User Name'
    );
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [params.data?.userName],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<p class="py-16 text-truncate">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Calls',
        field: 'Calls',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.callsInitiatedAllCount,
          params.data?.callsInitiatedDataUniqueCount,
        ],
        valueLabels: ['Calls', 'Calls (unique count)'],
        minWidth: 130,
        cellRenderer: (params: any) => {
          if (this.isActivityCommunicationLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
              }<span></p>`;
          }
        },
      },
      {
        headerName: 'Whatsapp',
        field: 'Whatsapp',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.whatsAppInitiatedAllCount,
          params.data?.whatsAppInitiatedDataUniqueCount,
        ],
        valueLabels: ['Whatsapp', 'Whatsapp (unique count)'],
        minWidth: 130,
        cellRenderer: (params: any) => {
          if (this.isActivityCommunicationLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
              }<span></p>`;
          }
        },
      },
      {
        headerName: 'Status Edits',
        field: 'Status Edits',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.statusEditsAllCount,
          params.data?.statusEditsDataUniqueCount,
        ],
        valueLabels: ['Status Edits', 'Status Edits (unique count)'],
        minWidth: 130,
        cellRenderer: (params: any) => {
          if (this.isActivityLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
              }<span></p>`;
          }
        },
      },
      {
        headerName: 'Form Edits',
        field: 'Form Edits',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.formEditsAllCount,
          params.data?.formEditsDataUniqueCount,
        ],
        valueLabels: ['Form Edits', 'Form Edits (unique count)'],
        minWidth: 130,
        cellRenderer: (params: any) => {
          if (this.isActivityLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
              }<span></p>`;
          }
        },
      },
      {
        headerName: 'Notes Added',
        field: 'Notes Added',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.notesAddedAllCount,
          params.data?.notesAddedDataUniqueCount,
        ],
        valueLabels: ['Notes Added', 'Notes Added (unique count)'],
        minWidth: 130,
        cellRenderer: (params: any) => {
          if (this.isActivityLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
              }<span></p>`;
          }
        },
      },
      {
        headerName: 'Not interested',
        field: 'Not interested',
        filter: false,
        valueGetter: (params: any) => [params.data?.notInterestedDataCount],
        minWidth: 115,
        cellRenderer: (params: any) => {
          if (this.isActivityLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
          }
        },
      },
      {
        headerName: 'Not Reachable',
        field: 'Not Reachable',
        filter: false,
        valueGetter: (params: any) => [params.data?.notReachableDataCount],
        minWidth: 115,
        cellRenderer: (params: any) => {
          if (this.isActivityLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
          <div class="dot-elastic"></div>
        </ng-container></div>`;
          } else {
            return `<p>${params.value[0] ? params.value[0] : '--'}</p>`;
          }
        },
      },
    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getData(operation: string, event: any, leadTags?: string) {
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.date[0]
      ? 'Modified Date'
      : 'All';
    this.gridOptionsService.status = operation;
    this.gridOptionsService.leadTags = leadTags;
    this.gridOptionsService.payload = this.filtersPayload;
  }

  getMeetingCount(operation: string, event: any, meetingStatus: string) {
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = this.filtersPayload;
    this.gridOptionsService.meetingStatus = visitMeeting;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(
      new UpdateDataActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(
      new UpdateAllActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchDataReportsActivityCommunication());
  }

  currentVisibility(visibility: any, isTopLevelFilter?: boolean) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }

    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(
      new UpdateDataActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(
      new UpdateAllActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchDataReportsActivityCommunication());
    this.currOffset = 0;
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter?.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
    };

    this._store.dispatch(
      new UpdateDataActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(
      new UpdateAllActivityFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchDataReportsActivityCommunication());
    this.currOffset = 0;
    if (this.appliedFilter.users?.length || this.appliedFilter.date?.[0]) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (['date'].includes(key)) {
      this.appliedFilter[key] = null;
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  filterByDate(type?: string) {
    // let newDate = new Date();
    let date = new Date(this.currentDate.setHours(0, 0, 0, 0));

    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.date[0] = new Date(date);
        this.appliedFilter.date[1] = new Date(date);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.date[0] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        this.appliedFilter.date[1] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.date[0] = new Date(date).setDate(
          new Date(date).getDate() - 6
        );
        this.appliedFilter.date[1] = new Date(date);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.date[0] = null;
        this.appliedFilter.date[1] = null;
        break;
    }
  }

  activeDate() {
    const fromDate = new Date(this.appliedFilter.date[0]);
    const toDate = new Date(this.appliedFilter.date[1]);

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);

    if (
      fromDate.toDateString() === today.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'today';
    } else if (
      fromDate.toDateString() === yesterday.toDateString() &&
      toDate.toDateString() === yesterday.toDateString()
    ) {
      this.isDateFilter = 'yesterday';
    } else if (
      fromDate.toDateString() === sevenDaysAgo.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'sevenDays';
    } else {
      this.isDateFilter = 'custom';
    }
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      date: [new Date(this.currentDate), new Date(this.currentDate)],
    };
    this.isDateFilter = 'today';
    this.filterFunction();
  }

  resetDate() {
    this.appliedFilter = {
      ...this.appliedFilter,
      date: [new Date(this.currentDate), new Date(this.currentDate)],
    };
    this.isDateFilter = 'today';
    this.filterFunction();
  }

  exportActivityReport() {
    this._store.dispatch(new FetchDataActivityExportSuccess(''));
    // this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        path: 'datareport/activity',
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' &&
      this.reportsGraph?.isChartReady &&
      !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
