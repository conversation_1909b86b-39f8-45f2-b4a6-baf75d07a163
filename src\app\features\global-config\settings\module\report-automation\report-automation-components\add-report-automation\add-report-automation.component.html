<div class="h-100vh bg-light-pearl">
    <div class="bg-dark flex-center flex-between p-12 h-40 position-relative">
        <h5 class="text-white fw-400">{{selectedReportAutomation ? 'Edit Automation' : 'Add Automation'}}</h5>
        <span (click)="modalRef.hide()"
            class="icon ic-close ic-sm position-absolute top-7 right-8 cursor-pointer"></span>
    </div>
    <form [formGroup]="addReportAutomationForm" class="px-10">
        <div class="flex-col px-10 bg-white w-100 mt-10 scrollbar scroll-hide h-100-110">
            <div class="w-100">
                <div class="">
                    <div class="field-label-req">Report name</div>
                    <!-- displayname -->
                    <form-errors-wrapper label="Report name"
                        [control]="addReportAutomationForm.controls['displayname']">
                        <input (debounceEvent)="doesReportAutomationExist($event)" appDebounceInput type="text"
                            formControlName="displayname" placeholder="ex. Open Deals Report">
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-100">
                <div class="field-label-req">Select Report</div>
                <!-- parentReportName -->
                <form-errors-wrapper label="Report" [control]="addReportAutomationForm.controls['parentReportName']">
                    <ng-select [virtualScroll]="true" [items]="reportTypes" ResizableDropdown [closeOnSelect]="true"
                        placeholder="{{'GLOBAL.select' | translate}} report" bindLabel="key" bindValue="key"
                        (change)="onChangeReport($event)" formControlName="parentReportName" class="bg-white">
                    </ng-select>
                </form-errors-wrapper>
            </div>
            <div class="w-100" *ngIf="addReportAutomationForm.get('parentReportName').value">
                <div class="field-label-req">Select Sub Report</div>
                <!-- reportName -->
                <form-errors-wrapper label="Sub report" [control]="addReportAutomationForm.controls['reportName']">
                    <ng-select [virtualScroll]="true" [items]="subReportTypes" ResizableDropdown [closeOnSelect]="true"
                        placeholder="{{'GLOBAL.select' | translate}} report" bindLabel="key" bindValue="value"
                        formControlName="reportName" class="bg-white">
                    </ng-select>
                </form-errors-wrapper>
            </div>
            <div class="flex-column w-100">
                <div class="field-label-req">Select Report Frequency</div>
                <div class="position-relative">
                    <form-errors-wrapper label="Frequency"
                        [control]="addReportAutomationForm.controls['reportFrequency']">
                        <input type="text" formControlName="reportFrequency" id="frequency"
                            placeholder="Select frequency" (click)="openFrequencyModel()" autocomplete="off" readonly
                            [value]="getSelectedReportFrequencyDisplay()">
                    </form-errors-wrapper>
                </div>
                <!-- Report Frequency Modal -->
                <div class="position-relative">
                    <div class="position-absolute top-0 w-90pr bg-white z-index-1001 box-shadow-10"
                        *ngIf="isOpenReportFrequencyModal">
                        <div class="d-flex">
                            <div class="w-100 bg-white">
                                <!-- Report Frequency Options -->
                                <ng-container *ngFor="let type of frequencyList">
                                    <div class="form-check form-check-inline p-6 w-fit-content">
                                        <input type="radio" id="inpShowData{{type.value}}"
                                            name="selectedReportFrequency" formControlName="selectedReportFrequency"
                                            [value]="type.label" class="radio-check-input w-10 h-10"
                                            (change)="onSelectReportFrequency(type);selectFrequencyError = false"
                                            [checked]="type.label === addReportAutomationForm.get('selectedReportFrequency').value">
                                        <label class="text-dark-gray text-large ml-8"
                                            for="inpShowData{{type.value}}">{{type.label}}</label>
                                    </div>
                                </ng-container>
                                <div *ngIf="selectFrequencyError" class="text-danger text-xxs ml-10">Select Frequency
                                </div>
                                <!-- Custom Days Picker -->
                                <div *ngIf="addReportAutomationForm.get('selectedReportFrequency').value === 'Custom Days'"
                                    class="position-relative dashboard-filter form-group m-6 border"
                                    [ngClass]="{'pe-none disabled' : addReportAutomationForm.get('selectedReportFrequency').value !== 'Custom Days'}">
                                    <input formControlName="customFrequency" type="number" class="border-0"
                                        placeholder="Enter Days" name="" id="" (input)="restrictInput($event)">
                                </div>
                                <div *ngIf="isShowCustomError" class="text-danger text-xxs ml-10">Enter Custom Days
                                </div>
                            </div>
                        </div>
                        <div *ngIf="addReportAutomationForm.get('selectedReportFrequency').value === 'Custom Days' && addReportAutomationForm.get('customFrequency').value"
                            class="flex-end gap-2 p-6 border-top">
                            <h5 class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer"
                                (click)="onClickCancelCustom()">{{'BUTTONS.cancel' |translate}}
                            </h5>
                            <div class="btn-coal" (click)="reportFrequencyCloseModal()">Apply</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="w-100">
                <div class="field-label-req">Receive Reports</div>
                <!-- scheduleType Enum -->
                <div class="align-center flex-wrap">
                    <ng-container *ngFor="let type of ['Everyday','Weekly','Monthly',]">
                        <label class="form-check form-check-inline bg-light-pearl br-20 p-10 marg-r-10"
                            for="inpOtpReceiver{{type}}">
                            <input type="radio" id="inpOtpReceiver{{type}}" name="receiveReports"
                                formControlName="receiveReports" [value]="type" class="radio-check-input">
                            <div [ngClass]="addReportAutomationForm.get('receiveReports').value === type ? 'text-dark':'text-dark-gray'"
                                class="cursor-pointer text-large text-sm ml-6">
                                {{type}}</div>
                        </label>
                    </ng-container>
                </div>
            </div>
            <div class="w-100" *ngIf="addReportAutomationForm.get('receiveReports').value === 'Weekly'">
                <div class="field-label-req">Select Day</div>
                <!-- duration -->
                <form-errors-wrapper label="Day" [control]="addReportAutomationForm.controls['selectedDay']">
                    <ng-select [virtualScroll]="true" [multiple]="true" [items]="daysList" ResizableDropdown
                        [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}} day" bindLabel="name"
                        bindValue="value" formControlName="selectedDay" class="bg-white">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span><span
                                    class="text-truncate-1 break-all">{{item?.name}}</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
            </div>
            <div class="w-100" *ngIf="addReportAutomationForm.get('receiveReports').value === 'Monthly'">
                <div class="field-label-req">Select Date</div>
                <form-errors-wrapper label="Date" [control]="addReportAutomationForm.controls['selectedDate']">
                    <input type="text" (click)="toggleDropdown($event)" readonly id="selectedDate"
                        placeholder="select date" formControlName="selectedDate" />
                    <div for="selectedDate"
                        class="position-absolute align-center br-5 justify-center bg-light-slate w-24 h-24 right-10 top-8">
                        <span class="icon ic-appointment ic-sm ic-gray"></span>
                    </div>
                    <div class="w-100 p-10 box-shadow-40 br-5" *ngIf="isDropdownOpen">
                        <div class="text-center text-decoration-underline">Select date</div>
                        <div class="date-grid mt-8 w-100">
                            <div *ngFor="let date of dateRange" [class.selected]="selectedDate === date"
                                class="date-item w-26 h-26 flex-center ic-light-gray fw-600 cursor-pointer"
                                (click)="selectDate(date)">
                                {{ date }}
                            </div>
                        </div>
                    </div>
                </form-errors-wrapper>
            </div>
            <div class="w-100">
                <div class="field-label-req">Select Time</div>
                <form-errors-wrapper label="Time" [control]="addReportAutomationForm.controls['selectedTime']">
                    <input type="text" [owlDateTime]="dt3" [owlDateTimeTrigger]="dt3" readonly bsDatepicker
                        id="selectedTime" placeholder="ex. 10:00 AM" data-automate-id="selectedTime"
                        formControlName="selectedTime" />
                    <div for="selectedTime"
                        class="position-absolute align-center br-5 justify-center bg-light-slate w-24 h-24 right-10 top-8">
                        <span class="icon ic-alarm-solid ic-sm ic-gray"></span>
                    </div>
                    <owl-date-time [pickerType]="'timer'" [hour12Timer]="true" #dt3
                        (afterPickerOpen)="onPickerOpened(currentDate)">
                    </owl-date-time>
                </form-errors-wrapper>
            </div>
            <div class="w-100">
                <span class="field-label-req">Mode</span>
                <form-errors-wrapper label="Mode" [control]="addReportAutomationForm.controls['mode']">
                    <div class="d-flex flex-wrap">
                        <div *ngFor="let mode of ['WhatsApp','Email']; let i = index">
                            <div class="flex-wrap mr-10 mb-12 bg-light-pearl br-4 p-10 w-130">
                                <label class="checkbox-container align-center">
                                    <input type="checkbox"
                                        [checked]="addReportAutomationForm.get('mode').value?.includes(mode)"
                                        [value]="mode" (change)="onSelectMode($event, mode)">
                                    <span class="ms-1 checkmark"></span>
                                    <div class="cursor-pointer text-large text-sm ml-6"
                                        [ngClass]="addReportAutomationForm.get('mode').value?.includes(mode) ? 'text-dark':'text-dark-gray'">
                                        {{ mode }}
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </form-errors-wrapper>
            </div>
            <div class="w-100 mb-20">
                <div class="field-label-req">Select User</div>
                <form-errors-wrapper label="User" [control]="addReportAutomationForm.controls['user']">
                    <ng-select #userSelect [virtualScroll]="true" [items]="filteredUserList || []" ResizableDropdown
                        [ngClass]="{ 'pe-none blinking': isUserWithRoleLoading }" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}} user" [multiple]="true" bindLabel="fullName"
                        formControlName="user" groupBy="selectedAllGroup" [selectableGroup]="true"
                        [selectableGroupAsModel]="false" class="bg-white" (change)="onSelectUser($event)"
                        (search)="onUserSearch($event)" [clearSearchOnAdd]="true"
                        (clear)="onClearSearch()" (close)="onClearSearch()" (keyup)="onKeyUp($event, userSelect)">
                        <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container">
                                <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                    [checked]="item$.selected">
                                <span class="checkmark"></span>
                                <span class="text-truncate-1 break-all">Select All</span>
                            </div>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container">
                                <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                    [checked]="item$.selected">
                                <span class="checkmark"></span>
                                <div class="d-flex">
                                    <did class="flex-col">
                                        <span class="text-truncate-1 break-all">{{item?.fullName}}</span>
                                        <span class="text-xxs fw-300 text-danger">{{item?.isActive?'':'Disable'}}</span>
                                    </did>
                                    <span class="ml-10 text-sm fw-300 text-truncate-1 break-all"
                                        [title]="formatUserRoles(item?.userRoles)">({{ formatUserRoles(item?.userRoles)
                                        }})</span>
                                </div>
                            </div>
                        </ng-template>
                    </ng-select>
                </form-errors-wrapper>
                <div class="w-100 mt-10" *ngIf="addReportAutomationForm.get('user').value?.length">
                    <span class="text-dark-gray text-sm">Selected</span>
                    <div class="mb-20 d-flex flex-wrap">
                        <div *ngFor="let user of addReportAutomationForm.get('user').value | slice:0:5"
                            class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap">
                            <span class="align-center"> {{user?.fullName}}</span>
                            <span (click)="onRemoveUser(user)"
                                class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"></span>
                        </div>
                        <div *ngIf="addReportAutomationForm.get('user').value?.length > 5"
                            class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap">
                            <span class="align-center">
                                +{{ addReportAutomationForm.get('user').value.length - 5 }} more
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="w-100 flex-end p-10 gap-4 position-absolute bottom-0 bg-white">
            <h5 (click)="modalRef.hide()" class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer"
                id="btnCancelUpdateStatus" data-automate-id="btnCancelUpdateStatus">
                {{ 'BUTTONS.cancel' | translate }}
            </h5>
            <button (click)="onSubmit()" class="btn-coal mr-8 px-10 min-w-fit-content" id="btnSaveConfiguration"
                data-automate-id="btnSaveConfiguration">
                <span *ngIf="!isAddEditReportAutomation else buttonDots">Save details</span>
            </button>
        </div>
    </form>
</div>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>
<ng-template #fieldLoader>
    <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>