import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { filter, skipWhile, take, takeUntil } from 'rxjs';

import { EMPTY_GUID } from 'src/app/app.constants';
import { ContactType, LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import { LeadCallComponent } from 'src/app/features/leads/lead-call/lead-call.component';
import { LeadsEmailShareComponent } from 'src/app/features/leads/leads-email-share/leads-email-share.component';
import { LeadsTemplateShareComponent } from 'src/app/features/leads/leads-template-share/leads-template-share.component';
import { WhatsappChatComponent } from 'src/app/features/leads/whatsapp-chat/whatsapp-chat.component';
import {
  CommonClickToCall,
  FetchVNAssignment,
  FetchVirtualNos,
} from 'src/app/reducers/Integration/integration.actions';
import {
  getVNAssignment,
  getVNAssignmentIsLoading,
  getVirtualNos,
  getVirtualNosIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import { getEmailSMTPByUserId } from 'src/app/reducers/email/email-settings.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  CommunicationCount,
  CommunicationMessage,
  DeleteLeads,
  NavigateToLink,
  PermanentDeleteLeads,
  RestoreLeads,
} from 'src/app/reducers/lead/lead.actions';
import {
  getLeadsCommunication,
  getLeadsCommunicationIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { NotificationCall } from 'src/app/reducers/notification-info/notification-info.action';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getIVRSettingById,
  getUserBasicDetails,
} from 'src/app/reducers/teams/teams.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { UnassignedComponent } from 'src/app/shared/components/unassigned/unassigned.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'leads-actions',
  templateUrl: './leads-actions.component.html',
})
export class LeadsActionsComponent implements OnInit, OnDestroy, OnChanges {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('mobileNotification') mobileNotification!: TemplateRef<any>;
  @ViewChild('noEmailModal') noEmailModal!: TemplateRef<any>;
  @Input() data: any;
  @Input() isLeadPreviewOpen: boolean = false;
  @Output() changeSelection: EventEmitter<string> = new EventEmitter<string>();
  @Input() whatsAppComp: boolean = false;
  params: any;
  leadSource: string;
  userDetails: any;
  callRecordingDetails: Array<Object>;
  moment = moment;
  userPhoneNo: string;
  userPhoneNoByToken: string;
  userId: string;
  selectedVirtualNo: string;
  loader: AnimationOptions = { path: 'assets/animations/circle-loader.json' };
  userPermissions: string;
  isLoading: boolean = true;
  canEditLead: boolean = false;
  canDeleteLead: boolean = false;
  canEditNotes: boolean = false;
  canEditDocument: boolean = false;
  canCommunicate: boolean = false;
  canEditInvoice: boolean = false;
  canDeleteInvoice: boolean = false;
  ivrAccountCount: any;
  EMPTY_GUID = EMPTY_GUID;
  @Input() selectedLead: any;
  @Input() isMobileView: boolean = false;
  @Input() isCardView: boolean = false;
  userCallType: any;
  contactAdminType: string;
  selectedCallType: any;
  leadsCommunication: Object;
  isLeadsCommunicationLoading: boolean = false;
  VNAssignment: any;
  clickedLeadId: string;
  globalSettingsDetails: any;
  virtualNosList: any;
  currentPath: string;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;
  canPermanentDelete: boolean = false;
  userWhatsappType: any;
  filteredCallLogs: any;
  ivrRecordingDetails: any = [];
  fieldSections: any = [
    {
      displayName: 'Call Recording(s)',
      isHide: false,
      icon: 'ic-notes-pen',
      name: 'call',
    },
    {
      displayName: 'IVR Recording(s)',
      isHide: false,
      icon: 'ic-circle-exclamation-solid',
      name: 'ivr',
    },
  ];
  @Input() showCommunicationCount: boolean = false;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    public router: Router,
    private shareDataService: ShareDataService,
    private _notificationService: NotificationsService,
    private trackingService: TrackingService,
    public NotifyModalRef: BsModalRef
  ) {
    this.userDetails = localStorage.getItem('userDetails');
    this.userPhoneNoByToken = JSON.parse(this.userDetails)?.phone_number;
    this.userId = JSON.parse(this.userDetails)?.sub;
    this.userPermissions = localStorage.getItem('userPermissions');
  }

  ngOnInit() {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canEditLead = permissionsSet.has('Permissions.Leads.Update');
        this.canEditNotes = permissionsSet.has('Permissions.Leads.UpdateNotes');
        this.canEditDocument = permissionsSet.has(
          'Permissions.Leads.UpdateDocuments'
        );
        this.canCommunicate = permissionsSet.has(
          'Permissions.Leads.Communications'
        );
        this.canPermanentDelete = permissionsSet.has(
          'Permissions.Leads.PermanentDelete'
        );
        this.canDeleteLead = permissionsSet.has('Permissions.Leads.Delete');
        this.canEditInvoice = permissionsSet.has('Permissions.Invoice.Update');
        this.canDeleteInvoice = permissionsSet.has(
          'Permissions.Invoice.Delete'
        );
      });

    this.store
      .select(getLeadsCommunication)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        this.leadsCommunication = res;
      });

    this.store
      .select(getLeadsCommunicationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLeadsCommunicationLoading = isLoading;
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
      });

    this.store
      .select(getVNAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.VNAssignment = item;
      });

    this.store
      .select(getIVRSettingById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userCallType = data?.callThrough;
        this.userWhatsappType = data?.whatsappThrough;
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.userPhoneNo = data?.phoneNumber;
      });

    if (this.data) {
      this.params = {
        data: this.data,
      };
    }
    this.filteredCallLogs = this.params?.data?.leadCallLogs?.filter(
      (call: any) => call?.callRecordingUrl
    );
  }

  agInit(params: any): void {
    this.params = params;
    this.showCommunicationCount = params.showCommunicationCount;
    this.leadSource = LeadSource[params.data.enquiry?.leadSource];
  }

  refresh(params: any): boolean {
    this.showCommunicationCount = params.showCommunicationCount;
    return true;
  }

  ngOnChanges(changes: SimpleChanges) {
    if ('data' in changes) {
      this.params = {
        data: this.data,
      };
    }
  }

  openWhatsapp(event: any, whatsappType: TemplateRef<any>, shareType: string) {
    event.stopPropagation();
    if (this.params.data.assignTo !== EMPTY_GUID) {
      if (this.globalSettingsDetails?.isWhatsAppDeepIntegration) {
        if (
          !this.params?.data?.alternateContactNo &&
          !this.params?.data?.contactNo.includes('+')
        ) {
          this._notificationService.warn(`country code is required`);
          return;
        }
        switch (this.userWhatsappType) {
          case 0:
            this.openAskBefore(whatsappType);
            break;
          case 1:
            this.openPersonalWhatsapp(shareType);
            break;
          case 2:
            this.openIntegratedWhatsapp(shareType);
            break;
          default:
            this.openAskBefore(whatsappType);
            break;
        }
      } else {
        this.openPersonalWhatsapp(shareType);
      }
    } else {
      this.openUnassignModal();
    }
  }

  openIntegratedWhatsapp(shareType: string) {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      data: { ...this.params.data, shareType: shareType },
    };
    let whatsAppModalClass: string = '';
    if (this.params?.data?.alternateContactNo) {
      whatsAppModalClass = 'modal-400 top-modal ip-modal-unset';
    } else {
      whatsAppModalClass = 'modal-550 right-modal ip-modal-unset';
    }
    this.modalRef = this.modalService.show(
      WhatsappChatComponent,
      Object.assign({}, { class: whatsAppModalClass, initialState })
    );
  }

  openPersonalWhatsapp(shareType: string) {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      data: { ...this.params.data, shareType: shareType },
    };
    this.modalRef = this.modalService.show(
      LeadsTemplateShareComponent,
      Object.assign({}, { class: 'modal-300 right-modal', initialState })
    );
  }

  async checkToCall(
    event: any,
    virtualNo: TemplateRef<any>,
    askBefore: TemplateRef<any>,
    contactAdmin: TemplateRef<any>,
    chooseToCallType: TemplateRef<any>,
    leadId: string
  ) {
    this.clickedLeadId = leadId;
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.Call.Click`,
        this.clickedLeadId
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.Call.Click`,
        this.clickedLeadId
      );
    }
    event.stopPropagation();
    if (this.params.data.assignTo !== EMPTY_GUID) {
      if (this.globalSettingsDetails.callSettings.callType === 2) {
        switch (this.userCallType) {
          case 0:
            this.openAskBefore(askBefore);
            break;
          case 1:
            this.openIVROnly(virtualNo, contactAdmin, chooseToCallType);
            break;
          case 2:
            this.initCall();
            break;
          default:
            this.openAskBefore(askBefore);
            break;
        }
      } else {
        this.initCall();
      }
    } else {
      this.openUnassignModal();
    }
  }

  openAskBefore(askBefore: TemplateRef<any>) {
    this.modalRef = this.modalService.show(askBefore, {
      class: 'modal-350 top-modal ip-modal-unset',
    });
  }

  async openIVROnly(
    virtualNo: TemplateRef<any>,
    contactAdmin: TemplateRef<any>,
    chooseToCallType: TemplateRef<any>
  ) {
    if (this.userPermissions?.includes('IVRCall')) {
      if (this.globalSettingsDetails?.isIVROutboundEnabled) {
        if (this.globalSettingsDetails?.isVirtualNumberRequiredForOutbound) {
          this.store.dispatch(new FetchVNAssignment(this.clickedLeadId));
          await this.store
            .select(getVNAssignmentIsLoading)
            .pipe(
              skipWhile((isLoading: boolean) => {
                return isLoading;
              }),
              take(1)
            )
            .toPromise();
          if (this.VNAssignment?.isVirtualNumberAssigned) {
            this.ivrClickToCheck(
              this.VNAssignment?.virtualNumber,
              chooseToCallType
            );
          } else if (this.VNAssignment?.shouldFetchVirtualNumbers) {
            this.store.dispatch(new FetchVirtualNos());
            await this.store
              .select(getVirtualNosIsLoading)
              .pipe(
                skipWhile((isLoading: boolean) => {
                  return isLoading;
                }),
                take(1)
              )
              .toPromise();
            this.checkVirtualNumber(virtualNo);
          }
        } else {
          this.ivrClickToCheck(null, chooseToCallType);
        }
      } else {
        this.openContactAdmin(contactAdmin, 'NoPrimaryOutBound');
      }
    } else {
      this.openContactAdmin(contactAdmin, 'UserPermission');
    }
  }

  initCall() {
    if (this.params.data.alternateContactNo) {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      let initialState: any = {
        data: this.params.data,
      };
      this.modalRef = this.modalService.show(
        LeadCallComponent,
        Object.assign(
          {},
          {
            class: 'modal-400 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
    } else if (this.globalSettingsDetails.isMobileCallEnabled) {
      let payload = {
        contactNo: this.params.data.contactNo,
        userId: this.userId,
        name: this.params.data.name,
      };
      this.store.dispatch(new NotificationCall(payload));
      this.mobileNotifyInfo();
      this.onInitiateCall(1);
      this.modalRef.hide();
    } else {
      location.href = 'tel:' + this.params.data.contactNo;
      this.onInitiateCall(1);
    }
  }

  openDialerPad() {
    location.href = 'tel:' + this.params.data.contactNo;
    this.modalRef.hide();
  }

  mobileNotifyInfo() {
    let initialState: any = {
      class: 'modal-400 top-modal ph-modal-unset',
    };
    this.NotifyModalRef = this.modalService.show(
      this.mobileNotification,
      initialState
    );
    const intervalId = setInterval(() => {
      this.NotifyModalRef.hide();
      clearInterval(intervalId);
    }, 5000);
  }

  openContactAdmin(contactAdmin: TemplateRef<any>, type: string) {
    switch (type) {
      case 'UserPermission':
        this.contactAdminType =
          "You don't have permission to call through IVR, Please contact your admin";
        break;
      case 'NoPrimaryOutBound':
        this.contactAdminType =
          'No primary IVR account found in Integration, please integrate an "Outbound" account with valid token and make it primary';
        break;
      case 'NoAgents':
        this.contactAdminType =
          'No Agents are registered with your IVR Service Provider. Please contact your Admin.';
        break;
    }

    this.modalRef = this.modalService.show(contactAdmin, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
  }

  checkVirtualNumber(virtualNo: any) {
    this.store
      .select(getVirtualNos)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.virtualNosList = item;
        if (this.modalRef) {
          this.modalRef.hide();
        }
        this.modalRef = this.modalService.show(virtualNo, {
          class: 'modal-400 modal-dialog-centered tb-modal-unset',
        });
      });
  }

  ivrClickToCheck(virtualNo: string, chooseToCallType: TemplateRef<any>) {
    if (this.params.data.alternateContactNo) {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.selectedCallType = this.params.data.contactNo;
      this.modalRef = this.modalService.show(chooseToCallType, {
        class: 'modal-400 top-modal ph-modal-unset',
      });
    } else {
      this.ivrClickToCall(virtualNo, false);
    }
  }

  ivrClickToCall(virtualNo: string, alternateContactNo: boolean = false) {
    this.onInitiateCall(1);
    let payload = {
      destinationNumber: !alternateContactNo
        ? this.params.data.contactNo
        : this.selectedCallType,
      agentNumber: this.userPhoneNo || this.userPhoneNoByToken,
      callerIdOrVirtualNumber: virtualNo,
      leadId: this.params.data.id,
      userId: this.userId,
    };
    this.store.dispatch(new CommonClickToCall(payload));
    this.store.dispatch(new LoaderHide());
    this.modalRef.hide();
  }

  onInitiateCall(contactType: number) {
    const payload: any = {
      contactType: ContactType['Call'],
      leadId: this.params.data?.id,
    };
    let payloadCount = {
      id: this.params.data?.id,
      contactType: contactType,
    };
    this.store.dispatch(new CommunicationCount(payloadCount.id, payloadCount));
    this.store.dispatch(new CommunicationMessage(payload));
  }

  openAudioPlayer(event: any, aP: TemplateRef<any>) {
    event.stopPropagation();
    if (this.params?.data?.assignTo === EMPTY_GUID) {
      this.openUnassignModal();
      return;
    }

    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (
      this.params?.data?.callRecordingUrls &&
      Object.entries(this.params.data.callRecordingUrls).length > 0
    ) {
      this.ivrRecordingDetails = Object.entries(
        this.params?.data?.callRecordingUrls
      )
        .map((item: any) => {
          return {
            years: item[0],
            yearData: Object.entries(item[1])
              .reverse()
              .map((item1: any) => {
                return {
                  months: item1[0],
                  monthData: Object.keys(item1[1])
                    .reverse()
                    .map((key) => {
                      let audioDate = key.toString();
                      let audioFile = item1[1][key];
                      return { date: audioDate, audioUrl: audioFile };
                    }),
                };
              }),
          };
        })
        .reverse();
    } else {
      this.fieldSections = this.fieldSections.filter(
        (field: any) => field?.name !== 'ivr'
      );
    }
    if (
      this.params?.data?.leadCallLogs &&
      this.params?.data?.leadCallLogs?.length > 0
    ) {
      this.callRecordingDetails = this.groupByYearAndMonth(
        this.filteredCallLogs
      );
    } else {
      this.fieldSections = this.fieldSections.filter(
        (field: any) => field?.name !== 'call'
      );
    }

    this.modalRef = this.modalService.show(aP, {
      class: 'right-modal modal-350 ph-modal-unset',
    });
  }

  groupByYearAndMonth(data: any[]) {
    const groupedData: any = {};
    data.forEach((item) => {
      const date = new Date(item.callStartTime);
      const year = date.getFullYear();
      const month = date.getMonth();
      if (!groupedData[year]) {
        groupedData[year] = {};
      }
      if (!groupedData[year][month]) {
        groupedData[year][month] = [];
      }
      groupedData[year][month].push({
        ...item,
        months: date,
      });
    });

    return Object.keys(groupedData)
      .sort((a, b) => +b - +a)
      .map((year) => ({
        years: +year,
        yearData: Object.keys(groupedData[year])
          .sort((a, b) => +b - +a)
          .map((month) => ({
            months: new Date(+year, +month, 1),
            monthData: groupedData[year][month].sort((a: any, b: any) =>
              new Date(b.callStartTime).getTime() - new Date(a.callStartTime).getTime()
            ),
          })),
      }));
  }

  pauseOtherAudio(audioPlayer: any) {
    let audioElements = document.getElementsByTagName('audio');
    for (let i = 0; i < audioElements.length; i++) {
      if (audioElements[i] !== audioPlayer) {
        audioElements[i].pause();
      }
    }
  }

  openLeadHistory() {
    if (this.isLeadPreviewOpen) {
      this.changeSelection.emit('History');
      return;
    }
    const initialState: any = {
      data: this.params.data,
      selectedSection: 'History',
      closeLeadPreviewModal: () => {
        leadPreview.hide();
      },
    };
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.History.Click`,
        this.params.data.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.History.Click`,
        this.params.data?.id
      );
    }

    var leadPreview = this.modalService.show(
      LeadPreviewComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-550 ip-modal-unset', initialState }
      )
    );
  }

  openDocumentUploadModal() {
    if (this.isLeadPreviewOpen) {
      this.changeSelection.emit('Document');
      return;
    }
    const initialState: any = {
      data: this.params.data,
      selectedSection: 'Document',
      closeLeadPreviewModal: () => {
        leadPreview.hide();
      },
    };
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.Documents.Click`,
        this.params.data?.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.Documents.Click`,
        this.params.data?.id
      );
    }
    var leadPreview = this.modalService.show(
      LeadPreviewComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-550 ip-modal-unset', initialState }
      )
    );
  }

  openTemplateModal(event: any, shareType: string) {
    event.stopPropagation();
    if (this.params.data.assignTo === EMPTY_GUID) {
      this.openUnassignModal();
      return;
    }

    if (this.modalRef) {
      this.modalRef.hide();
    }

    let initialState: any = {
      data: { ...this.params.data, shareType: shareType },
    };

    let whatsAppModalClass: string = '';
    if (this.params?.data?.alternateContactNo) {
      whatsAppModalClass = 'modal-400 top-modal ip-modal-unset';
    } else {
      whatsAppModalClass = 'modal-550 right-modal ip-modal-unset';
    }

    if (shareType === 'Email') {
      if (this.isLeadPreviewOpen) {
        this.trackingService.trackFeature(
          `Web.Leads.Menu.Email.Click`,
          this.params.data.id
        );
      } else {
        this.trackingService.trackFeature(
          `Web.Leads.Button.Email.Click`,
          this.params.data.id
        );
      }
      this.trackingService.trackFeature(
        `Web.Leads.Menu.Email.Click`,
        this.params.data.id
      );
    } else if (shareType === 'WhatsApp') {
      if (this.isLeadPreviewOpen) {
        this.trackingService.trackFeature(
          `Web.Leads.Menu.WhatApp.Click`,
          this.params.data.id
        );
      } else {
        this.trackingService.trackFeature(
          `Web.Leads.Button.WhatApp.Click`,
          this.params.data.id
        );
      }
    }

    if (shareType === 'Email') {
      if (!this.params?.data?.email) {
        this.modalRef = this.modalService.show(
          this.noEmailModal,
          Object.assign({}, { class: 'modal-400 top-modal ph-modal-unset' })
        );
        return;
      }

      this.store
        .select(getEmailSMTPByUserId)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          if (data && data.length) {
            this.modalRef = this.modalService.show(
              LeadsEmailShareComponent,
              Object.assign(
                {},
                {
                  class:
                    'modal-600 modal-dialog-centered right-modal ip-modal-unset',
                  initialState,
                }
              )
            );
          } else {
            this.modalRef = this.modalService.show(
              LeadsTemplateShareComponent,
              Object.assign(
                {},
                { class: 'modal-300 right-modal', initialState }
              )
            );
          }
        });
    }
  }

  openNotesModal() {
    if (this.isLeadPreviewOpen) {
      this.changeSelection.emit('Notes');
      return;
    }
    const initialState: any = {
      data: this.params.data,
      selectedSection: 'Notes',
      closeLeadPreviewModal: () => {
        leadPreview.hide();
      },
    };
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.Notes.Click`,
        this.params.data.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.Notes.Click`,
        this.params.data?.id
      );
    }

    var leadPreview = this.modalService.show(
      LeadPreviewComponent,
      Object.assign(
        {},
        { class: 'right-modal modal-550 ip-modal-unset', initialState }
      )
    );
  }

  editLead(data: any) {
    const leadId: string = this.params.data.id;
    this.modalService.hide();
    this.router.navigate(
      [
        (location?.href?.includes('/invoice')
          ? '/invoice/edit-invoice/'
          : '/leads/edit-lead/') + leadId,
      ],
      {
        state: { leadId },
      }
    );
    this.shareDataService.setCurrentUrl(this.currentPath);
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.EditLead.Click`,
        this.params.data.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.EditLead.Click`,
        this.params.data.id
      );
    }
  }

  deleteLead(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'lead',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-350 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(
            new DeleteLeads([data?.id], this.currentPath.includes('invoice'))
          );
        }
      });
    }
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.Delete.Click`,
        data?.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.Delete.Click`,
        data?.id
      );
    }
    this.shareDataService.triggerApiCall(true);
  }

  permanentDelete(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: 'Delete lead permanently?',
        message: `You are about to delete the lead "<b>${data?.name}</b>" permanently. 🚫`,
        description:
          'Delete all information associated with this lead. Once the lead is deleted, the action is irreversible, and data recovery will not be possible.',
        title: data?.name,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new PermanentDeleteLeads([data?.id]));
        }
      });
    }
    if (this.isLeadPreviewOpen) {
      this.trackingService.trackFeature(
        `Web.Leads.Menu.PermanentDelete.Click`,
        data?.id
      );
    } else {
      this.trackingService.trackFeature(
        `Web.Leads.Button.PermanentDelete.Click`,
        data?.id
      );
    }
    this.shareDataService.triggerApiCall(true);
  }

  restoreLead(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'Restore',
      title: data?.name,
      fieldType: 'lead',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-350 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new RestoreLeads([data?.id]));
        }
      });
    }
  }

  openUnassignModal() {
    this.modalRef = this.modalService.show(UnassignedComponent, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    });
  }

  navigateToLink() {
    if (!this.params.data?.links?.[0]?.url) {
      return;
    }
    window.open(this.params.data?.links?.[0]?.url, '_blank');
    let payload: any = {
      leadId: this.params.data.id,
      linkId: this.params.data?.links?.[0]?.id,
    };
    this.store.dispatch(new NavigateToLink(payload));
  }

  closeModal() {
    this.modalRef.hide();
    this.selectedVirtualNo = null;
  }

  decodeAudioUrl(url: string): string | null {
    try {
      return decodeURIComponent(url);
    } catch {
      return url;
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
