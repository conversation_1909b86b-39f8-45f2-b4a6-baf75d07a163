import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { getProjectSubTypeDisplayName } from 'src/app/core/utils/common.util';

@Component({
  selector: 'add-new-project',
  templateUrl: './add-new-project.component.html'
})
export class AddNewProjectComponent implements OnInit, OnDestroy {
  showLeftNav: boolean = true;
  currentTab: number = 1;
  routerActive: boolean = false;
  projectId: number;
  sharedTabData: Subscription;
  stopNavigate: Subscription;
  projectSubType: any; 
  projectTypeList: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');
  navTabs: Array<{title: string, link: string}> = [
    { title: 'Basic Details', link: 'basic-details' },
    { title: 'Units Info', link: 'unit-info' },
    { title: 'Blocks Info', link: 'blocks-info' },
    { title: 'Amenities', link: 'amenities' },
    { title: 'Gallery', link: 'gallery' },
  ];

  getNavTabTitle(title: string): string {
    if (title === 'Blocks Info' && this.projectSubType === 'Plot') {
      return 'Phase';
    }
    return title;
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private headerTitle: HeaderTitleService,
    private sharedDataService: ShareDataService,
    private router: Router,
    public trackingService: TrackingService
  ) {
  }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('Add project');

    this.sharedDataService.projectId$.subscribe((data: any) => {
      this.projectId = data;
      if (this.projectId) {
        this.headerTitle.setLangTitle('Edit project');
        this.routerActive = true;
      }
      else {
        this.headerTitle.setLangTitle('Add project');
        this.routerActive = false;
      }
    });

    this.stopNavigate = this.sharedDataService.stopNavigate$.subscribe((data: any) => {
      if (data?.[0] === 'VALID' && data?.[1]) {
        this.routerActive = true;
      }
      else {
        this.routerActive = false;
      }
    });

    this.sharedTabData = this.sharedDataService.sharedTabData$.subscribe(data => {
      this.currentTab = data;
      this.cdr.detectChanges();
    });

    this.sharedDataService.projectSubType$.subscribe((subTypeId: any) => {
      this.projectSubType = getProjectSubTypeDisplayName(subTypeId);
    });
  }

  navigate(navigat: { link: string, title: string }) {
    this.trackingService.trackFeature(`Web.Project.Button.AddProject${navigat?.title?.replace(/\s+/g, '')}.Click`)
    if (!this.routerActive) {
      this.sharedDataService.validateDetail(null);
      return;
    }
   if (navigat && navigat.link) {
      this.router.navigate(['projects/edit-project/' + navigat.link + '/' + this.projectId]);
    }
  }

  ngOnDestroy() {
    this.sharedTabData.unsubscribe();
    this.stopNavigate.unsubscribe();
  }
}
