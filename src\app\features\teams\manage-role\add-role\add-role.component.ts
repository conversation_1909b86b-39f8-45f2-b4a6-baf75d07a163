import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { getAttendanceSettings } from 'src/app/reducers/attendance/attendance.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddRole,
  FetchAllExistingPermission,
  FetchRolePermissionById,
  UpdateRolePermissionById,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAllPermissions,
  getSelectedRole,
} from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'add-role',
  templateUrl: './add-role.component.html',
})
export class AddRoleComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  addRoleModalRef: BsModalRef;
  permissions: any;
  permissionsKeyList: [string, unknown][];
  selectedPermissionsList: Array<string> = [];
  roleName: string;
  roleId: any;
  addRoleForm: FormGroup;
  hides: String[] = ['MasterData', 'UserProfile', 'ExportTemplates', 'Media'];
  isActionSelected: number = 1;
  pageNumber: any;
  pageSize: any;
  search: any;
  isViewlisting: any;
  duplicateFeatureInfo: any;
  isGeoFenceEnabled: any;

  constructor(
    private modalRef: BsModalRef,
    private store: Store<AppState>,
    private formBuilder: FormBuilder
  ) {
    this.addRoleForm = this.formBuilder.group({
      name: ['', [Validators.required]],
    });
  }

  ngOnInit() {
    this.store.dispatch(new FetchAllExistingPermission());
    if (this.roleId) {
      this.store.dispatch(new FetchRolePermissionById(this.roleId));
    }

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isViewlisting = data?.shouldEnablePropertyListing;
        this.duplicateFeatureInfo = data?.duplicateFeatureInfo;
      });

    this.store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
      });

    this.store
      .select(getAllPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.permissions = data;
        const dynamicHide = this.isViewlisting ? '' : 'ListingIntegration';
        const updatedHides = [...this.hides, dynamicHide];
        this.permissionsKeyList = Object.entries(data)
          .filter((headers: any[]) => !updatedHides.includes(headers[0]))
          .sort();
      });

    if (this.roleId) {
      this.store
        .select(getSelectedRole)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.selectedPermissionsList = Object.assign(
            [],
            data?.permissions || []
          );

          this.roleName = data?.name;
          this.addRoleForm.patchValue({
            name: data?.name,
          });
        });
    }
    if (!this.roleId) {
      this.selectedPermissionsList = [];
      this.roleName = '';
    }
  }

  checkAllAction(permission: any, isChecked: boolean) {
    if (isChecked) {
      //append action
      this.appendToPermissionList(permission[0], permission[1]);
      this.checkedView(permission[0]);
    } else {
      //remove action
      this.removeFromPermissionList(permission[0], permission[1]);
    }
  }

  checkSingleActionAction(permission: any, action: any, isChecked: boolean) {
    if (isChecked) {
      //append action
      this.appendToPermissionList(permission, [action]);
      this.checkedView(permission);
    } else {
      //remove action
      this.removeFromPermissionList(permission, [action]);
      this.removeView(permission);
      if (this.selectedPermissionsList?.length === 1) {
        this.selectedPermissionsList = [];
      }
    }
  }

  checkedView(permission: any) {
    let selectedAction;
    if (
      this.selectedPermissionsList?.length > 0 &&
      !this.selectedPermissionsList.includes('Permissions.Dashboard.View')
    ) {
      selectedAction = `Permissions.Dashboard.View`;
      this.selectedPermissionsList.push(selectedAction);
    }
    if (
      !this.selectedPermissionsList.includes(
        `Permissions.${permission}.View`
      ) &&
      !(permission == 'Reports')
    ) {
      selectedAction = `Permissions.${permission}.View`;
      this.selectedPermissionsList.push(selectedAction);
    }
    if (
      this.selectedPermissionsList.includes(`Permissions.Roles.Create`) ||
      this.selectedPermissionsList.includes(`Permissions.Roles.Update`)
    ) {
      this.selectedPermissionsList.push(`Permissions.RoleClaims.View`);
      this.selectedPermissionsList.push(`Permissions.RoleClaims.Update`);
    }
    if (
      this.selectedPermissionsList.includes(`Permissions.Users.Create`) ||
      this.selectedPermissionsList.includes(`Permissions.Users.Update`)
    ) {
      this.selectedPermissionsList.push(`Permissions.UserRoles.View`);
      this.selectedPermissionsList.push(`Permissions.UserRoles.Update`);
    }
  }

  removeView(permission: any) {
    let permissionCount = 0;
    this.selectedPermissionsList.map((item: any) => {
      let selectedModule = `Permissions.${permission}`;
      if (item.includes(selectedModule)) {
        permissionCount++;
      }
    });
    let index = this.selectedPermissionsList.indexOf(
      `Permissions.${permission}.View`
    );
    if (permissionCount === 1 && index !== -1) {
      this.selectedPermissionsList.splice(index, 1);
    }
    if (
      !this.selectedPermissionsList.includes(`Permissions.Roles.Create`) &&
      !this.selectedPermissionsList.includes(`Permissions.Roles.Update`)
    ) {
      this.selectedPermissionsList = this.selectedPermissionsList.filter(
        (role) =>
          role !== `Permissions.RoleClaims.View` &&
          role !== `Permissions.RoleClaims.Update`
      );
    }
    if (
      !this.selectedPermissionsList.includes(`Permissions.Users.Create`) &&
      !this.selectedPermissionsList.includes(`Permissions.Users.Update`)
    ) {
      this.selectedPermissionsList = this.selectedPermissionsList.filter(
        (role) =>
          role !== `Permissions.UserRoles.View` &&
          role !== `Permissions.UserRoles.Update`
      );
    }
  }

  allActionSelectionCheck(permission: any) {
    return permission[1]?.every((item: any) => {
      return this.selectedPermissionsList?.includes(
        `Permissions.${permission[0]}.${item}`
      );
    });
  }

  actionSelectedCheck(permission: string, action: string) {
    let selectedAction = `Permissions.${permission}.${action}`;
    return this.selectedPermissionsList.includes(selectedAction);
  }

  appendToPermissionList(module: string, action: any) {
    action.map((item: any) => {
      let selectedAction = `Permissions.${module}.${item}`;
      if (!this.selectedPermissionsList.includes(selectedAction)) {
        this.selectedPermissionsList.push(selectedAction);
      }
    });
  }

  removeFromPermissionList(module: string, action: any) {
    // action = Object.assign([], action);
    action.map((item: any) => {
      let selectedAction = `Permissions.${module}.${item}`;
      let indexTobeRemoved =
        this.selectedPermissionsList.indexOf(selectedAction);
      this.selectedPermissionsList.splice(indexTobeRemoved, 1);
    });
    if (
      this.selectedPermissionsList?.length === 1 &&
      this.selectedPermissionsList.includes('Permissions.Dashboard.View')
    ) {
      this.selectedPermissionsList = [];
    }
  }

  addRole() {
    this.isActionSelected = this.selectedPermissionsList?.length;
    if (!this.addRoleForm.valid) {
      validateAllFormFields(this.addRoleForm);
      return;
    }
    let payload: any = {
      name: this.addRoleForm.value.name || this.roleName,
      description: this.roleName,
      permissions: this.selectedPermissionsList,
      pageNumber: this.pageNumber,
      pageSize: this.pageSize,
      Name: this.search,
    };

    if (payload.permissions.length && this.isActionSelected) {
      if (this.roleId) {
        payload = {
          roleName: this.addRoleForm.value.name || this.roleName,
          roleId: this.roleId,
          permissions: this.selectedPermissionsList,
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
          Name: this.search,
        };
        this.store.dispatch(new UpdateRolePermissionById(this.roleId, payload));
        this.modalRef.hide();
      } else {
        this.store.dispatch(new AddRole(payload));
        this.modalRef.hide();
      }
    }
  }

  renamePermission(permissionName: string) {
    if (permissionName === 'Todos') {
      return 'Tasks';
    } else if (permissionName === 'RoleClaims') {
      return 'Permissions';
    } else if (permissionName === 'Prospects') {
      return 'Data';
    } else {
      return this.convertPascalToNormal(permissionName);
    }
  }

  convertPascalToNormal(text: string): string {
    let result = text.replace(/([A-Z])/g, ' $1').trim();
    return result.replace(/Prospects/g, 'Data');
  }

  sortActions(actions: string[]): string[] {
    let filteredActions = actions;
    if (!this.isGeoFenceEnabled) {
      filteredActions = actions.filter(action => action !== 'SetGeoFence');
    }
    return filteredActions.slice().sort((a, b) => a.localeCompare(b));
  }

  getSortedPermissionsList(): [string, unknown][] {
    if (!this.permissionsKeyList) return [];

    return [...this.permissionsKeyList].sort((a, b) => {
      const displayNameA = this.renamePermission(a[0]);
      const displayNameB = this.renamePermission(b[0]);
      return displayNameA.localeCompare(displayNameB);
    });
  }

  isReadOnlyRole(): boolean {
    const roleName = this.addRoleForm.get('name')?.value;
    return (
      roleName === 'Basic' ||
      roleName === 'SalesExecutive' ||
      roleName === 'Admin' ||
      roleName === 'HR' ||
      roleName === 'Manager'
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
