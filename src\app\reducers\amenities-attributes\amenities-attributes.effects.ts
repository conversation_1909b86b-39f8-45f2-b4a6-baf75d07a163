import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { firstValueFrom, from, of } from 'rxjs';
import { catchError, map, skipWhile, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AmenitiesAttributesService } from 'src/app/services/amenities-attributes.service';
import { AddCategory, AmenitiesAttributesActionTypes, CreateAmenities, CreateAmenitiesSuccess, CreateAttributes, CreateAttributesSuccess, DeleteAmenity, DeleteAmenitySuccess, DeleteAttribute, DeleteAttributeSuccess, DoesAmenityNameExists, DoesAmenityNameExistsSuccess, DoesAttributeNameExists, DoesAttributeNameExistsSuccess, FetchAllAmenities, FetchAllAttributes, FetchAmenityById, FetchAmenityByIdSuccess, FetchAttributeById, FetchAttributeByIdSuccess, FetchCategoryList, FetchCategoryListSuccess, GetAllAmenitiesSuccess, GetAllAttributesSuccess, UpdateAmenities, UpdateAmenitiesSuccess, UpdateAttributes, UpdateAttributesSuccess } from './amenities-attributes.action';
import { handleCachedData } from 'src/app/core/utils/common.util';
import { getFetchModifiedDatesList } from '../master-data/master-data.reducer';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchModifiedDatesList } from '../master-data/master-data.actions';

@Injectable()
export class AmenitiesAttributesEffects {

  constructor(
    private actions$: Actions,
    private notificationsService: NotificationsService,
    private amenitiesAttributesService: AmenitiesAttributesService,
    private store: Store<AppState>
  ) { }

  createAmenities$ = createEffect(() => this.actions$.pipe(
    ofType(AmenitiesAttributesActionTypes.CREATE_AMENITIES),
    switchMap((action: CreateAmenities) => {
      return this.amenitiesAttributesService.createAmenities(action.payload).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Amenities created successfully');
            return new CreateAmenitiesSuccess(resp?.data),
              new FetchAllAmenities()
          }
          this.notificationsService.error('Failed to create amenities');
          return new CreateAmenitiesSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ));

  createAttributes$ = createEffect(() => this.actions$.pipe(
    ofType(AmenitiesAttributesActionTypes.CREATE_ATTRIBUTES),
    switchMap((action: CreateAttributes) => {
      return this.amenitiesAttributesService.createAttributes(action.payload).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Attribute created successfully');
            return new CreateAttributesSuccess(resp?.data), new FetchAllAttributes();
          }
          this.notificationsService.error('Failed to create amenities');
          return new CreateAttributesSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ));

  fetchAllAmenities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.FETCH_ALL_AMENITIES),
      map((action: FetchAllAmenities) => action),
      switchMap((data: any) => {
        return from(
          handleCachedData(
            'allAmenities',
            'allAmenitiesList',
            async (): Promise<any> => {
              const value: any = await firstValueFrom(
                this.store.select(getFetchModifiedDatesList).pipe(
                  skipWhile((v: any) => v.isLoading)
                )
              );
              return value?.data?.CustomMasterAmenity || null;
            },
            async () => {
              const resp: any = await firstValueFrom(
                this.amenitiesAttributesService.getAllAmenities()
              );
              if (resp?.succeeded) {
                return resp.data;
              } else {
                this.notificationsService.error('Failed to fetch all amenities');
                return [];
              }
            },
            (data: any, lastModified: string | null) => ({
              id: 'allAmenitiesList',
              items: data || [],
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || [] : data || [];
            }
          )
        ).pipe(
          map((list: any[]) => new GetAllAmenitiesSuccess(list)),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );


  fetchAllAttributes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.FETCH_ALL_ATTRIBUTES),
      map((action: FetchAllAttributes) => action),
      switchMap((data: any) => {
        return from(
          handleCachedData(
            'allAttributes',
            'allAttributesList',
            async (): Promise<any> => {
              const value: any = await firstValueFrom(
                this.store.select(getFetchModifiedDatesList).pipe(
                  skipWhile((v: any) => v.isLoading)
                )
              );
              return value?.data?.CustomMasterAttribute || null;
            },
            async () => {
              const resp: any = await firstValueFrom(
                this.amenitiesAttributesService.getAllAttributes()
              );
              if (resp?.succeeded) {
                return resp.items;
              } else {
                this.notificationsService.error('Failed to fetch all attributes');
                return [];
              }
            },
            (data: any, lastModified: string | null) => ({
              id: 'allAttributesList',
              items: data || [],
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || [] : data || [];
            }
          )
        ).pipe(
          map((list: any[]) => new GetAllAttributesSuccess(list)),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );


  updateAmenities$ = createEffect(() => this.actions$.pipe(
    ofType(AmenitiesAttributesActionTypes.UPDATE_AMENITY),
    switchMap((action: UpdateAmenities) => {
      return this.amenitiesAttributesService.updateAmenity(action.payload).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Amenity updated successfully');
            return new UpdateAmenitiesSuccess(action.payload);
          }
          this.notificationsService.error('Failed to update amenity');
          return new UpdateAmenitiesSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ))

  updateAttributes$ = createEffect(() => this.actions$.pipe(
    ofType(AmenitiesAttributesActionTypes.UPDATE_ATTRIBUTE),
    switchMap((action: UpdateAttributes) => {
      return this.amenitiesAttributesService.updateAttribute(action.payload).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Attribute updated successfully');

            return new UpdateAttributesSuccess(action.payload);
          }
          this.notificationsService.error('Failed to update attribute');
          return new UpdateAttributesSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ))

  deleteAmenity$ = createEffect(() => this.actions$.pipe(
    ofType(AmenitiesAttributesActionTypes.DELETE_AMENITIES),
    switchMap((action: DeleteAmenity) => {
      return this.amenitiesAttributesService.deleteAmenity(action.id).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Amenity deleted successfully');
            return new DeleteAmenitySuccess(action.id);
          }
          this.notificationsService.error('Failed to update attribute');
          return new DeleteAmenitySuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ))

  deleteAttribute$ = createEffect(() => this.actions$.pipe(
    ofType(AmenitiesAttributesActionTypes.DELETE_ATTRIBUTE),
    switchMap((action: DeleteAttribute) => {
      return this.amenitiesAttributesService.deleteAttribute(action.id).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Attribute deleted successfully');

            return new DeleteAttributeSuccess(action.id);
          }
          this.notificationsService.error('Failed to update attribute');
          return new DeleteAttributeSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ))

  addCategory$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.CREATE_CATEGORY),
      map((action: AddCategory) => action.category),
      switchMap((data: any) => {
        return this.amenitiesAttributesService.addCategory(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.notificationsService.success(
                `Category added successfully`
              );
              return new FetchCategoryList();
            }
            this.notificationsService.success(`Unable to add category`);
            return new FetchCategoryList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCategoryList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.FETCH_CATEGORY_LIST),
      map((action: FetchCategoryList) => action),
      switchMap((data: any) => {
        return this.amenitiesAttributesService.getCategories().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCategoryListSuccess(resp.data);
            }
            return new FetchCategoryListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  doesAmenityNameExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.DOES_AMENITY_NAME_EXISTS),
      map((action: DoesAmenityNameExists) => action.amenityName),
      switchMap((data: any) => {
        return this.amenitiesAttributesService.doesAmenityNameExists(data).pipe(
          map((resp: any) => {
            return new DoesAmenityNameExistsSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  doesAttrNameExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.DOES_ATTRIBUTE_NAME_EXISTS),
      map((action: DoesAttributeNameExists) => action.attrName),
      switchMap((data: any) => {
        return this.amenitiesAttributesService.doesAttrNameExists(data).pipe(
          map((resp: any) => {
            return new DoesAttributeNameExistsSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAmenityById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.FETCH_AMENITY_BY_ID),
      map((action: FetchAmenityById) => action.id),
      switchMap((data: any) => {
        return this.amenitiesAttributesService.getAmenityById(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAmenityByIdSuccess(resp?.data);
            }
            return new FetchAmenityByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAttributeById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AmenitiesAttributesActionTypes.FETCH_ATTRIBUTE_BY_ID),
      map((action: FetchAttributeById) => action.id),
      switchMap((data: any) => {
        return this.amenitiesAttributesService.getAttributeById(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAttributeByIdSuccess(resp?.data);
            }
            return new FetchAttributeByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );
}


