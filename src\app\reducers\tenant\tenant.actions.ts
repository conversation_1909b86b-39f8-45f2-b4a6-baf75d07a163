import { Action } from '@ngrx/store';

export enum TenantActionTypes {
  FETCH_TENANT_API_KEY = '[TENANT] Fetch Tenant API Key',
  FETCH_TENANT_API_KEY_SUCCESS = '[TENANT] Fetch Tenant API Key Success',
}

export class FetchTenantAPIKey implements Action {
  readonly type: string = TenantActionTypes.FETCH_TENANT_API_KEY;
  constructor() { }
}
export class FetchTenant<PERSON><PERSON>KeySuccess implements Action {
  readonly type: string = TenantActionTypes.FETCH_TENANT_API_KEY_SUCCESS;
  constructor(public resp: any = '') { }
}