import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { firstValueFrom, from, of, throwError } from 'rxjs';
import {
  catchError,
  concatMap,
  exhaustMap,
  map,
  mergeMap,
  skipWhile,
  switchMap,
} from 'rxjs/operators';

import { Router } from '@angular/router';
import { CloseModal, OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AddProject,
  AddProjectAmenities,
  AddProjectBlocks,
  AddUnitType,
  BulkDeleteProjectBlock,
  DeleteProject,
  DeleteProjectBlock,
  DeleteProjectUnitInfo,
  ExportProject,
  ExportProjectSuccess,
  FetchAssignedProjectsList,
  FetchAssignedProjectsListSuccess,
  FetchBlockById,
  FetchBlockByIdSuccess,
  FetchBuilderDetails,
  FetchBuilderDetailsSuccess,
  FetchExportProjectStatus,
  FetchExportProjectStatusSuccess,
  FetchMicrositeAmenities,
  FetchMicrositeAmenitiesSuccess,
  FetchMicrositeProject,
  FetchMicrositeProjectSuccess,
  FetchMicrositeUnit,
  FetchMicrositeUnitSuccess,
  FetchProjectAmenitiesByIds,
  FetchProjectAmenitiesByIdsSuccess,
  FetchProjectAssignmentDetails,
  FetchProjectAssignmentDetailsSuccess,
  FetchProjectAssignments,
  FetchProjectAssignmentsSuccess,
  FetchProjectBasicDetailsById,
  FetchProjectBasicDetailsByIdSuccess,
  FetchProjectById,
  FetchProjectByIdSuccess,
  FetchProjectCount,
  FetchProjectCountSuccess,
  FetchProjectCurrency,
  FetchProjectCurrencySuccess,
  FetchProjectDataById,
  FetchProjectDataByIdSuccess,
  FetchProjectExcelUploadedList,
  FetchProjectExcelUploadedSuccess,
  FetchProjectGalleryById,
  FetchProjectGalleryByIdSuccess,
  FetchProjectIDWithNameSuccess,
  FetchProjectIdWithName,
  FetchProjectLeadsCountByIds,
  FetchProjectLeadsCountByIdsSuccess,
  FetchProjectList,
  FetchProjectListSuccess,
  FetchProjectLocations,
  FetchProjectLocationsSuccess,
  FetchProjectUnit,
  FetchProjectUnitExcelUploadedList,
  FetchProjectUnitExcelUploadedSuccess,
  FetchProjectUnitSuccess,
  FetchProjectWithGoogleLocation,
  FetchProjectWithGoogleLocationSuccess,
  FetchUnitById,
  FetchUnitByIdSuccess,
  FetchUnitInfo,
  FetchUnitInfoById,
  FetchUnitInfoByIdSuccess,
  FetchUnitInfoSuccess,
  HasProjectName,
  HasProjectNameSuccess,
  IncreaseProjectShareCount,
  IncreaseProjectUnitShareCount,
  ProjectActionTypes,
  ProjectExcelUpload,
  ProjectExcelUploadSuccess,
  ProjectUnitExcelUpload,
  ProjectUnitExcelUploadSuccess,
  UpdateProject,
  UpdateProjectAminity,
  UpdateProjectBlock,
  UpdateProjectGallery,
  UpdateProjectToggleStatus,
  UpdateProjectUnitInfo,
  UpdateProjectsFiltersPayload,
  UpdateUnitTypeToggleStatus,
  UploadProjectMappedColumns,
  fetchCurrOffSetSuccess,
  fetchImageDropDown,
  fetchImageDropDownSuccess,
  updateAssignedProjects,
} from 'src/app/reducers/project/project.action';
import { ProjectsService } from 'src/app/services/controllers/projects.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { UploadMappedColumns } from '../lead/lead.actions';
import { getFiltersPayload } from './project.reducer';
import { handleCachedData } from 'src/app/core/utils/common.util';
import { getFetchModifiedDatesList } from '../master-data/master-data.reducer';

@Injectable()
export class ProjectEffects {
  getAssignedProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_ALL_ASSIGNED_PROJECTS_LIST),
      switchMap((action: FetchAssignedProjectsList) => {
        return this.api.getAllAssignedProjectsList(action.id).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchAssignedProjectsListSuccess(resp);
            }
            return new FetchAssignedProjectsListSuccess({});
          }),
          catchError((err) => {
            this._notificationService.error(
              `Error in fetching assigned Projects`
            );
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateAssignedProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_ASSIGNED_PROJECTS),
      switchMap((action: updateAssignedProjects) => {
        return this.api
          .updateAssignedProjects(action.id, action.projectId)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success(
                  `Projects added successfully.`
                );
                return new FetchAssignedProjectsListSuccess(resp);
              }
              return null;
            }),
            catchError((err) => {
              this._notificationService.error(`Error in adding Project`);
              return of(new OnError(err));
            })
          );
      })
    )
  );

  getProjectCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_COUNT),
      switchMap((data: FetchProjectCount) => {
        let filterPayload;
        this.store.select(getFiltersPayload).subscribe((data: any) => {
          let newData = {
            ...data,
            ProjectType: null,
            path: 'project/count',
            allAmenityList: null
          };
          filterPayload = newData;
        });
        return this.commonService.getModuleList(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectCountSuccess(resp);
            }
            return new FetchProjectCountSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_TEMP_PROJECT_LIST),
      map((action: FetchProjectList) => action),
      switchMap((data: any) => {
        this._store.dispatch(new FetchProjectCount(null, true));
        let filterPayload: any;
        this.store.select(getFiltersPayload).subscribe((data: any) => {
          filterPayload = {
            ...data,
            allAmenityList: null,
          }
        });
        return this.commonService.getModuleList(filterPayload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded && resp?.items?.length > 0) {
              const projectIds: string[] | undefined = resp?.items?.map(
                (project: any) => project?.id
              );
              if (projectIds && projectIds.length > 0) {
                const batchSize = 50;
                const numberOfBatches = Math.ceil(projectIds.length / batchSize);
                for (let i = 0; i < numberOfBatches; i++) {
                  const start = i * batchSize;
                  const end = (i + 1) * batchSize;
                  const batchIds = projectIds.slice(start, end);
                  this.store.dispatch(
                    new FetchProjectLeadsCountByIds(batchIds)
                  );
                }
              }
              return of(new FetchProjectListSuccess(resp));
            } else {
              const previousPageNumber = filterPayload?.pageNumber || 1;
              const retryPayload = {
                ...filterPayload,
                pageNumber: previousPageNumber > 1 ? previousPageNumber - 1 : 1,
              };
              this.store.dispatch(new UpdateProjectsFiltersPayload(retryPayload));
              return this.commonService.getModuleList(retryPayload).pipe(
                map((retryResp: any) => {
                  if (data?.canFetchProjectsCount) {
                    const projectIds: string[] | undefined =
                      retryResp?.items?.map((project: any) => project?.id);
                    if (projectIds && projectIds.length > 0) {
                      const batchSize = 50;
                      const numberOfBatches = Math.ceil(
                        projectIds.length / batchSize
                      );
                      for (let i = 0; i < numberOfBatches; i++) {
                        const start = i * batchSize;
                        const end = (i + 1) * batchSize;
                        const batchIds = projectIds.slice(start, end);
                        this.store.dispatch(
                          new FetchProjectLeadsCountByIds(batchIds)
                        );
                      }
                    }
                  }
                  return new FetchProjectListSuccess(retryResp);
                }),
                catchError((err) => of(new OnError(err)))
              );
            }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectLeadsCountById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_LEADS_COUNT_BY_IDS),
      mergeMap((action: FetchProjectLeadsCountByIds) => {
        let filterPayload = {
          ids: action?.ids,
          path: 'tempproject/lead-count',
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchProjectLeadsCountByIdsSuccess(resp?.data);
            }
            return new FetchProjectLeadsCountByIdsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_TEMP_PROJECT_BY_ID),
      map((action: FetchProjectById) => action),
      switchMap((action: FetchProjectById) => {
        return this.api.get(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (
                resp.succeeded &&
                (!resp.data?.unitTypes?.length ||
                  resp.data?.unitTypes?.length === 0)
              ) {
                this.sharedDataService.isProjectUnit();
              }
              return new FetchProjectByIdSuccess(resp.data);
            }
            return new FetchProjectByIdSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addProject$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.ADD_TEMP_PROJECT),
      map((action: AddProject) => action.payload),
      switchMap((action: AddProject) =>
        this.api.add(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              let projectId = resp.data;
              if (projectId !== null) {
                this.router.navigate([
                  '/projects/edit-project/unit-info/' + projectId,
                ]);
              }
              this.sharedDataService.setProjectTitleId(projectId);
              this._notificationService.success(
                `Basic detail added successfully.`
              );
              return new FetchProjectList(null, true);
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err) => {
            this._notificationService.error(`Failed to add Basic detail.`);
            return of(new OnError(err));
          })
        )
      )
    )
  );

  addUnitType$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.ADD_UNIT_TYPE),
      map((action: AddUnitType) => action),
      switchMap((action: AddUnitType) => {
        return this.api.addUnitType(action.payload).pipe(
          map((resp: any) => {
            let filtersPayload = {
              id: action.payload.projectId,
              pageNumber: action.payload.pageNumber || null,
              pageSize: action.payload.pageSize || null,
            };
            if (resp.succeeded) {
              this._notificationService.success(
                `Unit info added successfully.`
              );
              return new FetchUnitInfo(filtersPayload);
            }
            return new FetchUnitInfoSuccess(null);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addProjectBlock$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.ADD_PROJECT_BLOCKS),
      map((action: AddProjectBlocks) => action),
      switchMap((action: AddProjectBlocks) => {
        return this.api.addProjectBlocks(action.payload).pipe(
          concatMap((resp: any) => {
            let filtersPayload = {
              id: action.payload?.projectId,
              pageNumber: 1,
              pageSize: 10,
            };
            const successActions = [];
            if (resp.succeeded) {
              this._notificationService.success(
                `Block info added successfully.`
              );
              successActions.push(new FetchBlockById(filtersPayload));
              const randomFloat = Math.random();
              successActions.push(new fetchCurrOffSetSuccess(randomFloat));
            }
            return of(...successActions);
          }),
          catchError((err) => {
            return of(new OnError(err));
          })
        );
      })
    )
  );

  addProjectAmenities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.ADD_PROJECT_AMENITIES),
      map((action: AddProjectAmenities) => action.payload),
      switchMap((payload) => {
        return this.api.addProjectAmenities(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Project Amenities added successfully.`
              );
              return {
                type: '[Project API] Add Project Amenities Success',
                payload: resp,
              };
            } else {
              return {
                type: '[Project API] Add Project Amenities Failure',
                error: resp.error,
              };
            }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateProject$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_PROJECT),
      switchMap((action: UpdateProject) => {
        return this.api
          .updateBasicDetails(action.payload, `id=${action.id}`)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this.router.navigate([
                  '/projects/edit-project/unit-info/' + action.id,
                ]);
                this._notificationService.success(
                  `Basic detail updated successfully.`
                );
                return new FetchProjectList(null, true);
              }
              return new FetchProjectListSuccess();
            }),
            catchError((err) => {
              this._notificationService.error(
                `Failed to updated Basic detail.`
              );
              return of(new OnError(err));
            })
          );
      })
    )
  );

  updateProjectBlock$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_PROJECT_BLOCK),
      switchMap((action: UpdateProjectBlock) => {
        return this.api.updateProjectBlock(action.payload).pipe(
          concatMap((resp: any) => {
            let filtersPayload = {
              id: action.payload?.projectId,
              pageNumber: 1,
              pageSize: 10,
            };
            const successActions = [];
            if (resp.succeeded) {
              this._notificationService.success(
                `Block info updated successfully.`
              );
              successActions.push(new FetchBlockById(filtersPayload));
              const randomFloat = Math.random();
              successActions.push(new fetchCurrOffSetSuccess(randomFloat));
            }
            return of(...successActions);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateProjectAminity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_PROJECT_AMINITY),
      switchMap((action: UpdateProjectAminity) => {
        return this.api.updateProjectAminity(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Amenities updated successfully.`
              );
              return new FetchProjectDataById(action.payload?.projectId);
            }
            return new FetchProjectDataByIdSuccess(action.payload?.projectId);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateProjectGallery$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_PROJECT_GALLERY),
      switchMap((action: UpdateProjectGallery) => {
        return this.api.updateProjectGallery(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Gallery updated successfully.`
              );
              return new FetchProjectDataById(action.payload?.projectId);
            }
            return new FetchProjectDataByIdSuccess(action.payload?.projectId);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateProjectToggleStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_PROJECT_TOGGLE_STATUS),
      switchMap((action: UpdateProjectToggleStatus) => {
        return this.api.updateProjectToggleStatus(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Status updated successfully.`);
              return new FetchProjectList(null, true);
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateUnitTypeToggleStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_UNIT_TYPE_STATUS),
      switchMap((action: UpdateUnitTypeToggleStatus) => {
        return this.api.updateUnitTypeToggleStatus(action.payload.unitId).pipe(
          map((resp: any) => {
            let filtersPayload = {
              id: action.payload.projectId,
              pageNumber: action.payload.pageNumber,
              pageSize: action.payload.pageSize,
            };
            if (resp.succeeded) {
              this._notificationService.success(`Status updated successfully.`);
              return new FetchUnitInfo(filtersPayload);
            }
            return new FetchUnitInfo(filtersPayload);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateProjectUnitInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPDATE_PROJECT_UNIT_INFO),
      switchMap((action: UpdateProjectUnitInfo) => {
        return this.api.updateProjectUnitInfo(action.payload).pipe(
          concatMap((resp: any) => {
            let filtersPayload = {
              id: action.payload?.projectId,
              pageNumber: action.payload?.unitTypes[0]?.pageNumber,
              pageSize: action.payload?.unitTypes[0]?.pageSize,
            };
            const successActions = [];
            if (resp.succeeded) {
              this._notificationService.success(
                `Unit info updated successfully.`
              );
              successActions.push(new FetchUnitInfo(filtersPayload));
              const randomFloat = Math.random();
              successActions.push(new fetchCurrOffSetSuccess(randomFloat));
            }
            return of(...successActions);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteProject$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.DELETE_TEMP_PROJECT),
      map((action: DeleteProject) => action),
      switchMap((action: DeleteProject) => {
        return this.api.delete(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Project deleted successfully.`
              );
              this._store.dispatch(new FetchProjectCount());
              return new FetchProjectList(null, true);
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteProjectBlock$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.DELETE_PROJECT_BLOCK),
      map((action: DeleteProjectBlock) => action),
      switchMap((action: DeleteProjectBlock) => {
        return this.api.deleteBlock(action.id).pipe(
          concatMap((resp: any) => {
            let filtersPayload = {
              id: action.projId,
              pageNumber: 1,
              pageSize: 10,
            };
            const successActions = [];
            if (resp.succeeded) {
              this._notificationService.success(
                `Block info deleted successfully.`
              );
              successActions.push(new FetchBlockById(filtersPayload));
              const randomFloat = Math.random();
              successActions.push(new fetchCurrOffSetSuccess(randomFloat));
            }
            return of(...successActions);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteProjectUnitInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.DELETE_PROJECT_UNIT_INFO),
      map((action: DeleteProjectUnitInfo) => action),
      switchMap((action: DeleteProjectUnitInfo) => {
        return this.api.deleteUnitInfo(action.payload.unitId).pipe(
          map((resp: any) => {
            let filtersPayload = {
              id: action.payload.projectId,
              pageNumber: action.payload.pageNumber,
              pageSize: action.payload.pageSize,
            };
            if (resp.succeeded) {
              this._notificationService.success(
                `Unit info deleted successfully.`
              );
              return new FetchUnitInfo(filtersPayload);
            }
            return new FetchUnitInfo(filtersPayload);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLocations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_LOCATIONS),
      map((action: FetchProjectLocations) => action),
      switchMap((data: any) => {
        return this.api.getLocations().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectLocationsSuccess(resp.data);
            }
            return new FetchProjectLocationsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkDeleteProjectBlock$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.BULK_DELETE_PROJECT_BLOCK),
      map((action: BulkDeleteProjectBlock) => action),
      switchMap((action: BulkDeleteProjectBlock) => {
        return this.api.bulkDeleteProjectBlock(action?.ids).pipe(
          concatMap((resp: any) => {
            const successActions = [];
            if (resp.succeeded) {
              this._notificationService.success(
                `Block info deleted successfully.`
              );
              successActions.push(new FetchBlockById(action.payload));
              const randomFloat = Math.random();
              successActions.push(new fetchCurrOffSetSuccess(randomFloat));
            }
            return of(...successActions);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchAssignmentDetails = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_ASSIGNMENT_DETAILS),
      map((action: FetchProjectAssignmentDetails) => action),
      switchMap((action: any) => {
        return this.api.getAssignmentDetails(action.resource.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectAssignmentDetailsSuccess(
                resp.data?.assignedUserIds
              );
            }
            return new FetchProjectAssignmentDetailsSuccess([]);
          })
        );
      })
    )
  );

  getProjectDataById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_UNIT_INFO_BY_IDS),
      switchMap((action: FetchProjectDataById) =>
        this.api.getProjectDataById(action.ids).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectDataByIdSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getProjectAmenities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_AMENITIES_BY_IDS),
      switchMap((action: FetchProjectAmenitiesByIds) =>
        this.api.getProjectAmenities(action.ids).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectAmenitiesByIdsSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getUnitInfoData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_UNIT_TYPE_BY_ID),
      switchMap(
        (action: FetchUnitInfo) => {
          let filterPayload = {
            ...action.payload,
            PageNumber: action.payload?.pageNumber?.toString() || '1',
            PageSize: action.payload?.pageSize?.toString() || '10',
            ProjectId: action.payload?.id?.toString() || '',
            path: 'project/unitinfos',
          };
          return this.commonService
            .getModuleListByAdvFilter(filterPayload)
            .pipe(
              map((resp: any) => {
                if (resp?.succeeded) {
                  return new FetchUnitInfoSuccess(resp);
                }
                return new OnError(resp);
              }),
              catchError((err) => of(new OnError(err)))
            );
        }
      )
    )
  );

  getBuilderDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_BUILDER_DETAILS),
      switchMap((action: FetchBuilderDetails) =>
        this.api.getBuilderDetials().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchBuilderDetailsSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getBlockData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_BLOCK_BY_ID),
      switchMap((action: FetchBlockById) =>
        this.api.getBlockData(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchBlockByIdSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  doesProjectExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.HAS_PROJECT_NAME),
      switchMap((action: HasProjectName) => {
        return this.api.doesProjectExists(action.data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new HasProjectNameSuccess(resp.data);
            }
            return new HasProjectNameSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUnitInfoById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_UNIT_INFO_BY_ID),
      map((action: FetchUnitInfoById) => action),
      switchMap((action: FetchUnitInfoById) => {
        return this.api.getUnitInfoList(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUnitInfoByIdSuccess(resp.data);
            }
            return new FetchUnitInfoByIdSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectCurrency$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_CURRENCY_LIST),
      map((action: FetchProjectCurrency) => action),
      switchMap((data: any) => {
        return this.api.getCurrency().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectCurrencySuccess(resp.data);
            }
            return new FetchProjectCurrencySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getMicrositeProject$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_MICROSITE_PROJECT),
      switchMap((action: FetchMicrositeProject) => {
        return this.api.getMicrositeProject(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMicrositeProjectSuccess(resp.data);
            }
            return new FetchMicrositeProjectSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getMicrositeUnit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_MICROSITE_UNIT),
      switchMap((action: FetchMicrositeUnit) => {
        return this.api.getMicrositeUnit(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMicrositeUnitSuccess(resp.data);
            }
            return new FetchMicrositeUnitSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getMicrositeAmenities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_MICROSITE_AMENITIES),
      switchMap((action: FetchMicrositeAmenities) => {
        return this.api.getMicrositeAmentities(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMicrositeAmenitiesSuccess(resp.data);
            }
            return new FetchMicrositeAmenitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectUnit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_UNIT),
      switchMap((action: FetchProjectUnit) => {
        return this.api.getProjectUnit(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectUnitSuccess(resp.data);
            }
            return new FetchProjectUnitSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addBulkLeadExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.PROJECT_UNIT_EXCEL_UPLOAD),
      switchMap((action: ProjectUnitExcelUpload) => {
        return this.api.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Unit Excel uploaded Successfully'
              );
              return new ProjectUnitExcelUploadSuccess(resp.data);
            } else {
              this._store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchProjectUnit(true);
            }
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  uploadMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPLOAD_MAPPED_COLUMNS),
      switchMap((action: UploadMappedColumns) => {
        return this.api.uploadMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new ProjectUnitExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new ProjectUnitExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchProjectUnit(true);
            }
            return new FetchProjectUnitSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_EXCEL_UPLOADED_LIST),
      map((action: FetchProjectUnitExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchProjectUnitExcelUploadedSuccess(resp);
              }
              return new FetchProjectUnitExcelUploadedSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  increaseProjectShareCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.INCREASE_PROJECT_SHARE_COUNT),
      map((action: IncreaseProjectShareCount) => action),
      switchMap((action: IncreaseProjectShareCount) => {
        return this.api.increaseProjectShareCount(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Project shared successfully.`);
              return new FetchProjectList(null, true);
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGalleryDropDown$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_IMAGE_DROPDOWN),
      map((action: fetchImageDropDown) => action),
      switchMap((data: any) => {
        return this.api.getGalleryDropDown().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new fetchImageDropDownSuccess(resp.items);
            }
            return new fetchImageDropDownSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectsIDWithName$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_ID_WITH_NAME),
      map((action: FetchProjectIdWithName) => action),
      exhaustMap((data: any) =>
        from(
          handleCachedData(
            'projectIdWithName',
            'projectIdWithNameList',
            async (): Promise<any> => {
              let value: any = await firstValueFrom(this._store.select(getFetchModifiedDatesList).pipe(skipWhile((value: any) => value.isLoading)));
              return value?.data?.Project || null;
            },
            async () => {
              const resp: any = await firstValueFrom(this.api.getProjectIdsWithName());
              if (resp?.succeeded) {
                return resp.data || [];
              } else {
                this._notificationService.error('Failed to fetch project id with name');
                return [];
              }
            },
            (data: any, lastModified: any) => ({
              id: 'projectIdWithNameList',
              items: data || [],
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || [] : data || [];
            },
          )
        ).pipe(
          map((list: any[]) => new FetchProjectIDWithNameSuccess(list)),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );


  getProjectBasicDetailsById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_BASICDETAILS_BY_IDS),
      switchMap((action: FetchProjectBasicDetailsById) =>
        this.api.getProjectBasicDetailsById(action.ids).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectBasicDetailsByIdSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getProjectGalleryById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_GALLERY_BY_IDS),
      switchMap((action: FetchProjectGalleryById) =>
        this.api.getProjectGalleryById(action.ids).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectGalleryByIdSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  increaseProjectUnitShareCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.INCREASE_PROJECT_UNIT_SHARE_COUNT),
      map((action: IncreaseProjectUnitShareCount) => action),
      switchMap((action: IncreaseProjectUnitShareCount) => {
        return this.api.increaseProjectUnitShareCount(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Project shared successfully.`);
              return new FetchProjectList(null, true);
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getProjectUnitById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_UNIT_BY_IDS),
      switchMap((action: FetchUnitById) =>
        this.api.getUnitInfoById(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUnitByIdSuccess(resp);
            }
            return new OnError(resp);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getProjectExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_EXCEL_UPLOADED_LIST),
      map((action: FetchProjectExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getProjectExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchProjectExcelUploadedSuccess(resp);
              }
              return new FetchProjectExcelUploadedSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  addBulkProjectExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.PROJECT_EXCEL_UPLOAD),
      switchMap((action: ProjectExcelUpload) => {
        return this.api.uploadProjectExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Project Excel uploaded Successfully'
              );
              return new ProjectExcelUploadSuccess(resp.data);
            } else {
              this._store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchProjectList();
            }
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getExportProjectStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_EXPORT_PROJECT_STATUS),
      map((action: FetchExportProjectStatus) => action),
      switchMap((data: any) => {
        return this.api
          .getExportProjectStatus(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchExportProjectStatusSuccess(resp);
              }
              return new FetchExportProjectStatusSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  exportProject$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.EXPORT_PROJECT),
      switchMap((action: ExportProject) => {
        return this.api.exportProject(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Project are being exported in excel format`
              );
              return new ExportProjectSuccess(resp);
            }
            return new ExportProjectSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  uploadProjectMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.UPLOAD_PROJECT_MAPPED_COLUMNS),
      switchMap((action: UploadProjectMappedColumns) => {
        return this.api.uploadProjectMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new ProjectExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new ProjectExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchProjectList();
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getProjectWithGoogleLocation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_WITH_GOOGLE_LOCATION),
      switchMap((action: FetchProjectWithGoogleLocation) => {
        return this.api.getProjectWithGoogleLocation().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectWithGoogleLocationSuccess(resp.data);
            }
            return new FetchProjectWithGoogleLocationSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchPropertyAssignments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActionTypes.FETCH_PROJECT_ASSIGNMENTS),
      map((action: FetchProjectAssignments) => action),
      switchMap((action: FetchProjectAssignments) => {
        return this.api.getProjectAssignments(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectAssignmentsSuccess(resp.data);
            }
            return new FetchProjectAssignmentsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private _store: Store<AppState>,
    private actions$: Actions,
    private api: ProjectsService,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    private commonService: CommonService,
    private sharedDataService: ShareDataService,
    private router: Router
  ) { }
}
