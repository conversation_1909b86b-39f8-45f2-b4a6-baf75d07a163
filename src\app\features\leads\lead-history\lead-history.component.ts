import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit
} from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs/operators';

import { LEAD_HISTORY_CATEGORY, PAYMENT_MODES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  convertUrlsToLinks,
  getBedsDisplay,
  getBHKDisplay,
  getBRDisplay,
  getTimeZoneDate,
  groupBy
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchLeadHistoryList } from 'src/app/reducers/lead/lead.actions';
import {
  getLeadHistory,
  getLeadHistoryIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getUserBasicDetails
} from 'src/app/reducers/teams/teams.reducer';
@Component({
  selector: 'lead-history',
  templateUrl: './lead-history.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LeadHistoryComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() data: any;
  @Input() whatsAppComp: boolean = false;
  leadHistoryData: Array<{ date: Date; data: any[][][] }> = [];
  leadHistoryIsLoading: boolean = true;
  filteredHistoryList: Array<{ date: Date; data: any[][][] }> = [];
  leadHistoryCategory: Array<{ dispName: string; value: string }> = LEAD_HISTORY_CATEGORY;
  noDocument: AnimationOptions = { path: 'assets/animations/no-document.json' };
  moment = moment;
  getBHKDisplay = getBHKDisplay;
  getBRDisplay = getBRDisplay;
  getTimeZoneDate = getTimeZoneDate;
  getBedsDisplay = getBedsDisplay;
  hides = ['Meeting Location', 'Site Location', 'Assigned From User'];
  leadAppointment: Array<Object> = [];
  leadHiddenFields = [
    'LastModifiedBy',
    'LastModifiedOn',
    'LastModifiedByUser',
    'CreatedOn',
    'CreatedBy',
    'ContactType',
  ];

  leadHiddenItems: Array<any> = [];
  selectedLeadHistory: string = 'All';
  canViewLeadSource: boolean = false;
  userData: any;
  convertUrlsToLinks = convertUrlsToLinks;
  globalSettingsDetails: any;
  canViewConfidentialNotes: boolean;

  constructor(
    private _store: Store<AppState>,
    private sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.cdr.markForCheck();
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        this.cdr.markForCheck();
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canViewConfidentialNotes = permissionsSet.has('Permissions.Leads.ViewConfidentialNotes');
        if (!this.canViewConfidentialNotes) {
          this.leadHiddenItems.push('Confidential Notes');
        }
        if (permissions?.includes('Permissions.Leads.ViewLeadSource')) {
          this.canViewLeadSource = true;
        }
        this.cdr.markForCheck();
      });

    this._store
      .select(getLeadHistoryIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.leadHistoryIsLoading = isLoading;
        this.cdr.markForCheck();
      });

    this.fetchLeadHistoryData();
  }

  fetchLeadHistoryData() {
    this._store.dispatch(new FetchLeadHistoryList(this.data.id));
    this._store
      .select(getLeadHistory)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.leadHistoryData = this.groupLeadHistoryData(data);
        this.filterHistory('All');
      });
  }

  groupLeadHistoryData(data: any[]): any[] {
    const groupedData = data.reduce((acc: any, curr: any) => {
      const updatedOn = getTimeZoneDate(
        curr.updatedOn,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      );

      if (!acc[updatedOn]) {
        acc[updatedOn] = [];
      }
      acc[updatedOn].push(curr);
      return acc;
    }, {});

    return Object.entries(groupedData).map(([date, records]) => {
      const recordsWithRoundedTime = (records as any[]).map(record => ({
        ...record,
        roundedTime: record.updatedOn.split('.')[0] + 'Z'
      }));

      return {
        date,
        data: [Object.entries(groupBy(recordsWithRoundedTime, 'roundedTime'))],
      };
    });
  }

  filterHistory(type: string) {
    this.selectedLeadHistory = type;
    if (['LeadStatus', 'Notes', 'Assignment'].includes(type)) {
      this.filteredHistoryList = this.leadHistoryData
        .map(item => ({
          date: item.date,
          data: [this.filterLevel2(item.data[0], type)],
        }))
        .filter(item => item.data[0].length);
    } else {
      this.filteredHistoryList = [...this.leadHistoryData];
    }
  }
  filterLevel2(data: Array<any>, type: string) {
    let level2: any[] = [];
    let keysToDisplay: Array<string>;

    if (type === 'LeadStatus') {
      keysToDisplay = ['Lead Status', 'Reason', 'Scheduled Date'];
    } else if (type === 'Notes') {
      keysToDisplay = ['Notes'];
      if (this.canViewConfidentialNotes) {
        keysToDisplay.push('Confidential Notes');
      }
    } else if (type === 'Assignment') {
      keysToDisplay = [
        'Assigned To User',
        'Assigned On',
        'Assigned From User',
        'Secondary User',
      ];
    }

    data.forEach((singleRecord: any) => {
      let statusChangeData = singleRecord[1].filter((elem: any) =>
        keysToDisplay.includes(elem.fieldName)
      );
      if (statusChangeData.length) {
        level2.push([singleRecord[0], statusChangeData]);
      }
    });

    return level2;
  }

  getAddress = (data: any): string => {
    let address;
    try {
      address = typeof data === 'string' ? JSON.parse(data) : data;
    } catch {
      address = data;
    }
    const {
      SubLocality,
      City,
      State,
      Country,
      PostalCode
    } = address || {};
    const addressParts = [
      SubLocality,
      City,
      State,
      Country,
      PostalCode
    ].filter(part => part)
      .join(', ');
    return addressParts || 'Not available';
  };

  onViewOnMap(data: string) {
    let address = JSON.parse(data);
    window.open(
      `https://www.google.com/maps/place/${address?.Latitude},${address?.Longitude}`,
      '_blank'
    );
  }

  jsonFormat(data: any) {
    return Object?.entries(JSON.parse(data));
  }

  LeadCallFormate(data: string) {
    if (!data) return null;
    if (data.includes('->')) {
      const callParts = data.split('->').map(part => part.trim());
      if (callParts.length >= 3) {
        const result: any = {
          callType: callParts[0],
          status: callParts[1],
          duration: callParts[2].replace(', CallRecordingUrl', '').trim()
        };
        if (callParts[3]) {
          result.url = callParts[3];
        }
        return result;
      }
    }
  }

  decodeAudioUrl(url: string): string | null {
    try {
      return decodeURIComponent(url);
    } catch {
      return url;
    }
  }

  pauseOtherAudio(audioPlayer: any) {
    let audioElements = document.getElementsByTagName('audio');
    for (let i = 0; i < audioElements.length; i++) {
      if (audioElements[i] !== audioPlayer) {
        audioElements[i].pause();
      }
    }
  }

  convertAndSanitize(value: string): SafeHtml {
    const convertedText = this.convertUrlsToLinks(value);
    return this.sanitizer.bypassSecurityTrustHtml(convertedText);
  }

  bookingFormHistory(object: any) {
    let obj = typeof object === 'string' ? JSON.parse(object) : object;
    const excludeProperties = [
      'BookedBy',
      'BookedDocumentType',
      'BookedDocumentType',
      'LeadBrokerageInfoId',
      'Id',
      'SecondaryOwner',
      'TeamHead',
      'UserId',
      'Type',
      'UnitType',
      'ReferredBy',
      'FilePath',
      'ProjectsList',
      'PropertiesList',
      'Property',
      'IsBookingCompleted',
      'GSTUnit',
      'BrokerageType',
      '0',
      'DocumentName',
      'Projects',
      'BrokerageUnit',
      'CommissionUnit',
      'Currency',
      'DiscountUnit',
      'CreatedBy',
      'LastModifiedBy',
      'LastModifiedOn',
      'CreatedOn',
    ];
    const propertyNameMapping: any = {
      BookedByName: 'Booked By Name',
      AdditionalCharges: 'Additional Charges',
      AgreementValue: 'Agreement Value',
      BookedByUser: 'Booked By User',
      BookedDate: 'Booked Date',
      BookedUnderName: 'Booked Under Name',
      BrokerageCharges: 'Brokerage Charges',
      CarParkingCharges: 'Car Parking Charges',
      EarnedBrokerage: 'Brokerage Earned',
      NetBrokerageAmount: 'Net Brokerage Amount',
      ReferralName: 'Referral Name',
      RemainingAmount: 'Balance Amount',
      SecondaryOwnerName: 'Second Owner Name',
      TeamHeadName: 'Team Head Name',
      TokenAmount: 'Token Amount Paid',
      TotalBrokerage: 'Total Brokerage',
      UserName: 'User Name',
      SoldPrice: 'Sold Price',
      GST: 'GST Value',
      Commission: 'Commission',
    };
    const paymentModeMapping = PAYMENT_MODES;
    const paymentTypeMapping = [
      'Bank Loan',
      'Pending loan approval',
      'Partial loan-cash',
      'Loan applies',
      'Online transfer',
      'Cash',
      'Cheque',
      'DD',
    ];
    const discountModeMapping: any = {
      1: 'Cashback',
      2: 'Direct Adjustment',
    };
    const flattened: any = {};
    const flattenHelper = (prefix: any, obj: any) => {
      for (const key in obj) {
        if (excludeProperties.includes(key)) {
          continue;
        }
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          flattenHelper(prefix, obj[key]);
        } else {
          const value = obj[key];
          if (value !== null && value !== undefined && value !== '') {
            const propertyName = propertyNameMapping[key] || key;
            if (
              key === 'PaymentMode' &&
              value > 0 &&
              value <= paymentModeMapping.length
            ) {
              flattened[propertyName] = paymentModeMapping[value - 1];
            } else if (key === 'PaymentMode' && value === 0) {
              continue;
            } else if (
              key === 'PaymentType' &&
              value > 0 &&
              value <= paymentTypeMapping.length
            ) {
              flattened[propertyName] = paymentTypeMapping[value - 1];
            } else if (key === 'PaymentType' && value === 0) {
              continue;
            } else if (key === 'GST') {
              flattened['GST'] = `${value}%`;
            } else if (key === 'DiscountMode') {
              if (value !== 0) {
                flattened[propertyName] = discountModeMapping[value] || value;
              }
            } else if (key === 'BookedDate' || key === 'UploadedOn') {
              flattened[propertyName] = getTimeZoneDate(
                value,
                this.userData?.timeZoneInfo?.baseUTcOffset,
                'dateWithTime'
              );
            } else {
              if (
                typeof value === 'string' &&
                /(https?:\/\/|www\.)/.test(value)
              ) {
                flattened[propertyName] = convertUrlsToLinks(value);
              } else {
                flattened[propertyName] = value;
              }
            }
          }
        }
      }
    };
    flattenHelper('', obj);
    return flattened;
  }

  documentsbookingFormHistory(object: any) {
    let obj = typeof object === 'string' ? JSON.parse(object) : object;
    const excludeProperties = [
      'BookedDocumentType',
      'Type',
      'FilePath',
      'CreatedOn',
      'CreatedBy',
      'LastModifiedBy',
      'Id',
      'LastModifiedOn',
    ];
    const propertyNameMapping: any = {
      DocumentName: 'Document Name',
      UploadedOn: 'Uploaded On',
    };
    const flattened: any = {};
    const flattenHelper = (prefix: any, obj: any) => {
      for (const key in obj) {
        if (excludeProperties.includes(key)) {
          continue;
        }
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          flattenHelper(prefix, obj[key]);
        } else {
          const value = obj[key];
          if (value !== null && value !== undefined && value !== '') {
            const propertyName = propertyNameMapping[key] || key;
            if (key === 'DocumentName') {
              const fileNameParts = value.split(' ');
              flattened['Document Name'] = fileNameParts.shift();
            } else if (key === 'UploadedOn' || key === 'LastModifiedOn') {
              flattened[propertyName] = getTimeZoneDate(
                value,
                this.userData?.timeZoneInfo?.baseUTcOffset,
                'dateWithTime'
              );
            } else {
              flattened[propertyName] = value;
            }
          }
        }
      }
    };
    flattenHelper('', obj);
    return flattened;
  }

  brockerageFormHistory(object: any) {
    let obj = typeof object === 'string' ? JSON.parse(object) : object;
    const excludeProperties = [
      'ReferredBy',
      'BrokerageType',
      'GSTUnit',
      'LastModifiedBy',
      'CommissionUnit',
      'BrokerageUnit',
    ];
    const propertyNameMapping: any = {
      SoldPrice: 'Sold Price',
      AgreementValue: 'Agreement Value',
      BrokerageCharges: 'Brokerage Charges',
      NetBrokerageAmount: 'Net Brokerage Amount',
      GST: 'GST',
      TotalBrokerage: 'Total Brokerage',
      ReferralNumber: 'Referral Number',
      ReferralName: 'Referral Name',
      EarnedBrokerage: 'Earned Brokerage',
      CommissionUnit: 'Commission Unit',
      LastModifiedOn: 'Last Modified On',
    };
    const flattened: any = {};
    const flattenHelper = (prefix: any, obj: any) => {
      for (const key in obj) {
        if (excludeProperties.includes(key)) {
          continue;
        }
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          flattenHelper(prefix, obj[key]);
        } else {
          const value = obj[key];
          if (value !== null && value !== undefined && value !== '') {
            const propertyName = propertyNameMapping[key] || key;
            if (key === 'LastModifiedOn') {
              flattened[propertyName] = getTimeZoneDate(
                value,
                this.userData?.timeZoneInfo?.baseUTcOffset,
                'dateWithTime'
              );
            } else {
              flattened[propertyName] = value;
            }
          }
        }
      }
    };
    flattenHelper('', obj);
    return flattened;
  }

  retrieveMessageFromBackend(msg: any) {
    return this.sanitizer.bypassSecurityTrustHtml(msg.replace(/\n/g, '<br>'));
  }

  getChangeFlags(oldValue: string, newValue: string): string {
    const oldValuesArray = oldValue?.split(',')?.map((value) => value?.trim());
    const newValuesArray = newValue?.split(',')?.map((value) => value?.trim());
    const addedValues = newValuesArray?.filter(
      (value) => !oldValuesArray?.includes(value)
    );
    const removedValues = oldValuesArray?.filter(
      (value) => !newValuesArray?.includes(value)
    );

    if (addedValues.length > 0 && newValue) {
      return `Lead tagged as ${addedValues?.join(', ')}`;
    } else if (removedValues.length > 0 && oldValue) {
      return `Lead untagged from ${removedValues?.join(', ')}`;
    } else {
      return '';
    }
  }

  getAuditActionType(type: string): string {
    return type === 'Created' ? ' added' : ' ' + (type?.toLowerCase() || '');
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
